package com.guanghonggu;


import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.guanghonggu.controller.ComplaintController;
import com.guanghonggu.dto.ComplaintDTO;
import com.guanghonggu.dto.ResultDTO;
import com.guanghonggu.entity.Order;
import com.guanghonggu.entity.User;
import com.guanghonggu.mapper.OrderMapper;
import com.guanghonggu.mapper.UserMapper;
import com.guanghonggu.service.OrderService;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;


import java.io.*;
import java.util.*;

@SpringBootTest(classes = DiligentSisterApplication.class)
class DiligentSisterApplicationTests {




    @Autowired
    private ComplaintController complaintController;

    @Autowired
    private OrderMapper orderMapper;

    @Autowired
    private OrderService orderService;

    @Autowired
    private UserMapper userMapper;

//    @Test
//    void contextLoads() {
//
//        ComplaintDTO complaintDTO = new ComplaintDTO();
//        complaintDTO.setCaregiverId(1L);
//        complaintDTO.setComplaintDetail("测试2");
//        complaintDTO.setStatus(0);
//        complaintDTO.setComplaintType(2);
//        complaintDTO.setFeedback("测试反馈");
//        ResultDTO<Boolean> booleanResultDTO = complaintController.addComplaint(complaintDTO);
//        System.out.println(booleanResultDTO.getData() + "=========");
//
//    }
//
//
//    @Test
//    void contextLoads2() throws IOException {
//
//        List<String> list = new ArrayList<>();
//        Collections.addAll(list, "张三", "李四", "王五");
//
//        FileWriter fw = new FileWriter("D:\\test.txt", true);
//
//
//        list.stream().forEach(item -> {
//            try {
//                fw.write(item);
//                fw.write("\n");
//            } catch (IOException e) {
//                e.printStackTrace();
//            }
//        });
//
//
//        fw.close();
//
//    }
//
//
//    @Test
//    void contextLoads3() {
//
//        Order order = new Order();
//        order.setOrderId(1L);
//        order.setStatus(1);
//        order.setOrderNumber("1234567890");
//        String jsonString = JSON.toJSONString(order);
//        System.out.println("======" + jsonString);
//
//
//        Order order1 = JSON.parseObject(jsonString, Order.class);
//        System.out.println("----------" + order1);
//
//    }
//
//
//    @Test
//    void contextLoads4() throws IOException {
//
//        FileReader fr = new FileReader("D:\\test.txt");
//        StringBuilder sb = new StringBuilder();
//        int len;
//        while ((len = fr.read()) != -1) {
//            sb.append((char) len);
//        }
//
//        fr.close();
//        String[] split = sb.toString().split("\r\n");
//        System.out.println(Arrays.toString(split));
//        System.out.println(split.length);
//
//        int length = split.length;
//        System.out.println(split[length - 1]);
//
//    }
//
//
//    @Test
//    void testRedis01() {
//        redisTemplate.opsForValue().set("test", "测试");
//        redisTemplate.opsForValue().set("test", "测试2");
//
//        String res = (String) redisTemplate.opsForValue().get("test");
//
//        System.out.println(res);
//
//    }
//
//
//    @Test
//    void testRedis02() {
//
//        List<Order> order = orderMapper.selects(3L);
//
//        order.stream().forEach(item -> {
//            System.out.println(item);
//        });
//
//    }
//
//
//    @Test
//    void testRedis03() {
//
//        List<Integer> list = new ArrayList<>();
//        Collections.addAll(list, 1, 2, 3);
//
//        redisTemplate.opsForValue().set("test", list);
//
//    }
//
//
//    @Test
//    void test04() {
//
//        List<Long> list = new ArrayList<>();
//        Collections.addAll(list, 1L, 2L, 3L);
//
//        LambdaQueryWrapper<Order> wrapper = new LambdaQueryWrapper<>();
//        wrapper.in(Order::getOrderId, list);
//
//        List<Order> list1 = orderService.list(wrapper);
//        list1.stream().forEach(item -> {
//            System.out.println(item);
//        });
//
//
//    }
//
//
//    @Test
//    void test05() {
//
//        List<Long> list = new ArrayList<>();
//        Collections.addAll(list, 1L, 2L, 3L);
//
//        LambdaQueryWrapper<Order> wrapper = new LambdaQueryWrapper<>();
//        wrapper.in(Order::getOrderId, list);
//
//
//    }
//
//
//    @Test
//    void test06() {
//
//        List<Long> list = new ArrayList<>();
//        Collections.addAll(list, 1L, 2L, 3L);
//
//        LambdaQueryWrapper<Order> wrapper = new LambdaQueryWrapper<>();
//        wrapper.in(Order::getOrderId, list);
//
//
//        List<Map<String, Object>> maps = orderService.listMaps(wrapper);
//        maps.stream().forEach(item -> {
//            System.out.println(item);
//        });
//
//    }
//
//
//    @Test
//    void test07() {
//
//        Integer pageNum = 1;
//        Integer pageSize = 5;
//        Page<Order> page = new Page<>(pageNum, pageSize);
//        Page<Order> page1 = orderService.page(page);
//        LambdaQueryWrapper<Order> wrapper = new LambdaQueryWrapper<>();
//        wrapper.select(Order::getOrderId, Order::getStatus, Order::getOrderNumber);
//
//        System.out.println(page1.getTotal());
//        List<Order> records = page1.getRecords();
//
//        records.stream().forEach(item -> {
//            System.out.println(item);
//        });
//
//    }
//
//
//    @Test
//    void test08() {
//
//
//        Integer pageNum = 1;
//        Integer pageSize = 5;
//        Page<Order> page = new Page<>(pageNum, pageSize);
//
//        IPage<Order> page1 = orderMapper.selectPage(page, null);
//
//
//        System.out.println(page1.getTotal());
//        List<Order> records = page1.getRecords();
//
//        records.stream().forEach(item -> {
//            System.out.println(item);
//        });
//    }
//
//
//    @Test
//    void test09() {
//
//        User user = new User();
//        user.setPhoneNumber("18822205450");
//        user.setWecahtOpenid("acdds156");
//        user.setNickname("测试");
//
//        int insert = userMapper.insert(user);
//        Long userId = user.getUserId();
//        System.out.println(userId + "========");
//
//
//    }
//
//
//    @Test
//    void test10() {
//
//        String str = "zhangsan";
//        System.out.println(str.substring(0, 2));
//
//    }
}
