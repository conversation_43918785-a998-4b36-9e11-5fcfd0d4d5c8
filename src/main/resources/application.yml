
spring:
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: Asia/Shanghai
    default-property-inclusion: non_null
  servlet:
    multipart:
      enabled: true
      max-file-size: 500MB
      max-request-size: 2000MB
  datasource:
    url: *********************************************************************************************************************************************************
    driver-class-name: com.mysql.cj.jdbc.Driver
    username: root
    password: root
    # Druid 配置
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      initial-size: 10
      min-idle: 5
      max-active: 50
      max-wait: 10000
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: SELECT 1
      test-while-idle: true # 是否在空闲连接回收时，检查连接是否有效
      test-on-borrow: false # 指定从连接池中获取连接时，是否要执行验证查询来检查连接是否有效
      test-on-return: false # 归还连接池时，检查连接是否有效
      filters: stat
      connection-properties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
mybatis-plus:
  mapper-locations: classpath*:/com.guanghonggu.mapper/*.xml  # Mapper XML 文件路径
  typeAliasesPackage: com.guanghonggu                         # 指定实体类的包路径
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl     #开启sql打印
server:
  port: 8083

############## Sa-Token 配置 (文档: https://sa-token.cc) ##############
sa-token:
  # token 名称（同时也是 cookie 名称）
  token-name: token
  # token 有效期（单位：秒） 默认30天，-1 代表永久有效
  timeout: 2592000
  # token 最低活跃频率（单位：秒），如果 token 超过此时间没有访问系统就会被冻结，默认-1 代表不限制，永不冻结
  active-timeout: -1
  # 是否允许同一账号多地同时登录 （为 true 时允许一起登录, 为 false 时新登录挤掉旧登录）
  is-concurrent: true
  # 在多人登录同一账号时，是否共用一个 token （为 true 时所有登录共用一个 token, 为 false 时每次登录新建一个 token）
  is-share: false
  # token 风格（默认可取值：uuid、simple-uuid、random-32、random-64、random-128、tik）
  token-style: uuid
  # 是否输出操作日志
  is-log: true
aliyun:
  oss:
    endpoint: oss-cn-beijing.aliyuncs.com
    bucketName: diligent-sister
    fileHost: complaint
  accessKey:
    id: ${ALIYUN_ACCESS_KEY_ID:LTAI5tHZtvMzbLrtG59kA4Xu}  # 从环境变量ALIYUN_ACCESS_KEY_ID获取，如果没有则使用your-access-key-id
    secret: ${ALIYUN_ACCESS_KEY_SECRET:******************************}  # 从环境变量ALIYUN_ACCESS_KEY_SECRET获取，如果没有则使用your-access-key-secret
  regionId: cn-beijing
  chatbot:
    robot-id: chatbot-cn-faqM2NInba
#    region: cn-shanghai
    endpoint: chatbot.cn-shanghai.aliyuncs.com
  agentKey: fc196f5a60ae4d5d90fb268c2f6a8b8a_p_beebot_public
  dypns:
    endpoint: dypnsapi.aliyuncs.com
  sms:
    accessKeyId: LTAI5tHZtvMzbLrtG59kA4Xu
    accessKeySecret: ******************************
    signName: 天津光虹谷  # 这里填入你在阿里云申请的短信签名
    changePayPasswordTemplateCode: SMS_490700158
    LoginTemplateCode: SMS_492490140


wx:
  app:
    appId: your_app_id
    appSecret: your_app_secret
  redirectUri: https://guanghonggu.mynatapp.cc/login/wx-callback   # 微信登录回调地址
  notify-url: https://guanghonggu.mynatapp.cc/order/wechat/pay/notify           # 支付回调地址
  merchant-id: 1688498995
  private-key-path: D:/wechat/apiclient_key.pem
  public-key-path: D:/wechat/pub_key.pem
  public-key-id: PUB_KEY_ID_0116884989952025061700292367002400
  merchant-serial-number: 23CC74D75041360AD9841DFA46FD65DE50BA47DB
  api-v3-key: aB3kLp9Xq2WyZmD7TfGhJvCs8RnUeYz0
  transferNotifyUrl: https://guanghonggu.mynatapp.cc/wallet/wechat/withdraw/notify # 转账回调地址
  rechargeNotifyUrl: https://guanghonggu.mynatapp.cc/wallet/wechat/recharge/notify # 充值回调地址
  mini-programs:
    app-id: wx408023ec9b8f1c8f
    app-secret: cb2bdf05735f6a71f15d9dfa9a8d8cd3
    template-id: LFky-3iMmQEwKujS8kJUm11o5v__8wsG5iag7DPwHhw

address:
  domain: https://guanghonggu.mynatapp.cc
alipay:
  app-id: xx
  private-key: xx
  alipay-public-key: xx
  notify-url: xx
  rechargeNotifyUrl: xx
  charset: UTF-8
  sign-type: RSA2
  return-url: xx
  gateway-url: xx
amap:
  key: 8c3fd077eeb29017a98727ebc0d31c0d
jPush:
  user:
    appKey: bb4c2a9e2a7214f5bd75df2f
    masterSecret: 6ef6dd5585ed18164eba4e60
  caregiver:
    appKey: bb4c2a9e2a7214f5bd75df2f
    masterSecret: 6ef6dd5585ed18164eba4e60

wechat:
  work:
    webhook-url: https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=3093dd8d-d715-42cc-b2f4-228d2c92e902
