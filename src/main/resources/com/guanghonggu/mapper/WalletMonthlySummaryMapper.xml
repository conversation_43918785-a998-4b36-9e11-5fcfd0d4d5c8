<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.guanghonggu.mapper.WalletMonthlySummaryMapper">

    <resultMap id="BaseResultMap" type="com.guanghonggu.entity.WalletMonthlySummary">
            <id property="id" column="id" />
            <result property="userId" column="user_id" />
            <result property="userType" column="user_type" />
            <result property="year" column="year" />
            <result property="month" column="month" />
            <result property="totalIncome" column="total_income" />
            <result property="totalWithdraw" column="total_withdraw" />
            <result property="createTime" column="create_time" />
            <result property="updateTime" column="update_time" />
    </resultMap>

    <sql id="Base_Column_List">
        id,user_id,user_type,year,month,total_income,
        total_withdraw,create_time,update_time
    </sql>
    <select id="selectByUserAndMonth" resultType="com.guanghonggu.entity.WalletMonthlySummary">
        SELECT id, total_withdraw
        FROM wallet_monthly_summary
        WHERE user_id = #{userId}
                  AND user_type = #{userType}
                  AND year = #{year}
                  AND month = #{month}
            LIMIT 1
    </select>
</mapper>
