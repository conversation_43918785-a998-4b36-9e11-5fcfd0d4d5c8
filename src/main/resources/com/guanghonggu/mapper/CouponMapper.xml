<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.guanghonggu.mapper.CouponMapper">

    <resultMap id="BaseResultMap" type="com.guanghonggu.entity.Coupon">
        <id property="couponId" column="coupon_id"/>
        <result property="title" column="title"/>
        <result property="description" column="description"/>
        <result property="couponType" column="coupon_type"/>
        <result property="fullAmount" column="full_amount"/>
        <result property="discountValue" column="discount_value"/>
        <result property="maxDiscount" column="max_discount"/>
        <result property="validStartTime" column="valid_start_time"/>
        <result property="validEndTime" column="valid_end_time"/>
        <result property="quantity" column="quantity"/>
        <result property="remainingQuantity" column="remaining_quantity"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <update id="updateCouponRemainingQuantity">
        UPDATE coupon
        SET remaining_quantity = remaining_quantity - 1
        WHERE coupon_id = #{couponId}
          AND remaining_quantity > 0
    </update>
    <select id="listUserCoupon" resultType="com.guanghonggu.dto.CouponDTO">
        SELECT c.coupon_id,
               c.title,
               c.description,
               c.discount_type,
               c.full_amount,
               c.discount_value,
               c.max_discount,
               c.valid_start_time,
               c.valid_end_time,
               c.is_default_use,
               c.coupon_type,
               uc.user_coupon_id,
               uc.expiration_time,
               uc.status
        FROM user_coupon uc
                 JOIN
             coupon c
             ON uc.coupon_id = c.coupon_id
        WHERE uc.user_id = #{userId}
          AND uc.status = 1
        ORDER BY uc.acquire_time DESC
    </select>
    <select id="getCoupon" resultType="com.guanghonggu.entity.Coupon">
        SELECT coupon_id, valid_day, remaining_quantity, collect_start_time, collect_end_time, limit_count
        FROM coupon
        WHERE coupon_id = #{couponId}
          AND valid_start_time &lt;= NOW()
          AND valid_end_time >= NOW()
          AND remaining_quantity > 0
    </select>
</mapper>
