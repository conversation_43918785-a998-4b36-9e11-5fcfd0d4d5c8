<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.guanghonggu.mapper.CaregiverMapper">

    <resultMap id="BaseResultMap" type="com.guanghonggu.entity.Caregiver">
            <id property="caregiverId" column="caregiver_id" />
            <result property="phoneNumber" column="phone_number" />
            <result property="wechatOpenid" column="wechat_openid" />
            <result property="status" column="status" />
            <result property="name" column="name" />
            <result property="age" column="age" />
            <result property="caregiverAddressId" column="caregiver_address_id" />
            <result property="idCardFront" column="id_card_front" />
            <result property="idCardBack" column="id_card_back" />
            <result property="healthCertificate" column="health_certificate" />
            <result property="examStatus" column="exam_status" />
            <result property="score" column="score" />
            <result property="idCardVerification" column="id_card_verification" />
            <result property="healthCertificateVerification" column="health_certificate_verification" />
            <result property="registrationTime" column="registration_time" />
            <result property="updateTime" column="update_time" />
    </resultMap>

    <sql id="Base_Column_List">
        caregiver_id,phone_number,wechat_openid,status,name,age,
        caregiver_address_id,id_card_front,id_card_back,health_certificate,exam_status,
        score,id_card_verification,health_certificate_verification,registration_time,update_time
    </sql>

    <select id="getCityCaregiverCount" resultType="com.guanghonggu.dto.StatisticsDTO">
        SELECT
            r.region_name AS regionName,
            COUNT(c.caregiver_id) AS caregiverCount
        FROM caregiver c
                 LEFT JOIN sys_region r ON c.city_region_id = r.region_id
        WHERE c.city_region_id IS NOT NULL
        GROUP BY c.city_region_id
    </select>
</mapper>
