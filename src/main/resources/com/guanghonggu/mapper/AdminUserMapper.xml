<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.guanghonggu.mapper.AdminUserMapper">

    <resultMap id="BaseResultMap" type="com.guanghonggu.entity.AdminUser">
            <id property="id" column="id" />
            <result property="username" column="username" />
            <result property="password" column="password" />
            <result property="nickname" column="nickname" />
            <result property="status" column="status" />
            <result property="createTime" column="create_time" />
            <result property="updateTime" column="update_time" />
            <result property="accountPermission" column="account_permission" />
    </resultMap>

    <sql id="Base_Column_List">
        id,username,password,nickname,status,create_time,
        update_time,account_permission
    </sql>
</mapper>
