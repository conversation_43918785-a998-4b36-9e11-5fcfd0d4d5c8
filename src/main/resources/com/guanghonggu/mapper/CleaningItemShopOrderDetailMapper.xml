<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.guanghonggu.mapper.CleaningItemShopOrderDetailMapper">

    <resultMap id="BaseResultMap" type="com.guanghonggu.entity.CleaningItemShopOrderDetail">
            <id property="id" column="id" />
            <result property="shopOrderId" column="shop_order_id" />
            <result property="itemId" column="item_id" />
            <result property="itemName" column="item_name" />
            <result property="unitPrice" column="unit_price" />
            <result property="quantity" column="quantity" />
            <result property="totalPrice" column="total_price" />
            <result property="createTime" column="create_time" />
            <result property="updateTime" column="update_time" />
    </resultMap>

    <sql id="Base_Column_List">
        id,shop_order_id,item_id,item_name,unit_price,quantity,
        total_price,create_time,update_time
    </sql>
</mapper>
