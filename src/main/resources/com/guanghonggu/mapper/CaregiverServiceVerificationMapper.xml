<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.guanghonggu.mapper.CaregiverServiceVerificationMapper">

    <resultMap id="BaseResultMap" type="com.guanghonggu.entity.CaregiverServiceVerification">
            <id property="serviceVerificationId" column="service_verification_id" />
            <result property="caregiverId" column="caregiver_id" />
            <result property="orderId" column="order_id" />
            <result property="arrivalTime" column="arrival_time" />
            <result property="verificationTime" column="verification_time" />
            <result property="verificationPhoto" column="verification_photo" />
            <result property="status" column="status" />
            <result property="createTime" column="create_time" />
            <result property="updateTime" column="update_time" />
    </resultMap>

    <sql id="Base_Column_List">
        service_verification_id,caregiver_id,order_id,arrival_time,verification_time,verification_photo,
        status,create_time,update_time
    </sql>
</mapper>
