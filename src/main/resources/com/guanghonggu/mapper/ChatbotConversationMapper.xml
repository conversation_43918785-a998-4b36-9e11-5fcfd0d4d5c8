<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.guanghonggu.mapper.ChatbotConversationMapper">

    <resultMap id="BaseResultMap" type="com.guanghonggu.entity.ChatbotConversation">
            <id property="id" column="id" />
            <result property="userId" column="user_id" />
            <result property="sessionId" column="session_id" />
            <result property="question" column="question" />
            <result property="answer" column="answer" />
            <result property="createTime" column="create_time" />
    </resultMap>

    <sql id="Base_Column_List">
        id,user_id,session_id,question,answer,create_time
    </sql>
</mapper>
