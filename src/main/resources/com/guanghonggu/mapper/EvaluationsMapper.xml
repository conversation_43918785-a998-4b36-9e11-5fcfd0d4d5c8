<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.guanghonggu.mapper.EvaluationsMapper">

    <resultMap id="BaseResultMap" type="com.guanghonggu.entity.Evaluations">
            <id property="evaluationsId" column="evaluations_id" />
            <result property="evaluatorsId" column="evaluators_id" />
            <result property="evaluatedId" column="evaluated_id" />
            <result property="rating" column="rating" />
            <result property="evaluationContent" column="evaluation_content" />
            <result property="status" column="status" />
            <result property="createTime" column="create_time" />
            <result property="updateTime" column="update_time" />
    </resultMap>

    <sql id="Base_Column_List">
        evaluations_id,evaluators_id,evaluated_id,rating,evaluation_content,status,
        create_time,update_time
    </sql>
    <select id="getAvgScoreByCaregiverId" resultType="java.math.BigDecimal">
        SELECT ROUND(AVG((overall_evaluation + service_attitude + cleanliness_level) / 3.0), 1)
        FROM evaluations
        WHERE evaluated_id = #{caregiverId}
    </select>
</mapper>
