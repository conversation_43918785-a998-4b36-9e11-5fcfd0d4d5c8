<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.guanghonggu.mapper.ServiceVerificationImageMapper">

    <resultMap id="BaseResultMap" type="com.guanghonggu.entity.ServiceVerificationImage">
            <id property="id" column="id" />
            <result property="serviceVerificationId" column="service_verification_id" />
            <result property="imageUrl" column="image_url" />
            <result property="createTime" column="create_time" />
    </resultMap>

    <sql id="Base_Column_List">
        id,service_verification_id,image_url,create_time
    </sql>
</mapper>
