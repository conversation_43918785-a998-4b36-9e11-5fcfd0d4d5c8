<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.guanghonggu.mapper.MessageMapper">


    <select id="selectMessage" resultType="com.guanghonggu.dto.MessageDTO">
        select um.id, m.title, m.content, um.is_read, um.read_time
        from message m
        left join user_message um on m.id = um.message_id
        <where>
            um.user_id = #{userId}
            and um.user_type = #{userType}
        </where>
        order by um.create_time desc
    </select>
</mapper>