<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.guanghonggu.mapper.CaregiverAddressMapper">

    <resultMap id="BaseResultMap" type="com.guanghonggu.entity.CaregiverAddress">
            <id property="caregiverAddressId" column="caregiver_address_id" />
            <result property="provinceRegionId" column="province_region_id" />
            <result property="cityRegionId" column="city_region_id" />
            <result property="areaRegionId" column="area_region_id" />
            <result property="detailedAddress" column="detailed_address" />
            <result property="createTime" column="create_time" />
            <result property="updateTime" column="update_time" />
    </resultMap>

    <sql id="Base_Column_List">
        caregiver_address_id,province_region_id,city_region_id,area_region_id,detailed_address,create_time,
        update_time
    </sql>
</mapper>
