<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.guanghonggu.mapper.UserCouponMapper">

    <resultMap id="BaseResultMap" type="com.guanghonggu.entity.UserCoupon">
            <id property="userCouponId" column="user_coupon_id" />
            <result property="userId" column="user_id" />
            <result property="couponId" column="coupon_id" />
            <result property="orderId" column="order_id" />
            <result property="acquireTime" column="acquire_time" />
            <result property="usedTime" column="used_time" />
            <result property="expirationTime" column="expiration_time" />
            <result property="status" column="status" />
            <result property="createTime" column="create_time" />
            <result property="updateTime" column="update_time" />
    </resultMap>

    <sql id="Base_Column_List">
        user_coupon_id,user_id,coupon_id,order_id,acquire_time,used_time,
        expiration_time,status,create_time,update_time
    </sql>
    <insert id="insertBatch">
        insert into user_coupon(user_id, coupon_id, expiration_time, acquire_time, status)
        values
        <foreach collection="userCoupons" item="userCoupon" separator=",">
            (#{userCoupon.userId},
            #{userCoupon.couponId},
            #{userCoupon.expirationTime},
            #{userCoupon.acquireTime},
            #{userCoupon.status}
            )
        </foreach>
    </insert>
    <update id="updateExpireUserCouponsStatus">
        UPDATE user_coupon
        SET status = 3
        WHERE status = 1
          AND expiration_time &lt; NOW()
    </update>
    <update id="updateUsedIfUnused">
        UPDATE user_coupon
        SET status = #{status},
            used_time = now()
        WHERE user_coupon_id = #{userCouponId}
          AND status = 1
    </update>

</mapper>
