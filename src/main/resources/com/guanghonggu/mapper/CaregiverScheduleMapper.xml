<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.guanghonggu.mapper.CaregiverScheduleMapper">

    <resultMap id="BaseResultMap" type="com.guanghonggu.entity.CaregiverSchedule">
        <id property="scheduleId" column="schedule_id"/>
        <result property="caregiverId" column="caregiver_id"/>
        <result property="scheduleDate" column="schedule_date"/>
        <result property="scheduleStartTime" column="schedule_start_time"/>
        <result property="scheduleEndTime" column="schedule_end_time"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="Base_Column_List">
        schedule_id
        ,caregiver_id,schedule_date,schedule_start_time,schedule_end_time,create_time,
        update_time
    </sql>
    <select id="findCaregiversWithNoScheduleOnDate" resultType="java.lang.Long">
        SELECT caregiver_id FROM caregiver
        WHERE caregiver_id IN
        <foreach collection="caregiverIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND status = 1
        AND caregiver_id NOT IN (
        SELECT caregiver_id FROM caregiver_schedule
        WHERE schedule_date = #{appointmentDate} and schedule_type = 1
        )
    </select>

    <select id="findCaregiversWithNoTimeConflict" resultType="java.lang.Long">
        SELECT DISTINCT cs.caregiver_id
        FROM caregiver_schedule cs
        JOIN caregiver c ON cs.caregiver_id = c.caregiver_id
        WHERE c.status = 1
        AND cs.caregiver_id IN
        <foreach collection="caregiverIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND cs.caregiver_id NOT IN (
        SELECT caregiver_id
        FROM caregiver_schedule
        WHERE schedule_date = #{date}
        AND NOT (
        schedule_end_time <![CDATA[<=]]> #{appointmentStart}
        OR schedule_start_time >= #{appointmentEnd}
        )
        )
    </select>
</mapper>
