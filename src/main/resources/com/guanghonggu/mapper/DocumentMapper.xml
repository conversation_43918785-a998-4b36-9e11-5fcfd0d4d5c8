<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.guanghonggu.mapper.DocumentMapper">


    <select id="selectLists" resultType="com.guanghonggu.entity.Document">
        select bot_msg from document
        <where>
            <if test="query != null and query != ''">
                and keyword like concat('%',#{query},'%')
            </if>

        </where>
    </select>
</mapper>