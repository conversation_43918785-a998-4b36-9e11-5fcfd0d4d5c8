<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.guanghonggu.mapper.ServiceAttributeMapper">

    <resultMap id="BaseResultMap" type="com.guanghonggu.entity.ServiceAttribute">
            <id property="id" column="id" />
            <result property="serviceTypeCode" column="service_type_code" />
            <result property="attributeKey" column="attribute_key" />
            <result property="label" column="label" />
            <result property="inputType" column="input_type" />
            <result property="required" column="required" />
            <result property="sort" column="sort" />
    </resultMap>

    <sql id="Base_Column_List">
        id,service_type_code,attribute_key,label,input_type,required,
        sort
    </sql>
    <select id="selectByServiceType" resultType="com.guanghonggu.entity.ServiceAttribute">
        SELECT id, service_type_code, attribute_key, label, input_type, required, sort
        FROM service_attribute
        WHERE service_type_code = #{serviceTypeCode}
        ORDER BY sort ASC
    </select>
</mapper>
