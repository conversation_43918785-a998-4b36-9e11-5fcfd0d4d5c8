<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.guanghonggu.mapper.AddressMapper">

    <resultMap id="BaseResultMap" type="com.guanghonggu.entity.Address">
            <id property="addressId" column="address_id" />
            <result property="userId" column="user_id" />
            <result property="userType" column="user_type" />
            <result property="provinceRegionId" column="province_region_id" />
            <result property="cityRegionId" column="city_region_id" />
            <result property="areaRegionId" column="area_region_id" />
            <result property="detailedAddress" column="detailed_address" />
            <result property="isDefault" column="is_default" />
            <result property="createTime" column="create_time" />
            <result property="updateTime" column="update_time" />
    </resultMap>

    <sql id="Base_Column_List">
        address_id,user_id,user_type,province_region_id,city_region_id,area_region_id,
        detailed_address,is_default,create_time,update_time
    </sql>



</mapper>
