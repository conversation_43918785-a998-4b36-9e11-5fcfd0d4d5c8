<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.guanghonggu.mapper.AppVersionMapper">

    <resultMap id="BaseResultMap" type="com.guanghonggu.entity.AppVersion">
            <id property="id" column="id" />
            <result property="platform" column="platform" />
            <result property="appType" column="app_type" />
            <result property="versionCode" column="version_code" />
            <result property="downloadUrl" column="download_url" />
            <result property="isForceUpdate" column="is_force_update" />
            <result property="updateContent" column="update_content" />
            <result property="createTime" column="create_time" />
    </resultMap>

    <sql id="Base_Column_List">
        id,platform,app_type,version_code,download_url,is_force_update,
        update_content,create_time
    </sql>
</mapper>
