<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.guanghonggu.mapper.ComplaintTypeRelationMapper">

    <resultMap id="BaseResultMap" type="com.guanghonggu.entity.ComplaintTypeRelation">
            <id property="id" column="id" />
            <result property="complaintId" column="complaint_id" />
            <result property="typeId" column="type_id" />
    </resultMap>

    <sql id="Base_Column_List">
        id,complaint_id,type_id
    </sql>
</mapper>
