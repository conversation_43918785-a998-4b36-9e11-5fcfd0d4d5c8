<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.guanghonggu.mapper.SysRegionMapper">

    <resultMap id="BaseResultMap" type="com.guanghonggu.entity.SysRegion">
            <id property="regionId" column="region_id" />
            <result property="regionName" column="region_name" />
            <result property="regionParentId" column="region_parent_id" />
            <result property="regionLevel" column="region_level" />
    </resultMap>

    <sql id="Base_Column_List">
        region_id,region_name,region_parent_id,region_level
    </sql>
</mapper>
