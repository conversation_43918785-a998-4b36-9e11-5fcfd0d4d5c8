<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.guanghonggu.mapper.CleaningItemShopOrderMapper">

    <!-- 主订单分页查询 -->
    <select id="selectShopOrderPage" resultType="com.guanghonggu.dto.CleaningItemShopOrderDTO">
        SELECT
        o.id,
        o.order_number,
        o.total_price,
        o.payment_method,
        o.status,
        o.remark,
        o.address_id,

        -- 收货人信息
        a.user_name AS receiverName,
        a.phone_number AS receiverPhone,

        -- 拼接完整地址
        CONCAT_WS('', a.area, a.location_details, a.place_name, a.detailed_address) AS fullAddress

        FROM cleaning_item_shop_order o
        LEFT JOIN address a ON o.address_id = a.address_id

        <where>
            o.status NOT IN (4, 5)
            <if test="caregiverId != null">
                AND o.caregiver_id = #{caregiverId}
            </if>
        </where>

        ORDER BY o.create_time DESC
    </select>

    <!-- 查询每个订单的商品明细 -->
    <select id="selectDetailsByOrderIds" resultType="com.guanghonggu.dto.CleaningItemShopOrderDetailDTO">
        SELECT
        d.id,
        d.shop_order_id,
        d.item_id,
        d.item_name,
        d.unit_price,
        d.quantity,
        d.total_price
        FROM cleaning_item_shop_order_detail d
        WHERE d.shop_order_id IN
        <foreach collection="orderIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
</mapper>
