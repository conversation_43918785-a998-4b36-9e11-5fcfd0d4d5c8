<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.guanghonggu.mapper.ComplaintMapper">

    <resultMap id="BaseResultMap" type="com.guanghonggu.entity.Complaint">
            <id property="complaintId" column="complaint_id" />
            <result property="userId" column="user_id" />
            <result property="caregiverId" column="caregiver_id" />
            <result property="status" column="status" />
            <result property="complaintType" column="complaint_type" />
            <result property="complaintDetail" column="complaint_detail" />
            <result property="feedback" column="feedback" />
            <result property="createTime" column="create_time" />
            <result property="updateTime" column="update_time" />
    </resultMap>

    <sql id="Base_Column_List">
        complaint_id,user_id,caregiver_id,status,complaint_type,complaint_detail,
        feedback,create_time,update_time
    </sql>
</mapper>
