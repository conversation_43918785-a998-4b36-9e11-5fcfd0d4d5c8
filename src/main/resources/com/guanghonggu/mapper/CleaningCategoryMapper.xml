<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.guanghonggu.mapper.CleaningCategoryMapper">

    <resultMap id="BaseResultMap" type="com.guanghonggu.entity.CleaningCategory">
            <id property="id" column="id" />
            <result property="name" column="name" />
            <result property="parentId" column="parent_id" />
            <result property="level" column="level" />
            <result property="sortOrder" column="sort_order" />
            <result property="createTime" column="create_time" />
            <result property="updateTime" column="update_time" />
    </resultMap>

    <sql id="Base_Column_List">
        id,name,parent_id,level,sort_order,create_time,
        update_time
    </sql>
</mapper>
