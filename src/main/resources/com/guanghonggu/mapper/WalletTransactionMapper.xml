<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.guanghonggu.mapper.WalletTransactionMapper">

    <resultMap id="BaseResultMap" type="com.guanghonggu.entity.WalletTransaction">
            <id property="transactionId" column="transaction_id" />
            <result property="walletId" column="wallet_id" />
            <result property="transactionType" column="transaction_type" />
            <result property="amount" column="amount" />
            <result property="transactionTime" column="transaction_time" />
            <result property="status" column="status" />
            <result property="createTime" column="create_time" />
            <result property="updateTime" column="update_time" />
    </resultMap>

    <sql id="Base_Column_List">
        transaction_id,wallet_id,transaction_type,amount,transaction_time,status,
        create_time,update_time
    </sql>

    <select id="selectByOutTradeNo"
            parameterType="String"
            resultMap="BaseResultMap">
        SELECT *
        FROM wallet_transaction
        WHERE out_trade_no = #{outTradeNo}
    </select>

    <update id="updateStatusIfUnprocessed">
        UPDATE wallet_transaction
        <set>
            status = #{newStatus},
            <if test="transferBillNo != null">
                external_trade_no = #{transferBillNo},
            </if>
            transaction_time = NOW(),
            update_time = NOW()
        </set>
        WHERE transaction_id = #{transactionId} AND status = #{oldStatus}
    </update>

</mapper>
