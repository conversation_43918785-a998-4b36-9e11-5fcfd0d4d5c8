<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.guanghonggu.mapper.ChatbotQaMapper">

    <resultMap id="BaseResultMap" type="com.guanghonggu.entity.ChatbotQa">
            <id property="id" column="id" />
            <result property="question" column="question" />
            <result property="answer" column="answer" />
            <result property="createdAt" column="created_at" />
    </resultMap>

    <sql id="Base_Column_List">
        id,question,answer,created_at
    </sql>
</mapper>
