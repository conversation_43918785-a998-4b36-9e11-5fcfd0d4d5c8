<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.guanghonggu.mapper.AttributeOptionColorMapper">

    <resultMap id="BaseResultMap" type="com.guanghonggu.entity.AttributeOptionColor">
            <id property="id" column="id" />
            <result property="attributeKey" column="attribute_key" />
            <result property="colorIndex" column="color_index" />
            <result property="textColor" column="text_color" />
            <result property="areaColor" column="area_color" />
    </resultMap>

    <sql id="Base_Column_List">
        id,attribute_key,color_index,text_color,area_color
    </sql>
    <select id="selectDefaultColors" resultType="com.guanghonggu.entity.AttributeOptionColor">
        SELECT *
        FROM attribute_option_color
        WHERE attribute_key = 'default'
        ORDER BY color_index ASC
    </select>
    <select id="selectByAttributeKey" resultType="com.guanghonggu.entity.AttributeOptionColor">
        SELECT * FROM attribute_option_color ORDER BY color_index ASC
    </select>
</mapper>
