<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.guanghonggu.mapper.ServiceAttributeOptionMapper">

    <resultMap id="BaseResultMap" type="com.guanghonggu.entity.ServiceAttributeOption">
            <id property="id" column="id" />
            <result property="attributeKey" column="attribute_key" />
            <result property="value" column="value" />
            <result property="label" column="label" />
            <result property="price" column="price" />
            <result property="sort" column="sort" />
    </resultMap>

    <sql id="Base_Column_List">
        id,attribute_key,value,label,price,sort
    </sql>
    <select id="selectByAttributeKey" resultType="com.guanghonggu.entity.ServiceAttributeOption">
        SELECT id, attribute_key, `value`, label, price, is_starting_price, sort
        FROM service_attribute_option
        WHERE attribute_key = #{attributeKey}
        and service_type_code = #{serviceTypeCode}
        ORDER BY sort ASC
    </select>
</mapper>
