<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.guanghonggu.mapper.WalletMapper">

    <resultMap id="BaseResultMap" type="com.guanghonggu.entity.Wallet">
            <id property="walletId" column="wallet_id" />
            <result property="userId" column="user_id" />
            <result property="userType" column="user_type" />
            <result property="balance" column="balance" />
            <result property="totalIncome" column="total_income" />
            <result property="totalSpent" column="total_spent" />
            <result property="createTime" column="create_time" />
            <result property="updateTime" column="update_time" />
    </resultMap>

    <sql id="Base_Column_List">
        wallet_id,user_id,user_type,balance,total_income,total_spent,
        create_time,update_time
    </sql>
    <select id="selectWalletByUserForUpdate" resultType="com.guanghonggu.entity.Wallet">
        SELECT wallet_id, user_id, user_type, balance, recharge_balance, total_income, total_spent
        FROM wallet
        WHERE user_id = #{userId}
          AND user_type = #{userType}
            FOR UPDATE
    </select>
    <select id="selectWalletByIdForUpdate" resultType="com.guanghonggu.entity.Wallet">
        SELECT wallet_id, user_id, user_type, balance, recharge_balance, total_income, total_spent
        FROM wallet
        WHERE wallet_id = #{walletId}
            FOR UPDATE
    </select>
</mapper>
