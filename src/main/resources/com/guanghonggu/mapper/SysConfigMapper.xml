<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.guanghonggu.mapper.SysConfigMapper">

    <resultMap id="BaseResultMap" type="com.guanghonggu.entity.SysConfig">
            <id property="id" column="id" />
            <result property="configKey" column="config_key" />
            <result property="configValue" column="config_value" />
            <result property="description" column="description" />
            <result property="createTime" column="create_time" />
            <result property="updateTime" column="update_time" />
    </resultMap>

    <sql id="Base_Column_List">
        id,config_key,config_value,description,create_time,update_time
    </sql>
    <select id="selectValueByKey" resultType="java.lang.String">
        SELECT config_value FROM sys_config WHERE config_key = #{key} LIMIT 1
    </select>
</mapper>
