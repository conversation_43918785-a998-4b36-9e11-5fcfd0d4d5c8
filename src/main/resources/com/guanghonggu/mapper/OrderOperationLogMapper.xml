<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.guanghonggu.mapper.OrderOperationLogMapper">

    <resultMap id="BaseResultMap" type="com.guanghonggu.entity.OrderOperationLog">
            <id property="logId" column="log_id" />
            <result property="orderId" column="order_id" />
            <result property="operatorId" column="operator_id" />
            <result property="operatorType" column="operator_type" />
            <result property="operationType" column="operation_type" />
            <result property="operationDesc" column="operation_desc" />
            <result property="extraData" column="extra_data" />
            <result property="createTime" column="create_time" />
    </resultMap>

    <sql id="Base_Column_List">
        log_id,order_id,operator_id,operator_type,operation_type,operation_desc,
        extra_data,create_time
    </sql>
</mapper>
