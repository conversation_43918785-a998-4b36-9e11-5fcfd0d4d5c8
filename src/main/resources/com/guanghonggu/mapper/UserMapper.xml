<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.guanghonggu.mapper.UserMapper">

    <resultMap id="BaseResultMap" type="com.guanghonggu.entity.User">
            <id property="userId" column="user_id" />
            <result property="phoneNumber" column="phone_number" />
            <result property="wecahtOpenid" column="wecaht_openid" />
            <result property="nickname" column="nickname" />
            <result property="avatar" column="avatar" />
            <result property="status" column="status" />
            <result property="registrationTime" column="registration_time" />
            <result property="updateTime" column="update_time" />
    </resultMap>

    <sql id="Base_Column_List">
        user_id,phone_number,wecaht_openid,nickname,avatar,status,
        registration_time,update_time
    </sql>

    <select id="getCityUserCount" resultType="com.guanghonggu.dto.StatisticsDTO">
        SELECT
            r.region_name AS regionName,
            COUNT(u.user_id) AS userCount
        FROM user u
                 LEFT JOIN sys_region r ON u.city_region_id = r.region_id
        WHERE u.city_region_id IS NOT NULL
        GROUP BY u.city_region_id
    </select>

</mapper>
