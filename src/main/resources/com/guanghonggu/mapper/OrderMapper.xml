<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.guanghonggu.mapper.OrderMapper">

    <resultMap id="OrderDTOMap" type="com.guanghonggu.dto.OrderDTO">
        <id property="orderId" column="order_id" />
        <result property="orderNumber" column="order_number" />
        <result property="userId" column="user_id" />
        <result property="caregiversId" column="caregivers_id" />
        <result property="addressId" column="address_id" />
        <result property="status" column="status" />
        <result property="appointmentTime" column="appointment_time" />
        <result property="orderCompletionTime" column="order_completion_time" />
        <result property="totalPrice" column="total_price" />
        <result property="preferentialPrice" column="preferential_price" />
        <result property="actualPaymentPrice" column="actual_payment_price" />
        <result property="createTime" column="create_time" />
        <result property="updateTime" column="update_time" />
        <result property="serviceTypeId" column="service_type_id" />
        <result property="serviceTypeName" column="serviceTypeName" />
        <result property="nextServiceTime" column="next_service_time" />
    </resultMap>

    <sql id="Base_Column_List">
        order_id,order_number,user_id,caregivers_id,address_id,status,
        service_type_id,appointment_time,completion_time,total_price,preferential_price,
        actual_payment_price,create_time,update_time
    </sql>

    <select id="selectOverdueUnconfirmedOrders" resultType="java.lang.String">
        SELECT order_id FROM `order`
        WHERE status = 8 AND service_end_time <![CDATA[<=]]> #{deadline}
    </select>

    <!-- 修改这里，指定使用新的 resultMap -->
    <select id="getOrdersByUserIdPaged" resultMap="OrderDTOMap">
        SELECT
            o.*,
            orr.next_service_time,
            st.name AS serviceTypeName
        FROM `order` o
                 LEFT JOIN service_type st ON o.service_type_id = st.service_type_id
                 LEFT JOIN order_recurrence orr ON o.order_id = orr.order_id
        <where>
            o.user_id = #{userId}
            and o.status != 4
            and o.status != 7
            and o.user_is_delete = 0
        </where>
        ORDER BY o.create_time DESC
    </select>
    <select id="getOrderHallList" resultType="com.guanghonggu.dto.OrderDTO">
        SELECT
        o.order_id AS orderId,
        o.order_number AS orderNumber,
        o.service_type_id AS serviceTypeId,
        st.name AS serviceTypeName,
        o.appointment_time AS appointmentTime,
        o.caregiver_income AS caregiverIncome,
        o.estimated_time AS estimatedTime,

        -- 地址信息
        a.detailed_address AS detailedAddress,
        a.area,
        a.location_details AS locationDetails,
        a.place_name AS placeName

        FROM `order` o
        LEFT JOIN `address` a ON o.address_id = a.address_id
        LEFT JOIN `service_type` st ON o.service_type_id = st.service_type_id

        WHERE o.order_type = 2
        AND o.status = 0
        AND o.appointment_time > NOW()

        <if test="serviceTypeId != null">
            AND o.service_type_id = #{serviceTypeId}
        </if>

        <if test="area != null and area != ''">
            AND a.area LIKE CONCAT('%', #{area}, '%')
        </if>

        ORDER BY o.appointment_time ASC
    </select>
    <select id="selectOrderAgainById" resultType="com.guanghonggu.dto.OrderDTO">
        SELECT o.order_id         AS orderId,
               o.order_number     AS orderNumber,
               o.user_id          AS userId,
               o.caregivers_id    AS caregiversId,
               o.address_id       AS addressId,
               o.service_type_id  AS serviceTypeId,
               st.name            AS serviceTypeName,
               o.appointment_time AS appointmentTime,
               o.estimated_time   AS estimatedTime
        FROM `order` o
                 LEFT JOIN address a ON o.address_id = a.address_id
                 LEFT JOIN service_type st ON o.service_type_id = st.service_type_id
        WHERE o.order_id = #{orderId}
    </select>
    <select id="selectOrderDetailById" resultType="com.guanghonggu.dto.OrderDTO">
        SELECT
            o.order_id AS orderId,
            o.order_number AS orderNumber,
            o.user_id AS userId,
            o.status,
            o.caregiver_income AS caregiverIncome,
            o.service_type_id AS serviceTypeId,
            st.name AS serviceTypeName,
            o.appointment_time AS appointmentTime,
            o.caregiver_income AS caregiverIncome,
            o.order_type AS orderType,
            o.estimated_time AS estimatedTime,
            orr.service_mode,
            orr.recurrence_rule AS recurrenceRuleAsString,
            orr.recurrence_count,
            orr.total_service_count,
            orr.used_count,
            orr.service_time,
            orr.next_service_time,
            orr.last_service_time,
            -- 地址
            a.address_id AS addressId,
            a.detailed_address AS detailedAddress,
            a.area,
            a.location_details AS locationDetails,
            a.place_name AS placeName,
            a.phone_number AS phoneNumber,
            a.user_name  AS userName
        FROM `order` o
                 LEFT JOIN address a ON o.address_id = a.address_id
                 LEFT JOIN service_type st ON o.service_type_id = st.service_type_id
                 LEFT JOIN order_recurrence orr ON o.order_id = orr.order_id
        WHERE o.order_id = #{orderId}
    </select>
    <select id="selectByIdForUpdate" resultType="com.guanghonggu.entity.Order">
        SELECT user_id, order_id, status, appointment_time, estimated_time FROM `order` WHERE order_id = #{orderId} FOR UPDATE
    </select>
    <!-- 查询订单并联接查询服务类型和地址信息 -->
    <select id="getOrderById" resultType="com.guanghonggu.dto.OrderDTO">
        <![CDATA[
        SELECT
            o.order_id,
            o.order_number,
            o.user_id,
            o.caregivers_id,
            o.address_id,
            o.status,
            o.service_type_id,
            o.appointment_time,
            o.last_appointment_time,
            o.order_completion_time,
            o.service_end_time,
            o.total_price,
            o.preferential_price,
            o.caregiver_income,
            o.actual_payment_price,
            o.pay_methods,
            o.pay_time,
            o.pay_order_number,
            o.order_type,
            o.estimated_time,
            o.create_time,
            o.update_time,
            orr.service_mode,
            orr.recurrence_rule AS recurrenceRuleAsString,
            orr.recurrence_count,
            orr.total_service_count,
            orr.used_count,
            orr.service_time,
            orr.next_service_time,
            orr.last_service_time,
            st.name AS serviceTypeName,
            a.phone_number,
            a.longitude,
            a.latitude,
            a.area,
            a.detailed_address,
            oa.label,
            oa.attribute_value

        FROM
            `order` o
                LEFT JOIN
            service_type st ON o.service_type_id = st.service_type_id
                LEFT JOIN
            address a ON o.address_id = a.address_id
                LEFT JOIN
            order_attribute oa ON o.order_id = oa.order_id
                LEFT JOIN
            order_recurrence orr ON o.order_id = orr.order_id
        WHERE
            o.order_id = #{orderId}
        ]]>
    </select>
    <!-- 查询与订单关联的所有属性 -->
    <select id="getOrderAttributesByOrderId" resultType="com.guanghonggu.dto.OrderAttributeDTO">
        <![CDATA[
        SELECT
            oa.label,
            oa.attribute_value
        FROM order_attribute oa
        WHERE oa.order_id = #{orderId}
        ]]>
    </select>
    <select id="getOrdersWithAddressInfo" resultType="com.guanghonggu.dto.OrderDTO">
        SELECT
        o.order_id,
        o.order_number,
        o.user_id,
        o.caregivers_id,
        o.address_id,
        o.status,
        o.service_type_id,
        o.appointment_time,
        o.last_appointment_time,
        o.order_completion_time,
        o.service_end_time,
        o.total_price,
        o.preferential_price,
        o.actual_payment_price,
        o.caregiver_income,
        o.merchant_income,
        o.pay_methods,
        o.pay_time,
        o.pay_order_number,
        o.order_type,
        o.estimated_time,
        o.create_time,
        o.update_time,
        a.user_name,
        a.phone_number,
        a.area,
        a.location_details,
        a.detailed_address,
        a.longitude,
        a.latitude
        FROM
        `order` o
        LEFT JOIN
        address a ON o.address_id = a.address_id
        WHERE
        o.caregivers_id = #{caregiversId}
        and o.caregiver_is_delete = 0
        <if test="orderDate != null">
            AND DATE(o.appointment_time) = #{orderDate}
        </if>
        ORDER BY o.create_time DESC
        LIMIT #{page}, #{size}
    </select>
    <select id="getOrderAttibutes" resultType="com.guanghonggu.dto.OrderAttributeDTO">
        SELECT
            oa.label,
            oa.attribute_value
        FROM
            order_attribute oa
        WHERE
            oa.order_id = #{orderId}
    </select>
    <select id="listAppointmentOrdersByCaregiver" resultType="com.guanghonggu.dto.OrderDTO">
        SELECT
        o.order_id AS orderId,
        o.service_type_id AS serviceTypeId,
        st.name AS serviceTypeName,
        o.appointment_time AS appointmentTime,
        o.caregiver_income AS caregiverIncome,
        o.estimated_time AS estimatedTime,

        a.detailed_address AS detailedAddress,
        a.area,
        a.location_details AS locationDetails,
        a.place_name AS placeName

        FROM `order` o
        LEFT JOIN `address` a ON o.address_id = a.address_id
        LEFT JOIN `service_type` st ON o.service_type_id = st.service_type_id

        <where>
            o.order_type = 3
            AND o.status = 0
            <if test="caregiverId != null">
                AND o.caregivers_id = #{caregiverId}
            </if>
        </where>
        ORDER BY o.appointment_time ASC
    </select>
    <!-- 获取指定阿姨 status = 10 的所有预约订单 -->
    <select id="listAppointmentOrdersByCaregiverWithStatus" resultType="com.guanghonggu.dto.OrderDTO">
        SELECT
        o.order_id AS orderId,
        o.service_type_id AS serviceTypeId,
        st.name AS serviceTypeName,
        o.appointment_time AS appointmentTime,
        o.caregiver_income AS caregiverIncome,
        o.estimated_time AS estimatedTime,

        -- 地址信息
        a.detailed_address AS detailedAddress,
        a.area,
        a.location_details AS locationDetails,
        a.place_name AS placeName

        FROM `order` o
        LEFT JOIN `address` a ON o.address_id = a.address_id
        LEFT JOIN `service_type` st ON o.service_type_id = st.service_type_id

        <where>
            o.status = #{status}  <!-- 通过状态过滤 -->
            <if test="caregiverId != null">
                AND o.caregivers_id = #{caregiverId}
            </if>
        </where>
        ORDER BY o.appointment_time ASC
    </select>
    <select id="listCancelledOrdersByCaregiver" resultType="com.guanghonggu.dto.OrderDTO">
        SELECT
        o.order_id AS orderId,
        o.service_type_id AS serviceTypeId,
        st.name AS serviceTypeName,
        o.appointment_time AS appointmentTime,
        o.caregiver_income AS caregiverIncome,
        o.estimated_time AS estimatedTime,

        -- 地址信息
        a.detailed_address AS detailedAddress,
        a.area,
        a.location_details AS locationDetails,
        a.place_name AS placeName

        FROM `order` o
        LEFT JOIN `address` a ON o.address_id = a.address_id
        LEFT JOIN `service_type` st ON o.service_type_id = st.service_type_id

        <where>
            o.status = #{status}
            <if test="caregiverId != null">
                AND o.caregivers_id = #{caregiverId}
            </if>
            AND o.caregiver_refund_confirmation = 0
        </where>
        ORDER BY o.appointment_time ASC
    </select>

    <select id="listRejectAppointmentByUser" resultType="com.guanghonggu.dto.OrderDTO">
        SELECT
        o.order_id AS orderId,
        o.service_type_id AS serviceTypeId,
        st.name AS serviceTypeName,
        o.appointment_time AS appointmentTime,
        o.caregiver_income AS caregiverIncome,
        o.estimated_time AS estimatedTime,

        -- 地址信息
        a.detailed_address AS detailedAddress,
        a.area,
        a.location_details AS locationDetails,
        a.place_name AS placeName

        FROM `order` o
        LEFT JOIN `address` a ON o.address_id = a.address_id
        LEFT JOIN `service_type` st ON o.service_type_id = st.service_type_id

        <where>
            o.order_type = 3
            AND o.status = #{status}
            <if test="userId != null">
                AND o.user_id = #{userId}
            </if>
        </where>
        ORDER BY o.appointment_time ASC
    </select>
    <select id="countTodayOrdersForCaregivers" resultType="map">
        SELECT
        caregivers_id AS caregiverId,
        COUNT(*) AS orderCount
        FROM
        `order`
        WHERE
        caregivers_id IN
        <foreach collection="caregiverIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND appointment_time BETWEEN #{todayStart} AND #{todayEnd}
        GROUP BY caregivers_id
    </select>

</mapper>
