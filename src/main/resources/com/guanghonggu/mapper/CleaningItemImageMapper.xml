<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.guanghonggu.mapper.CleaningItemImageMapper">

    <resultMap id="BaseResultMap" type="com.guanghonggu.entity.CleaningItemImage">
            <id property="id" column="id" />
            <result property="cleaningItemId" column="cleaning_item_id" />
            <result property="imageUrl" column="image_url" />
            <result property="sortOrder" column="sort_order" />
            <result property="createTime" column="create_time" />
    </resultMap>

    <sql id="Base_Column_List">
        id,cleaning_item_id,image_url,sort_order,create_time
    </sql>
</mapper>
