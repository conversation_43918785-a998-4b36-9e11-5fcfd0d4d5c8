<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.guanghonggu.mapper.ToDoRemindersMapper">

    <resultMap id="BaseResultMap" type="com.guanghonggu.entity.ToDoReminders">
            <id property="remindersId" column="reminders_id" />
            <result property="caregiverId" column="caregiver_id" />
            <result property="orderId" column="order_id" />
            <result property="title" column="title" />
            <result property="description" column="description" />
            <result property="reminderTime" column="reminder_time" />
            <result property="status" column="status" />
            <result property="createTime" column="create_time" />
            <result property="updateTime" column="update_time" />
    </resultMap>

    <sql id="Base_Column_List">
        reminders_id,caregiver_id,order_id,title,description,reminder_time,
        status,create_time,update_time
    </sql>
</mapper>
