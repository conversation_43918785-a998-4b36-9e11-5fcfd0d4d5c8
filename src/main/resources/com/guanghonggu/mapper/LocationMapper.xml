<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.guanghonggu.mapper.LocationMapper">

    <select id="findWithinDistance" resultType="com.guanghonggu.entity.Location">
        SELECT DISTINCT l.id, l.caregiver_id, l.latitude, l.longitude
        FROM location l
                 JOIN caregiver_service_type cst ON l.caregiver_id = cst.caregiver_id
        WHERE ST_Distance_Sphere(POINT(#{lng}, #{lat}), POINT(l.longitude, l.latitude)) <![CDATA[<=]]> #{radiusKm} * 1000
          AND cst.service_type_id = #{serviceTypeId}
    </select>

</mapper>
