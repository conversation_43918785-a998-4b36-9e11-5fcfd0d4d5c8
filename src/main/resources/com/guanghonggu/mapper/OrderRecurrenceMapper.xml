<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.guanghonggu.mapper.OrderRecurrenceMapper">

    <resultMap id="BaseResultMap" type="com.guanghonggu.entity.OrderRecurrence">
            <id property="recurrenceId" column="recurrence_id" />
            <result property="userId" column="user_id" />
            <result property="caregiverId" column="caregiver_id" />
            <result property="serviceTypeId" column="service_type_id" />
            <result property="addressId" column="address_id" />
            <result property="serviceMode" column="service_mode" />
            <result property="recurrenceRule" column="recurrence_rule" />
            <result property="startTime" column="start_time" />
            <result property="endTime" column="end_time" />
            <result property="totalPrice" column="total_price" />
            <result property="preferentialPrice" column="preferential_price" />
            <result property="actualPaymentPrice" column="actual_payment_price" />
            <result property="singleServicePrice" column="single_service_price" />
            <result property="status" column="status" />
            <result property="createTime" column="create_time" />
            <result property="updateTime" column="update_time" />
    </resultMap>

    <sql id="Base_Column_List">
        recurrence_id,user_id,caregiver_id,service_type_id,address_id,service_mode,
        recurrence_rule,start_time,end_time,total_price,preferential_price,
        actual_payment_price,single_service_price,status,create_time,update_time
    </sql>
</mapper>
