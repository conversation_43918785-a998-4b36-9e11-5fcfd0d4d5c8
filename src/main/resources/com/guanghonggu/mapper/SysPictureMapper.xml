<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.guanghonggu.mapper.SysPictureMapper">

    <resultMap id="BaseResultMap" type="com.guanghonggu.entity.SysPicture">
            <id property="pictureId" column="picture_id" />
            <result property="pictureName" column="picture_name" />
            <result property="pictureType" column="picture_type" />
            <result property="downloadAddress" column="download_address" />
            <result property="jumpLink" column="jump_link" />
    </resultMap>

    <sql id="Base_Column_List">
        picture_id,picture_name,picture_type,download_address,jump_link
    </sql>
</mapper>
