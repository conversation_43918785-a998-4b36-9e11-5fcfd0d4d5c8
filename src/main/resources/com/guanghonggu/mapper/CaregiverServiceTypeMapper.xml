<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.guanghonggu.mapper.CaregiverServiceTypeMapper">

    <resultMap id="BaseResultMap" type="com.guanghonggu.entity.CaregiverServiceType">
            <id property="id" column="id" />
            <result property="caregiverId" column="caregiver_id" />
            <result property="serviceTypeId" column="service_type_id" />
            <result property="createTime" column="create_time" />
            <result property="updateTime" column="update_time" />
    </resultMap>

    <sql id="Base_Column_List">
        id,caregiver_id,service_type_id,create_time,update_time
    </sql>
</mapper>
