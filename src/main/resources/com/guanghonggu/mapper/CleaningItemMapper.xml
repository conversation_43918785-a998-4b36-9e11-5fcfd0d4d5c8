<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.guanghonggu.mapper.CleaningItemMapper">

    <resultMap id="BaseResultMap" type="com.guanghonggu.entity.CleaningItem">
            <id property="id" column="id" />
            <result property="name" column="name" />
            <result property="type" column="type" />
            <result property="categoryId" column="category_id" />
            <result property="material" column="material" />
            <result property="applicableScope" column="applicable_scope" />
            <result property="remark" column="remark" />
            <result property="createTime" column="create_time" />
            <result property="updateTime" column="update_time" />
    </resultMap>

    <select id="listByCategoryId" resultType="com.guanghonggu.dto.CleaningItemDTO">
        SELECT ci.id,
               ci.name        AS itemName,
               ci.type        AS type,
               ci.material,
               ci.applicable_scope,
               ci.remark,
               ci.price,
               cc.name        AS categoryName,
               cimg.image_url AS imageUrl
        FROM cleaning_item ci
                 LEFT JOIN cleaning_category cc ON ci.category_id = cc.id
                 LEFT JOIN cleaning_item_image cimg ON ci.id = cimg.cleaning_item_id and cimg.sort_order = 1
            <where>
                <if test="categoryId != null and categoryId != ''">
                    ci.category_id = #{categoryId}
                </if>
            </where>
        ORDER BY ci.id ASC, cimg.sort_order ASC
    </select>
    <select id="getCleaningItemById" resultType="com.guanghonggu.dto.CleaningItemDTO">
        SELECT ci.id               AS item_id,
               ci.name             AS item_name,
               ci.type             AS item_type,
               ci.material         AS material,
               ci.applicable_scope AS applicable_scope,
               ci.remark           AS remark,
               ci.price            AS price,
               cc.name             AS category_name
        FROM cleaning_item ci
                 LEFT JOIN cleaning_category cc ON ci.category_id = cc.id
        WHERE ci.id = #{itemId}
    </select>
</mapper>
