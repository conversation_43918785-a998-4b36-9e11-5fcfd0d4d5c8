<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.guanghonggu.mapper.OrderSequenceMapper">

    <resultMap id="BaseResultMap" type="com.guanghonggu.entity.OrderSequence">
            <id property="seqId" column="seq_id" />
            <result property="seqDate" column="seq_date" />
            <result property="currentSeq" column="current_seq" />
            <result property="updateTime" column="update_time" />
    </resultMap>

    <sql id="Base_Column_List">
        seq_id,seq_date,current_seq,update_time
    </sql>
    <select id="selectByDate" resultType="OrderSequence">
        SELECT seq_id, seq_date, current_seq, update_time
        FROM order_sequence WHERE seq_date = #{seqDate} FOR UPDATE
    </select>

    <insert id="insert">
        INSERT INTO order_sequence(seq_date, current_seq) VALUES(#{seqDate}, #{currentSeq})
    </insert>

    <update id="updateSequence">
        UPDATE order_sequence SET current_seq = current_seq + 1 WHERE seq_date = #{seqDate}
    </update>

</mapper>
