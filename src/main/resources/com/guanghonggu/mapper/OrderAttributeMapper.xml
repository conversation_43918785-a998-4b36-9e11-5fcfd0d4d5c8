<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.guanghonggu.mapper.OrderAttributeMapper">

    <resultMap id="BaseResultMap" type="com.guanghonggu.entity.OrderAttribute">
            <id property="id" column="id" />
            <result property="orderId" column="order_id" />
            <result property="attributeKey" column="attribute_key" />
            <result property="attributeValue" column="attribute_value" />
    </resultMap>

    <sql id="Base_Column_List">
        id,order_id,attribute_key,attribute_value
    </sql>
    <insert id="insertBatch">
        insert into order_attribute(order_id,label,attribute_key,attribute_value)
        values
        <foreach collection="insertOrderAttributeList" item="orderAttribute" separator=",">
            (#{orderAttribute.orderId},
            #{orderAttribute.label},
            #{orderAttribute.attributeKey},
            #{orderAttribute.attributeValue}
            )
        </foreach>
    </insert>
</mapper>
