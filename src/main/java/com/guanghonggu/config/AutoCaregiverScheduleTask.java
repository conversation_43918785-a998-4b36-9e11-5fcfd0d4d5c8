package com.guanghonggu.config;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.guanghonggu.entity.Caregiver;
import com.guanghonggu.entity.CaregiverSchedule;
import com.guanghonggu.mapper.CaregiverMapper;
import com.guanghonggu.mapper.CaregiverScheduleMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalTime;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

@Slf4j
@Component
public class AutoCaregiverScheduleTask {
    @Autowired
    private CaregiverMapper caregiverMapper;
    @Autowired
    private CaregiverScheduleMapper caregiverScheduleMapper;

    /**
     * 每分钟检查一次
     */
    @Scheduled(cron = "0 */1 * * * *") // 每分钟执行一次
    public void autoUpdateCaregiverStatusBySchedule() {
        // 查询所有阿姨
        List<Caregiver> allCaregivers = caregiverMapper.selectList(null);
        Date now = new Date();

        for (Caregiver caregiver : allCaregivers) {
            try {
                // 查询该阿姨今天设置的不可接单时间段（schedule_type=0）
                List<CaregiverSchedule> blockList = caregiverScheduleMapper.selectList(
                        new QueryWrapper<CaregiverSchedule>()
                                .eq("caregiver_id", caregiver.getCaregiverId())
                                .eq("schedule_type", 0)
                                .apply("DATE(schedule_date) = CURDATE()")
                );

                // 如果没有设置不可接单时间段，跳过此阿姨，不更新状态
                if (blockList.isEmpty()) {
                    continue; // 没有休息时间段，跳过此阿姨
                }

                boolean inRestTime = false;

                for (CaregiverSchedule block : blockList) {
                    // 拿到 LocalDate + LocalTime
                    Date scheduleDate = block.getScheduleDate();
                    LocalTime startTime = block.getScheduleStartTime();
                    LocalTime endTime = block.getScheduleEndTime();

                    // 拼完整 today-start
                    Calendar cal = Calendar.getInstance();
                    cal.setTime(scheduleDate);
                    cal.set(Calendar.HOUR_OF_DAY, startTime.getHour());
                    cal.set(Calendar.MINUTE, startTime.getMinute());
                    cal.set(Calendar.SECOND, startTime.getSecond());
                    cal.set(Calendar.MILLISECOND, 0);
                    Date fullStart = cal.getTime();

                    // 拼完整 today-end
                    cal.set(Calendar.HOUR_OF_DAY, endTime.getHour());
                    cal.set(Calendar.MINUTE, endTime.getMinute());
                    cal.set(Calendar.SECOND, endTime.getSecond());
                    cal.set(Calendar.MILLISECOND, 0);
                    Date fullEnd = cal.getTime();

                    // 判断 now 是否在这个时间段内
                    if (!now.before(fullStart) && now.before(fullEnd)) {
                        inRestTime = true;
                        break;
                    }
                }

                // 根据判断结果更新阿姨的接单状态
                Integer currentStatus = caregiver.getStatus();
                Integer newStatus = inRestTime ? 0 : 1;

                if (!currentStatus.equals(newStatus)) {
                    caregiverMapper.update(null,
                            new UpdateWrapper<Caregiver>()
                                    .eq("caregiver_id", caregiver.getCaregiverId())
                                    .set("status", newStatus));
                    log.info("阿姨 {} 状态变更为：{}", caregiver.getCaregiverId(), newStatus == 0 ? "不可接单" : "可接单");
                }

            } catch (Exception e) {
                log.error("处理阿姨 {} 状态异常", caregiver.getCaregiverId(), e);
            }
        }
    }
}