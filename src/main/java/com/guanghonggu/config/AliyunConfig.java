package com.guanghonggu.config;

import com.aliyun.dypnsapi20170525.Client;
import com.aliyun.teaopenapi.models.Config;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class AliyunConfig {

    @Value("${aliyun.accessKey.id}")
    private String accessKeyId;

    @Value("${aliyun.accessKey.secret}")
    private String accessKeySecret;

    @Value("${aliyun.dypns.endpoint}")
    private String endpoint;

    /**
     * ✅ 初始化阿里云 Dypns 客户端
     */
    @Bean
    public Client aliyunClient() throws Exception {
        Config config = new Config()
                .setAccessKeyId(accessKeyId)
                .setAccessKeySecret(accessKeySecret)
                .setEndpoint(endpoint); //  建议加上 endpoint（如：dypnsapi.aliyuncs.com）

        return new Client(config);
    }

}
