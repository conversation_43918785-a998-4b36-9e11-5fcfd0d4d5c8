package com.guanghonggu.config;

import cn.dev33.satoken.context.SaHolder;
import cn.dev33.satoken.interceptor.SaInterceptor;
import cn.dev33.satoken.stp.StpUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Slf4j
@Configuration
public class SaTokenInterceptorConfig implements WebMvcConfigurer {

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(new SaInterceptor(handler -> {
                    // 这里表示所有接口都必须登录，除非用 @SaIgnore 标注
                    String requestPath = SaHolder.getRequest().getRequestPath();
                    log.info("请求路径：{}", requestPath);
                    StpUtil.checkLogin();
                }))
                .addPathPatterns("/**")         // 拦截所有接口
                .excludePathPatterns
                        (
                                "/sysPicture/list",
                                "/serviceAttribute/list",
                                //静态资源
                                "/privacyPolicy.html",
                                "/userAgreement.html",
                                "/user.jpg",
                                "/aunt.jpg",
                                // 登录
                                "/user/phoneLogin",
                                "/user/login",              // 登录接口放行
                                "/user/get-mobile",         // 一键登录接口放行
                                "/user/loginWithSms",       // 用户端短信验证码登录放行
                                "/caregiver/loginWithSms",  // 阿姨端短信验证码登录放行
                                "/user/wx-auth",            // 微信跳转
                                "/user/wx-callback",        // 微信回调
                                "/caregiver/get-mobile",
                                "/caregiver/phoneLogin",
                                // 支付回调
                                "/order/wechat/pay/notify",
                                "/wallet/wechat/withdraw/notify",
                                "/wallet/wechat/recharge/notify",
                                // 下单/提现支付
                                "/wallet/withdraw",
                                "/order/released",
                                "/shopOrder/place",
                                "/shopOrder/wechat/pay/notify",
                                // 版本
                                "/appVersion/**",
                                //发送验证码
                                "/user/userSendLoginSms",       // 用户端发送验证码
                                "/user/caregiverSendLoginSms"   // 阿姨端发送验证码
                        );
    }
}
