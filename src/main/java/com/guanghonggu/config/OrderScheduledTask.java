package com.guanghonggu.config;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.guanghonggu.JPush.MyJPushService;
import com.guanghonggu.constant.CommonConstants;
import com.guanghonggu.entity.Caregiver;
import com.guanghonggu.entity.Order;
import com.guanghonggu.enums.AppType;
import com.guanghonggu.enums.WebSocketMessageType;
import com.guanghonggu.mapper.CaregiverMapper;
import com.guanghonggu.mapper.OrderMapper;
import com.guanghonggu.service.impl.CaregiverScheduleServiceImpl;
import com.guanghonggu.service.impl.OrderServiceImpl;
import com.guanghonggu.util.NotificationUtil;
import com.guanghonggu.websocket.WebSocketServer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

import java.util.Date;
import java.util.List;


/**
 * @author：zzn
 * @description：
 * @createData：2025/5/22 14:17
 */
@Slf4j
@Configuration
@EnableScheduling
public class OrderScheduledTask {

    @Autowired
    private OrderMapper orderMapper;

    @Autowired
    private OrderServiceImpl orderService;


    @Autowired
    private CaregiverScheduleServiceImpl caregiverScheduleService;

    @Autowired
    private WebSocketServer webSocketServer;

    @Autowired
    private NotificationUtil notificationUtil;

    @Autowired
    private CaregiverMapper caregiverMapper;

    @Scheduled(cron = "0 */1 * * * ?")
    public void autoConfirmOrders() {
//        log.info("开始执行自动确认订单任务");

        // 计算截止时间 = 当前时间 - 24小时
        Date deadline = DateUtils.addHours(new Date(), -24);

        // 查询超时未确认的订单
        List<String> orderIdList = orderMapper.selectOverdueUnconfirmedOrders(deadline);
//        log.info("共发现 {} 个待自动确认的订单", orderIdList.size());

        for (String orderId : orderIdList) {
            try {
                orderService.confirmOrder(orderId);
                log.info("自动确认订单成功: {}", orderId);
            } catch (Exception e) {
                log.error("自动确认订单失败: {}", orderId, e);
            }
        }
    }

    @Scheduled(cron = "0 */1 * * * ?")
    public void immediateAssignmentOrder() {
        // 查询所有待分配的“立即服务”订单
        LambdaQueryWrapper<Order> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Order::getStatus, 0)
                .eq(Order::getOrderType, 2);
        List<Order> orderList = orderMapper.selectList(queryWrapper);

        // 当前时间
        Date now = new Date();

        for (Order order : orderList) {
            Date appointmentTime = order.getAppointmentTime();

            // 时间差（毫秒）
            long diffMillis = appointmentTime.getTime() - now.getTime();
            long diffMinutes = diffMillis / (1000 * 60);

            // 如果预约时间在1小时内，进行自动分配
            if (diffMinutes >= 0 && diffMinutes <= 60) {
                // 自动分配的阿姨id
                Long caregiverId = caregiverScheduleService.autoAssignCaregiver(order);
                if (caregiverId != null) {
                    caregiverScheduleService.updateAssignCaregiver(order, caregiverId);
                    Caregiver caregiver = caregiverMapper.selectById(caregiverId);
                    // 推送阿姨
                    webSocketServer.sendMessageToTarget("caregiver:" + caregiverId, "", WebSocketMessageType.ORDER_UPDATE.getType());
                    notificationUtil.sendNotificationToJPush(
                            CommonConstants.PUSH_NEW_ORDER_TITLE,
                            CommonConstants.PUSH_NEW_ORDER_ALERT,
                            AppType.CAREGIVER,
                            caregiver.getRegistrationId());
                    log.info("订单 {} 自动指派成功，阿姨ID：{}", order.getOrderId(), caregiverId);
                } else {
                    log.info("订单 {} 自动指派失败，50公里内无阿姨可指派", order.getOrderId());
                }
            }
        }
    }
}
