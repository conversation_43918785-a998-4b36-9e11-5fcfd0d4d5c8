package com.guanghonggu.config;

import com.aliyun.chatbot20220408.Client;

import com.aliyun.teaopenapi.models.Config;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;





@Configuration
public class ChatBotConfig {


    @Value("${aliyun.accessKey.id}")
    private String accessKeyId;

    @Value("${aliyun.accessKey.secret}")
    private String accessKeySecret;

    @Value("${aliyun.regionId}")
    private String regionId;

    @Value("${aliyun.chatbot.endpoint}")
    private String endpoint;


    @Bean
    public Client createClient() throws Exception {

        Config config = new Config()
                .setAccessKeyId(accessKeyId)
                .setAccessKeySecret(accessKeySecret)
                .setEndpoint(endpoint)
                .setRegionId(regionId);

        return new Client(config);
    }


}
