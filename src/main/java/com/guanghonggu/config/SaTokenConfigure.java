package com.guanghonggu.config;

import cn.dev33.satoken.config.SaTokenConfig;
import cn.dev33.satoken.context.SaTokenContext;
import cn.dev33.satoken.spring.SaTokenContextForSpring;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class SaTokenConfigure {


    @Autowired
    public void configureSaToken(SaTokenConfig config) {
        // 从请求头中读取 Token（默认即为该配置）
        config.setTokenName("token");
    }

}
