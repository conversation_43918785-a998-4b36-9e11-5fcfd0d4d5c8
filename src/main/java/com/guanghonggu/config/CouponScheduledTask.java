package com.guanghonggu.config;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.guanghonggu.entity.Coupon;
import com.guanghonggu.entity.UserCoupon;
import com.guanghonggu.mapper.CouponMapper;
import com.guanghonggu.mapper.UserCouponMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

import java.util.Date;
import java.util.List;

/**
 * @author：zzn
 * @description：优惠券定时任务
 * @createData：2025/4/29 10:50
 */
@Slf4j
@Configuration
@EnableScheduling
public class CouponScheduledTask {

    @Autowired
    private UserCouponMapper userCouponMapper;

    @Autowired
    private CouponMapper couponMapper;

    /**
     * 用户领取的优惠券过期后，先把状态改为过期，后续轮询优惠券最终到期时间，如果过期，则删除用户优惠券（deleteExpireUserCoupons）
     */
    @Scheduled(cron = "0 0/1 * * * ?")
    public void updateExpireUserCoupons() {
        Long startTime = System.currentTimeMillis();
        userCouponMapper.updateExpireUserCouponsStatus();
        Long endTime = System.currentTimeMillis();
//        log.info("handlerCouponsUserStatusTimeOutToExpired end second={}", (endTime - startTime) / 1000);
    }

    @Scheduled(cron = "0 0 2 * * ?")
    public void deleteExpireUserCoupons() {
        // 全部优惠券
        LambdaQueryWrapper<Coupon> couponLambdaQueryWrapper = new LambdaQueryWrapper<>();
        couponLambdaQueryWrapper.select(Coupon::getCouponId, Coupon::getValidEndTime);
        List<Coupon> couponList = couponMapper.selectList(couponLambdaQueryWrapper);
        for (Coupon coupon : couponList) {
            // 最终使用截至日期
            Date validEndTime = coupon.getValidEndTime();
            Date date = new Date();
            if (date.after(validEndTime)) {
                Long couponId = coupon.getCouponId();
                LambdaQueryWrapper<UserCoupon> userCouponLambdaQueryWrapper = new LambdaQueryWrapper<>();
                userCouponLambdaQueryWrapper.eq(UserCoupon::getCouponId, couponId);
                userCouponMapper.delete(userCouponLambdaQueryWrapper);
            }
        }
    }
}
