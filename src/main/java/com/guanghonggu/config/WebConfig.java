package com.guanghonggu.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * @author：zzn
 * @description: 跨域配置
 * @createData：2025/4/27 13:45
 */
@Configuration
public class WebConfig implements WebMvcConfigurer {
    @Override
    public void addCorsMappings(CorsRegistry registry) {
        // 配置跨域访问
        registry.addMapping("/**") // 允许所有接口跨域访问
                .allowedOriginPatterns("*") // 允许指定的域名
                .allowedMethods("GET","HEAD", "POST", "PUT", "DELETE", "OPTIONS") // 允许的请求方法
                .allowedHeaders("*") // 允许的请求头
                .allowCredentials(true) // 是否允许携带cookie
                .maxAge(3600); // 预检请求的缓存时间，单位为秒
    }
}
