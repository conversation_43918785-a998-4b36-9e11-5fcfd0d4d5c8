package com.guanghonggu.chatbot;


import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/6/7
 */
@Data
public class ChatbotSSEResponse {
    @J<PERSON>NField(name = "SessionId")
    private String sessionId;

    @J<PERSON>NField(name = "MessageId")
    private String messageId;

    @JSO<PERSON>ield(name = "SequenceId")
    private String sequenceId;

    @JSONField(name = "Source")
    private String source;

    @JSONField(name = "StreamEnd")
    private Boolean streamEnd;

    @JSONField(name = "MessageBody")
    private MessageBody messageBody;

    @Data
    public static class MessageBody {
        @JSONField(name = "Type")
        private String type;

        @J<PERSON>NField(name = "DirectMessageBody")
        private DirectMessageBody directMessageBody;

        @JSO<PERSON>ield(name = "ClarifyMessageBody")
        private ClarifyMessageBody clarifyMessageBody;

        @J<PERSON><PERSON>ield(name = "Commands")
        private Map<String, Object> commands;
    }

    @Data
    public static class DirectMessageBody {
        @JSONField(name = "HitSystemAskConfig")
        private String hitSystemAskConfig;

        @J<PERSON><PERSON>ield(name = "ContentType")
        private String contentType;

        @JSONField(name = "SentenceList")
        private List<Sentence> sentenceList;

    }

    @Data
    public static class ClarifyMessageBody {
        @JSONField(name = "Title")
        private String title;

        @JSONField(name = "ClarifyList")
        private List<Clarify> clarifyList;

        @JSONField(name = "ClarifyContent")
        private String clarifyContent;
    }

    @Data
    public static class Sentence {
        @JSONField(name = "Content")
        private String content;
        @JSONField(name = "ReferNumber")
        private Integer referNumber;
    }

    @Data
    public static class Clarify {
        @JSONField(name = "Title")
        private String title;
    }
}
