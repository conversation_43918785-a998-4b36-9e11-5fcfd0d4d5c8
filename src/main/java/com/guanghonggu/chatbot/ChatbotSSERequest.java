package com.guanghonggu.chatbot;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.Arrays;
import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 * @date 2024/2/23
 */
@Data
public class ChatbotSSERequest {
    @JSONField(name = "messageId")
    private String messageId = UUID.randomUUID().toString();

    @JSONField(name = "action")
    private String action = "TongyiBeebotChat";

    @J<PERSON>NField(name = "version")
    private String version = "2022-04-08";

    @J<PERSON><PERSON>ield(name = "data")
    private List<DataItem> data;

    @Data
    @NoArgsConstructor
    public static class DataItem {
        @JSONField(name = "type")
        private String type = "JSON_TEXT";

        @JSONField(name = "value")
        private String value;

        public DataItem(String value) {
            this.value = value;
        }
    }

    @Data
    @Accessors(chain = true)
    public static class UtteranceData {
        @J<PERSON><PERSON>ield(name = "InstanceId")
        private String instanceId;
        @J<PERSON><PERSON>ield(name = "Utterance")
        private String utterance;
        @JSONField(name = "SessionId")
        private String sessionId;

    }

    public static ChatbotSSERequest build(String instanceId, String utterance, String sessionId) {
        ChatbotSSERequest sseChatRequest = new ChatbotSSERequest();
        UtteranceData utteranceData = new UtteranceData()
            .setUtterance(utterance)
            .setInstanceId(instanceId)
            .setSessionId(sessionId);

        sseChatRequest.setData(Arrays.asList(new DataItem(JSON.toJSONString(utteranceData))));

        return sseChatRequest;
    }
}
