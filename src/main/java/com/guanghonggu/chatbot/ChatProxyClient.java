package com.guanghonggu.chatbot;


import com.guanghonggu.service.MessageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.annotation.PostConstruct;

/**
 * <AUTHOR>
 * @date 2024/6/14
 */
@Component
@Slf4j
public class ChatProxyClient {
    private ChatbotClient chatbotClient;

    @Value("${aliyun.chatbot.robot-id}")
    private String CHATBOT_INSTANCE_ID;
    @Value("${aliyun.agentKey}")
    private String CHATBOT_AGENT_KEY;
    @Value("${aliyun.accessKey.id}")
    private String ALIYUN_ACCESS_KEY;
    @Value("${aliyun.accessKey.secret}")
    private String ALIYUN_SECRET_KEY;

    @Autowired
    private MessageService messageService;

    @PostConstruct
    public void init() throws Exception {
        chatbotClient = new ChatbotClient(CHATBOT_AGENT_KEY, ALIYUN_ACCESS_KEY, ALIYUN_SECRET_KEY,messageService);
    }

    public void chat(String sessionId, String text, SseEmitter sseEmitter) {
        log.info("receive message :{}", text);
        try {
            if (text != null) {
                //机器人接收消息内容
                JavaChatContext context = new JavaChatContext();
                context.setSessionId(sessionId);
                context.setQuery(text);
                context.setSseEmitter(sseEmitter);

                ChannelMsgSender sender = new JavaClientMsgSender(context);
                sender.preSengMsg();

                // 调用大模型
                ChatbotSSERequest reqData = ChatbotSSERequest.build(CHATBOT_INSTANCE_ID, text, sessionId);
                chatbotClient.invokeLlm(reqData, sender);

            }
        } catch (Exception e) {
            log.error("receive message by error:" + e.getMessage(), e);
        }
    }
}
