package com.guanghonggu.chatbot;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.guanghonggu.service.MessageService;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;
import okhttp3.sse.EventSource;
import okhttp3.sse.EventSourceListener;
import org.apache.commons.lang3.StringUtils;


/**
 * <AUTHOR>
 * @date 2024/5/31
 */
@Slf4j
public class SSEEventSourceListener extends EventSourceListener {

    private final ChannelMsgSender channelMsgSender;
    private final MessageService messageService;

    public SSEEventSourceListener(ChannelMsgSender channelMsgSender, MessageService messageService) {
        this.channelMsgSender = channelMsgSender;
        this.messageService = messageService;
    }

    @Override
    public void onOpen(EventSource eventSource, Response response) {
        log.info("onOpen: {}", response);
        super.onOpen(eventSource, response);
    }

    @Override
    public void onEvent(EventSource eventSource, String id, String type, String data) {
        //   接受消息 data
        parseResponse(data);
        super.onEvent(eventSource, id, type, data);
    }

    private void parseResponse(String message) {
        JSONObject jsonRes;
        try {
            jsonRes = JSON.parseObject(message);

//            log.info("onMessage: {}", message);
            String jsonValue = jsonRes.getJSONArray("data").getJSONObject(0).getString("value");

            ChatbotSSEResponse llmGaussResp = JSON.parseObject(jsonValue, ChatbotSSEResponse.class);

            if (llmGaussResp != null && llmGaussResp.getMessageBody() != null) {
                StringBuilder content = new StringBuilder();
                ChatbotSSEResponse.MessageBody mb = llmGaussResp.getMessageBody();
                if (mb.getDirectMessageBody() != null &&
                        mb.getDirectMessageBody().getSentenceList().size() > 0) {
                    for (ChatbotSSEResponse.Sentence sentence : mb.getDirectMessageBody().getSentenceList()) {
                        content.append(sentence.getContent());
                    }
                }
                if (mb.getClarifyMessageBody() != null) {
                    content.append(mb.getClarifyMessageBody().getClarifyContent());
                }

//                log.info("大模型答案：{}", content);
                String content1 = content.toString();
                if (StringUtils.isNotBlank(content1)) {
                    channelMsgSender.sendMsg(message, content1, llmGaussResp.getStreamEnd());
//                    messageService.receiveMessage(message);
                }
            }

        } catch (Exception e) {
            log.error("解析大模型返回数据异常", e);
        }
    }

    @Override
    public void onClosed(EventSource eventSource) {
        log.info("onClosed: {}", eventSource);
        channelMsgSender.postSendMsg();

        super.onClosed(eventSource);
    }

    @Override
    public void onFailure(EventSource eventSource, Throwable t, Response response) {
        log.info("onFailure: {}, response:{}", JSON.toJSONString(eventSource), JSON.toJSONString(response));
        if (t != null) {
            t.printStackTrace();
        }
        super.onFailure(eventSource, t, response);
    }
}
