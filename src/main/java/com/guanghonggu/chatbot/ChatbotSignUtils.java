package com.guanghonggu.chatbot;

import org.apache.commons.codec.binary.Hex;
import org.apache.commons.codec.digest.DigestUtils;

/**
 * <AUTHOR>
 * @date 2024/2/23
 */
public class ChatbotSignUtils {
    public static String getSign(String streamSecret, String timestamp) {
        String toHash = "streamSecret=" + streamSecret + "&timestamp=" + timestamp;
        byte[] md5Hash = DigestUtils.md5(toHash);
        return Hex.encodeHexString(md5Hash);
    }
}
