package com.guanghonggu.chatbot;

/**
 * 渠道消息发送者
 *
 * <AUTHOR>
 * @date 2024/6/7
 */
public abstract class ChannelMsgSender<T> {
    protected T context;

    public ChannelMsgSender(T context) {
        this.context = context;
    }

    /**
     * 发送消息，一般用于回复首包，类似于钉钉先出一个卡片消息，然后出现一个loading的动态图片
     */
    public abstract void preSengMsg();

    /**
     * 流式回复消息，会调用多次
     */
    public abstract void sendMsg(String originResp, String reply, Boolean streamEnd) throws Exception;

    /**
     * 大模型回复完成后调用，可以用于保存一些数据或更新一些状态
     */
    public abstract void postSendMsg();
}
