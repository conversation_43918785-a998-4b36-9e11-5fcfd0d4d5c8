package com.guanghonggu.chatbot;


import org.apache.commons.lang3.BooleanUtils;

/**
 * <AUTHOR>
 * @date 2024/6/14
 */
public class JavaClientMsgSender extends ChannelMsgSender<JavaChatContext> {

    public JavaClientMsgSender(JavaChatContext context) {
        super(context);
    }

    @Override
    public void preSengMsg() {

    }

    @Override
    public void sendMsg(String originResp, String reply, Boolean streamEnd) throws Exception {
        context.getSseEmitter().send(originResp);
        if (BooleanUtils.isTrue(streamEnd)) {
            context.getSseEmitter().complete();
        }
    }

    @Override
    public void postSendMsg() {

    }

}
