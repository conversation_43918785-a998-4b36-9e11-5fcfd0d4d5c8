package com.guanghonggu.chatbot;

/**
 * <AUTHOR>
 * @date 2024/2/23
 */


import com.alibaba.fastjson.annotation.JSONField;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class ChatbotSSEToken {
    @J<PERSON><PERSON>ield(name = "RequestId")
    private String requestId;

    @JSONField(name = "AccessToken")
    private String accessToken;

    @JSONField(name = "ChannelId")
    private String channelId;

    @JSONField(name = "StreamSecret")
    private String streamSecret;

}
