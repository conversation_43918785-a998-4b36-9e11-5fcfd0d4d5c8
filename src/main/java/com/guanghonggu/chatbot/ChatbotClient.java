package com.guanghonggu.chatbot;


import com.alibaba.fastjson.JSON;
import com.guanghonggu.service.MessageService;
import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.sse.EventSource;
import okhttp3.sse.EventSources;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2024/6/11
 */
@Slf4j
public class ChatbotClient {
    private static final MediaType JSON_HEADER = MediaType.parse("application/json; charset=utf-8");
    public static final String SSE_URL = "https://alime-ws.aliyuncs.com/sse/paas4Json/%s/%s/%s/%s";

    static OkHttpClient httpClient = new OkHttpClient.Builder()
        .readTimeout(10, TimeUnit.SECONDS)
        .writeTimeout(10, TimeUnit.SECONDS)
        .connectTimeout(10, TimeUnit.SECONDS)
        .build();
    private final String chatbotAgentKey;
    private final String aliyunAccessKey;
    private final String aliyunSecretKey;
    private final MessageService messageService;

    public ChatbotClient(String chatbotAgentKey, String aliyunAccessKey, String aliyunSecretKey, MessageService messageService) {
        this.chatbotAgentKey = chatbotAgentKey;
        this.aliyunAccessKey = aliyunAccessKey;
        this.aliyunSecretKey = aliyunSecretKey;
        this.messageService = messageService;
    }

    public void invokeLlm(ChatbotSSERequest reqData, ChannelMsgSender sender) throws Exception {
        ChatbotSSEToken token = ChatbotSSETokenFetcher.getSseToken(aliyunAccessKey, aliyunSecretKey, chatbotAgentKey);
        long timeMillis = System.currentTimeMillis();
        String sign = ChatbotSignUtils.getSign(token.getStreamSecret(), String.valueOf(timeMillis));

        String url = String.format(SSE_URL, token.getAccessToken(), token.getChannelId(), sign, timeMillis);

        log.info("url:{}", url);

        EventSource.Factory factory = EventSources.createFactory(httpClient);

        String json = JSON.toJSONString(reqData);

        RequestBody body = RequestBody.create(JSON_HEADER, json);
        // 请求对象
        Request request = new Request.Builder().url(url).post(body).build();

        SSEEventSourceListener eventSourceListener = new SSEEventSourceListener(sender,messageService);

        factory.newEventSource(request, eventSourceListener);

    }
}
