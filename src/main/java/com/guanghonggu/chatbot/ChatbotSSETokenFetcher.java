package com.guanghonggu.chatbot;

/**
 * <AUTHOR>
 * @date 2024/2/23
 */


import com.alibaba.fastjson.JSON;
import com.aliyun.chatbot20220408.Client;
import com.aliyun.chatbot20220408.models.ApplyForStreamAccessTokenRequest;
import com.aliyun.chatbot20220408.models.ApplyForStreamAccessTokenResponse;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.concurrent.TimeUnit;

@Slf4j
public class ChatbotSSETokenFetcher {

    private static Client createClient(String accessKeyId, String accessKeySecret) throws Exception {
        com.aliyun.teaopenapi.models.Config config = new com.aliyun.teaopenapi.models.Config()
            .setAccessKeyId(accessKeyId)
            .setAccessKeySecret(accessKeySecret);
        // Endpoint 请参考 https://api.aliyun.com/product/Chatbot
        config.endpoint = "chatbot.cn-shanghai.aliyuncs.com";
        return new Client(config);
    }

    private static final LoadingCache<TokenRequest, ChatbotSSEToken> CACHE = CacheBuilder.newBuilder()
        .maximumSize(10)
        .expireAfterWrite(90, TimeUnit.MINUTES)
        .build(new CacheLoader<TokenRequest, ChatbotSSEToken>() {
            @Override
            public ChatbotSSEToken load(TokenRequest key) throws Exception {
                return doGetSseToken(key);
            }
        });

    public static ChatbotSSEToken getSseToken(String ak, String sk, String agentKey) throws Exception {
        return CACHE.get(new TokenRequest(ak, sk, agentKey));
    }

    public static ChatbotSSEToken doGetSseToken(TokenRequest key) throws Exception {
        log.info("getSseToken:{}", key);
        Client client = createClient(key.getAk(), key.getSk());
        ApplyForStreamAccessTokenRequest applyForStreamAccessTokenRequest = new ApplyForStreamAccessTokenRequest();
        if (StringUtils.isNotBlank(key.getAgentKey())) {
            applyForStreamAccessTokenRequest.setAgentKey(key.getAgentKey());
        }
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();

        // 复制代码运行请自行打印 API 的返回值
        ApplyForStreamAccessTokenResponse res
            = client.applyForStreamAccessTokenWithOptions(applyForStreamAccessTokenRequest, runtime);

        ChatbotSSEToken sseToken = new ChatbotSSEToken();
        sseToken.setChannelId(res.getBody().getChannelId());
        sseToken.setStreamSecret(res.getBody().getStreamSecret());
        sseToken.setAccessToken(res.getBody().getAccessToken());
        sseToken.setRequestId(res.getBody().getRequestId());

        log.info("sseToken:{}", JSON.toJSONString(res));
        return sseToken;
    }

    @Data
    @Accessors(chain = true)
    @AllArgsConstructor
    public static class TokenRequest {
        private String ak;
        private String sk;
        private String agentKey;

    }
}
