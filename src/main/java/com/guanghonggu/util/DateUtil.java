package com.guanghonggu.util;

import java.util.Calendar;
import java.util.Date;

public class DateUtil {

    /**
     * 在给定的 Date 上加上 指定的时分秒
     * @param date 原始时间
     * @param hours 小时数
     * @param minutes 分钟数
     * @param seconds 秒数
     * @return 加完之后的时间
     */
    public static Date addTime(Date date, int hours, int minutes, int seconds) {
        if (date == null) return null;

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.HOUR_OF_DAY, hours);
        calendar.add(Calendar.MINUTE, minutes);
        calendar.add(Calendar.SECOND, seconds);
        return calendar.getTime();
    }

    /**
     * 获取时间偏移量后的时间
     * @param base
     * @param seconds
     * @return
     */
    public static Date offsetTime(Date base, int seconds) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(base);
        cal.add(Calendar.SECOND, seconds);
        return cal.getTime();
    }

    /**
     * 加 23小时59分59秒（封装好，直接用）
     */
    public static Date addAlmostOneDay(Date date) {
        return addTime(date, 23, 59, 59);
    }
}
