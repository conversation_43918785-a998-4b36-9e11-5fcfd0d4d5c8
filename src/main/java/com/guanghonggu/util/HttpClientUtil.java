package com.guanghonggu.util;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;

public class HttpClientUtil {

    /**
     * 发起 GET 请求，获取响应内容
     * @param url 请求地址
     * @return 响应字符串
     * @throws Exception 网络异常
     */
    public static String doGet(String url) throws Exception {
        URL obj = new URL(url);
        HttpURLConnection con = (HttpURLConnection) obj.openConnection();

        // 设置请求方法
        con.setRequestMethod("GET");

        // 设置请求头
        con.setRequestProperty("User-Agent", "Mozilla/5.0");

        // 读取返回结果
        BufferedReader in = new BufferedReader(new InputStreamReader(con.getInputStream(), "UTF-8"));
        String inputLine;
        StringBuilder response = new StringBuilder();

        while ((inputLine = in.readLine()) != null) {
            response.append(inputLine);
        }
        in.close();

        return response.toString();
    }

    /**
     * 发起 POST 请求，发送 XML 数据
     * @param url 请求地址
     * @param xmlData XML 格式数据
     * @return 响应字符串
     * @throws Exception 网络异常
     */
    public static String postXml(String url, String xmlData) throws Exception {
        URL obj = new URL(url);
        HttpURLConnection con = (HttpURLConnection) obj.openConnection();

        con.setRequestMethod("POST");
        con.setDoOutput(true);
        con.setDoInput(true);
        con.setRequestProperty("Content-Type", "application/xml;charset=UTF-8");
        con.setConnectTimeout(10000);
        con.setReadTimeout(10000);

        // 发送 XML 数据
        try (OutputStream outputStream = con.getOutputStream()) {
            outputStream.write(xmlData.getBytes("UTF-8"));
            outputStream.flush();
        }

        // 读取返回结果
        StringBuilder response = new StringBuilder();
        try (BufferedReader in = new BufferedReader(new InputStreamReader(con.getInputStream(), "UTF-8"))) {
            String line;
            while ((line = in.readLine()) != null) {
                response.append(line);
            }
        }

        return response.toString();
    }
}
