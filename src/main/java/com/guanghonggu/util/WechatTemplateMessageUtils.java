package com.guanghonggu.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.guanghonggu.dto.OrderDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.text.SimpleDateFormat;

/**
 * @author：zzn
 * @description：
 * @createData：2025/8/8 15:31
 */
@Slf4j
@Component
public class WechatTemplateMessageUtils {

    @Value("${wx.mini-programs.app-id}")
    private String appId;

    @Value("${wx.mini-programs.app-secret}")
    private String appSecret;

    @Value("${wx.mini-programs.template-id}")
    private String templateId;

    private final RestTemplate restTemplate = new RestTemplate();

    // 获取 access_token
    public String getAccessToken() {
        String url = String.format(
                "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=%s&secret=%s",
                appId, appSecret
        );

        String response = restTemplate.getForObject(url, String.class);
        JSONObject json = JSON.parseObject(response);
        return json.getString("access_token");
    }

    /**
     * 发送订阅消息
     * @param openId    openId
     * @param orderDTO  订单信息
     */
    public void sendOrderAcceptedMessage(String openId, OrderDTO orderDTO) {
        String accessToken = getAccessToken();
        String url = "https://api.weixin.qq.com/cgi-bin/message/subscribe/send?access_token=" + accessToken;

        JSONObject payload = new JSONObject();
        payload.put("touser", openId);
        payload.put("template_id", templateId);
        payload.put("miniprogram_state", "trial");

        JSONObject data = new JSONObject();
        String address = orderDTO.getArea() + orderDTO.getLocationDetails() + orderDTO.getPlaceName() + orderDTO.getDetailedAddress();
        String thing1 = address;
        if (thing1.length() > 20) {
            thing1 = address.substring(0, 17) + "...";
        }
        data.put("thing1", createData(thing1));
        data.put("character_string3", createData(orderDTO.getOrderNumber()));
        data.put("thing4", createData(orderDTO.getServiceTypeName()));
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        String reserveTimeStr = sdf.format(orderDTO.getAppointmentTime());
        data.put("date5", createData(reserveTimeStr));
        data.put("amount10", createData(orderDTO.getCaregiverIncome().toString()));

        payload.put("data", data);
        orderDTO.setDetailedAddress(address);
        String jsonString = JSONObject.toJSONString(orderDTO);
        payload.put("page", "subPackages/homeStack/orderInfo/orderInfo?data=" + jsonString);
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<String> request = new HttpEntity<>(payload.toJSONString(), headers);

        String response = restTemplate.postForObject(url, request, String.class);
        log.info("微信订阅消息推送结果：{}", response);
    }

    private JSONObject createData(String value) {
        JSONObject json = new JSONObject();
        json.put("value", value);
        return json;
    }
}

