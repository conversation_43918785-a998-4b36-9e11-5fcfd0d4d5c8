package com.guanghonggu.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.beans.factory.annotation.Autowired;


@Slf4j
@Service
public class AmapService {

    @Value("${amap.key}")
    private String amapKey;  // 读取配置文件中的 amap.key

    private final RestTemplate restTemplate;

    // 地球半径，单位：米（6371km）
    private static final double EARTH_RADIUS = 6371000;

    // 构造方法，注入 RestTemplate
    @Autowired
    public AmapService(RestTemplate restTemplate) {
        this.restTemplate = restTemplate;
    }

    public String getCoordinates(String address) {
        try {
            // 对地址进行编码，确保地址参数的正确性
//            String encodedAddress = URLEncoder.encode(address, "UTF-8");

            // 构建API请求URL
            String url = String.format(
                    "https://restapi.amap.com/v3/geocode/geo?address=%s&key=%s",
                    address,  // 编码后的地址
                    amapKey           // 使用从配置文件读取的API密钥
            );

            log.info("Requesting URL: " + url);  // 输出请求的 URL

            // 发送请求并获取响应
            String response = restTemplate.getForObject(url, String.class);

            // 输出返回的完整响应，方便调试
            log.info("Response from Amap API: " + response);

            // 解析响应获取经纬度
            return parseCoordinates(response);
        } catch (Exception e) {
            log.error("Error making API request", e);
            throw new RuntimeException("Error calling AMap API", e);  // 可以抛出自定义异常
        }
    }

    private String parseCoordinates(String response) {
        // 解析返回的JSON数据来提取经纬度信息
        if (response != null) {
            try {
                com.alibaba.fastjson.JSONObject jsonResponse = com.alibaba.fastjson.JSON.parseObject(response);
                String status = jsonResponse.getString("status");

                if ("1".equals(status)) { // 确保请求成功
                    com.alibaba.fastjson.JSONArray geocodes = jsonResponse.getJSONArray("geocodes");
                    if (geocodes != null && !geocodes.isEmpty()) {
                        String location = geocodes.getJSONObject(0).getString("location");
                        if (location != null && location.contains(",")) {
                            String[] coordinates = location.split(",");
                            String longitude = coordinates[0];
                            String latitude = coordinates[1];

                            log.info("Parsed coordinates: Longitude: " + longitude + ", Latitude: " + latitude);
                            return longitude + "," + latitude;
                        } else {
                            log.error("Invalid location format for coordinates: " + location);
                        }
                    } else {
                        log.error("No geocodes found in response");
                    }
                } else {
                    String info = jsonResponse.getString("info");
                    log.error("API request failed. Status: " + status + ", Info: " + info);
                }
            } catch (Exception e) {
                log.error("Failed to parse coordinates", e);
                throw new RuntimeException("Failed to parse coordinates", e);  // 可抛出自定义异常
            }
        }
        return null;  // 如果解析失败，返回null
    }

    /**
     * 计算两个经纬度之间的直线距离（单位：米）
     *
     * @param lat1 起点纬度
     * @param lon1 起点经度
     * @param lat2 终点纬度
     * @param lon2 终点经度
     * @return 距离（米）
     */
    public Double getDistance(Double lat1, Double lon1, Double lat2, Double lon2) {
        if (lat1 == null || lon1 == null || lat2 == null || lon2 == null) {
            return null;
        }
        // 转弧度
        double radLat1 = Math.toRadians(lat1);
        double radLat2 = Math.toRadians(lat2);
        double deltaLat = radLat1 - radLat2;
        double deltaLon = Math.toRadians(lon1 - lon2);

        double a = Math.sin(deltaLat / 2) * Math.sin(deltaLat / 2)
                + Math.cos(radLat1) * Math.cos(radLat2)
                * Math.sin(deltaLon / 2) * Math.sin(deltaLon / 2);

        double c = 2 * Math.asin(Math.sqrt(a));
        return EARTH_RADIUS * c;
    }

    // 示例
    public static void main(String[] args) {
        // 示例：北京两个点
        double lat1 = 39.049327;
        double lon1 = 117.12368;
        double lat2 = 39.04353;
        double lon2 = 117.125199;

//        double distance = getDistance(lat1, lon1, lat2, lon2);
//        System.out.printf("直线距离：%.2f 米\n", distance);
    }
}
