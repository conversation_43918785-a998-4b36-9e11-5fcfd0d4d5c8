package com.guanghonggu.util;

import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSONObject;
import com.guanghonggu.dto.OrderDTO;
import com.guanghonggu.service.OrderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * @author：zzn
 * @description：
 * @createData：2025/8/11 15:00
 */
@Slf4j
@Component
public class WechatRobotSenderUtil {

    private static final String WEBHOOK_URL = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=4eb5ff44-951d-4b8d-8cf2-ac480166836c";
    private static final String BIRTH_NANNY_WEBHOOK_URL = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=4eb5ff44-951d-4b8d-8cf2-ac480166836c";

    @Value("${wx.mini-programs.app-id}")
    private String appId;

    @Autowired
    private OrderService orderService;

    /**
     * 下单后发送企业微信卡片消息（除月嫂 保姆 护工外）
     *
     * @param orderId
     */
    public void sendCard(Long orderId) {

        OrderDTO orderDTO = orderService.getByOrderIds(orderId).getData();
        String address = orderDTO.getArea() + orderDTO.getLocationDetails() + orderDTO.getPlaceName() + orderDTO.getDetailedAddress();
        JSONObject cardAction = new JSONObject();
        cardAction.put("type", 2);
        cardAction.put("appid", appId);
        cardAction.put("pagepath", "pages/orderAccept/index?orderId=" + orderId);

        JSONObject source = new JSONObject();
        source.put("icon_url", "https://qinqinjava.cn/static/logo.jpg");
        source.put("desc", "勤勤姐姐");
        source.put("desc_color", 0);

        JSONObject mainTitle = new JSONObject();
        mainTitle.put("title", "新订单通知");
        mainTitle.put("desc", "点击查看详情");

        JSONObject emphasisContent = new JSONObject();
        emphasisContent.put("keyname", "订单地址");
        emphasisContent.put("value", address);

        JSONObject jumpItem = new JSONObject();
        jumpItem.put("type", 2);
        jumpItem.put("appid", appId);
        jumpItem.put("pagepath", "pages/orderAccept/index?orderId=" + orderId);

        jumpItem.put("title", "打开小程序");

        JSONObject templateCard = new JSONObject();
        templateCard.put("card_type", "text_notice");
        templateCard.put("main_title", mainTitle);
        templateCard.put("source", source);
        templateCard.put("horizontal_content_list", new Object[]{emphasisContent});
        templateCard.put("jump_list", new Object[]{jumpItem});
        templateCard.put("card_action", cardAction);

        JSONObject body = new JSONObject();
        body.put("msgtype", "template_card");
        body.put("template_card", templateCard);
        String resp = HttpRequest.post(WEBHOOK_URL)
                .header("Content-Type", "application/json")
                .body(body.toJSONString())
                .execute()
                .body();
    }

    /**
     * 下单后发送企业微信卡片消息（月嫂 保姆 护工）
     *
     * @param orderId 订单ID
     */
    public void sendOrderDetailCard(Long orderId) {
        OrderDTO orderDTO = orderService.getByOrderIds(orderId).getData();

        String address = orderDTO.getArea()
                + orderDTO.getLocationDetails()
                + orderDTO.getPlaceName()
                + orderDTO.getDetailedAddress();
        String serviceTypeName = orderDTO.getServiceTypeName();
        String appointmentTime = cn.hutool.core.date.DateUtil.format(orderDTO.getAppointmentTime(), "yyyy-MM-dd HH:mm");
        String phoneNumber = orderDTO.getPhoneNumber();

        orderDTO.setDetailedAddress(address);
        String jsonString = JSONObject.toJSONString(orderDTO);

        // 点击卡片默认跳转小程序
        JSONObject cardAction = new JSONObject();
        cardAction.put("type", 2);
        cardAction.put("appid", appId);
        cardAction.put("pagepath", "subPackages/homeStack/orderInfo/orderInfo?orderId=" + orderId);
        log.info("json:{}", jsonString);

        // 来源信息
        JSONObject source = new JSONObject();
        source.put("icon_url", "https://qinqinjava.cn/static/logo.jpg");
        source.put("desc", "勤勤姐姐");
        source.put("desc_color", 0);

        // 标题
        JSONObject mainTitle = new JSONObject();
        mainTitle.put("title", "新订单提醒");
        mainTitle.put("desc", "请及时查看并处理");

        // 横向内容（展示多个关键信息）
        List<JSONObject> horizontalContentList = new ArrayList<>();

        horizontalContentList.add(buildContent("订单ID", String.valueOf(orderId)));
        horizontalContentList.add(buildContent("服务类型", serviceTypeName));
        horizontalContentList.add(buildContent("服务地址", address));
        horizontalContentList.add(buildContent("预约时间", appointmentTime));
        horizontalContentList.add(buildContent("联系电话", phoneNumber));

        // 跳转按钮
        JSONObject jumpItem = new JSONObject();
        jumpItem.put("type", 2);
        jumpItem.put("appid", appId);
        jumpItem.put("pagepath", "subPackages/homeStack/orderInfo/orderInfo?orderId=" + orderId);
        jumpItem.put("title", "打开订单详情");

        // 组装卡片
        JSONObject templateCard = new JSONObject();
        templateCard.put("card_type", "text_notice");
        templateCard.put("main_title", mainTitle);
        templateCard.put("source", source);
        templateCard.put("horizontal_content_list", horizontalContentList);
        templateCard.put("jump_list", new Object[]{jumpItem});
        templateCard.put("card_action", cardAction);

        // 请求体
        JSONObject body = new JSONObject();
        body.put("msgtype", "template_card");
        body.put("template_card", templateCard);

        // 发送
        String resp = HttpRequest.post(BIRTH_NANNY_WEBHOOK_URL)
                .header("Content-Type", "application/json")
                .body(body.toJSONString())
                .execute()
                .body();

        log.info("企业微信群推送结果: {}", resp);
    }

    /**
     * 构建横向展示内容
     */
    private JSONObject buildContent(String key, String value) {
        JSONObject content = new JSONObject();
        content.put("keyname", key);
        content.put("value", value);
        return content;
    }

}
