package com.guanghonggu.util;

import com.wechat.pay.contrib.apache.httpclient.WechatPayHttpClientBuilder;
import com.wechat.pay.contrib.apache.httpclient.util.PemUtil;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.util.EntityUtils;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.FileInputStream;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.PrivateKey;
import java.util.*;

/**
 * 微信支付工具类（兼容 V3 下单、签名、随机字符串）
 */
public class WXPayUtil {

    // 生成随机字符串
    public static String generateNonceStr() {
        return UUID.randomUUID().toString().replaceAll("-", "");
    }

    // 生成签名（用于 V2）
    public static String generateSignature(Map<String, String> data, String key) throws Exception {
        SortedMap<String, String> sortedMap = new TreeMap<>(data);
        StringBuilder sb = new StringBuilder();
        for (Map.Entry<String, String> entry : sortedMap.entrySet()) {
            if (entry.getValue() != null && !"".equals(entry.getValue()) && !"sign".equals(entry.getKey())) {
                sb.append(entry.getKey()).append("=").append(entry.getValue()).append("&");
            }
        }
        sb.append("key=").append(key);
        return md5(sb.toString()).toUpperCase();
    }

    // MD5 加密
    private static String md5(String data) throws Exception {
        MessageDigest md = MessageDigest.getInstance("MD5");
        byte[] array = md.digest(data.getBytes("UTF-8"));
        StringBuilder sb = new StringBuilder();
        for (byte b : array) {
            sb.append(String.format("%02x", b & 0xff));
        }
        return sb.toString();
    }

    // V3 下单：JSAPI 接口请求
    public static String createJsapiPrepay(String jsonData, String mchId, String serialNo, String privateKeyPath, String apiV3Key) throws Exception {
        // 1. 读取私钥
        PrivateKey merchantPrivateKey = PemUtil.loadPrivateKey(new FileInputStream(privateKeyPath));

        // 2. 构建微信支付 V3 HTTP 客户端
        WechatPayHttpClientBuilder builder = WechatPayHttpClientBuilder.create()
                .withMerchant(mchId, serialNo, merchantPrivateKey)
                .withValidator((response) -> true);     //后续有可能需要修改

        try (CloseableHttpClient httpClient = builder.build()) {
            // 3. 构造 POST 请求
            HttpPost httpPost = new HttpPost("https://api.mch.weixin.qq.com/v3/pay/transactions/jsapi");

            // 加上微信强制要求的头部
            httpPost.setHeader("Accept", "application/json");
            httpPost.setHeader("User-Agent", "guanghonggu-pay");
            httpPost.setHeader("Content-Type", "application/json");

            // 设置请求体
            httpPost.setEntity(new StringEntity(jsonData, StandardCharsets.UTF_8));

            // 发起请求
            try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
                int statusCode = response.getStatusLine().getStatusCode();
                String body = EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8);
                if (statusCode == 200 || statusCode == 204) {
                    return body;
                } else {
                    // 打印微信返回内容，方便调试
                    System.err.println(" 微信统一下单失败，状态码：" + statusCode);
                    System.err.println(" 返回内容：" + body);
                    throw new RuntimeException("微信支付预下单失败，状态码: " + statusCode + "，返回: " + body);
                }
            }
        }
    }

    // V3 签名（SHA256 with RSA）
    public static String signWithPrivateKey(String message, String privateKeyPath) throws Exception {
        PrivateKey privateKey = PemUtil.loadPrivateKey(new FileInputStream(privateKeyPath));
        java.security.Signature sign = java.security.Signature.getInstance("SHA256withRSA");
        sign.initSign(privateKey);
        sign.update(message.getBytes(StandardCharsets.UTF_8));
        return Base64.getEncoder().encodeToString(sign.sign());
    }
}
