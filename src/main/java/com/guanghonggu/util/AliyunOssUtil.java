package com.guanghonggu.util;

import com.aliyun.oss.ClientBuilderConfiguration;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.common.comm.Protocol;
import com.aliyun.oss.model.PutObjectRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.net.URL;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.UUID;

/**
 * @author：zzn
 * @description：
 * @createData：2025/5/20 10:20
 */
@Slf4j
@Component
public class AliyunOssUtil {

    // 从配置文件中读取 OSS 的 endpoint（地域节点），例如：oss-cn-beijing.aliyuncs.com
    @Value("${aliyun.oss.endpoint}")
    private String endpoint;

    // 阿里云账号的 AccessKey ID，相当于用户名
    @Value("${aliyun.accessKey.id}")
    private String accessKeyId;

    // 阿里云账号的 AccessKey Secret，相当于密码，具有访问 OSS 的权限
    @Value("${aliyun.accessKey.secret}")
    private String accessKeySecret;

    // OSS 中你创建的 bucket 名称（存储空间）
    @Value("${aliyun.oss.bucketName}")
    private String bucketName;

    // 文件路径前缀（相当于 OSS 中的目录名），例如 complaint 表示上传到 complaint 目录下
    @Value("${aliyun.oss.fileHost}")
    private String fileHost;

    /**
     * 上传图片到阿里云 OSS
     *
     * @param file 用户上传的 MultipartFile 文件（图片）
     * @return 上传后的图片访问地址（拼接后的公网链接）
     */
    public String upload(MultipartFile file) {
        try {
            // 第1步：生成按日期分类的文件夹路径，比如 "2025/05/20"
            // 好处：文件不会全部堆在一个目录下，便于管理
            String datePath = new SimpleDateFormat("yyyy/MM/dd").format(new Date());
            // 第2步：获取用户上传的原始文件名（如 abc.jpg）
            String originalFilename = file.getOriginalFilename();
            // 第3步：生成不重复的新文件名（UUID + 原始后缀），如：af2d234f34f23f.jpg
            String fileName = UUID.randomUUID().toString().replace("-", "") +
                    originalFilename.substring(originalFilename.lastIndexOf("."));
            // 说明：
            //  - UUID 是保证唯一（防止覆盖）
            //  - substring(...) 获取的是原来的后缀（如 .jpg）
            // 第4步：拼接 OSS 中的完整路径（objectName，不含域名）
            // 例：2025/05/20/af2d234f34f23f.jpg
            String objectName = datePath + "/" + fileName;
            // 第5步：连接 OSS 客户端，准备上传
            OSS ossClient = new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);
            // 上传文件：把文件内容上传到你指定的 bucket + 路径（objectName）
            ossClient.putObject(bucketName, objectName, file.getInputStream());
            // 上传后要关闭客户端连接，释放资源
            ossClient.shutdown();
            // 第6步：生成带签名的完整访问链接（含域名 + objectName + ?签名参数）
            return generatePresignedUrl(objectName);
        } catch (Exception e) {
            // 发生异常时打印错误信息，并返回 null
            log.error("上传文件失败！", e);
            return null;
        }
    }

    /**
     * 生成带签名的临时访问链接（用于私有 Bucket 文件下载）
     *
     * @param objectName 文件名（不含目录）
     * @return 有效期内可访问的带签名下载链接
     */
    public String generatePresignedUrl(String objectName) {
        try {
            // 第1步：构造 OSS 客户端（必须每次上传/下载都重新连接）
            OSS ossClient = new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);
            // 第2步：设置签名链接的过期时间（单位：毫秒）
            // 当前系统时间 + 1小时（3600秒 × 1000 = 3600000毫秒）
            Date expiration = new Date(System.currentTimeMillis() + 3600L * 1000 * 24 * 365);
            // 第3步：生成带签名的临时访问 URL
            // 参数说明：
            //  - bucketName：你创建的 OSS 桶名
            //  - objectName：文件路径（不含域名）
            //  - expiration：链接有效期
            URL url = ossClient.generatePresignedUrl(bucketName, objectName, expiration);
            // 第4步：关闭客户端，释放资源
            ossClient.shutdown();
            // 第5步：返回最终的完整签名链接（可直接访问）
            return url.toString();
        } catch (Exception e) {
            // 如果生成失败，打印错误信息，返回 null（上层可判断）
            log.error("上传失败", e);
            return null;
        }
    }

    /**
     * 删除阿里云，数据库图片
     */
    public void delete(String fullUrl) {
        // 打印完整的 OSS 文件链接（带签名参数）
        System.out.println("准备删除的完整 OSS URL: " + fullUrl);
        try {
            // 第一步：去掉链接末尾的参数（? 后面的签名、时间戳等）
            // fullUrl 形如：https://xxx.aliyuncs.com/2025/05/21/abc.png?Expires=xxxx
            // split("\\?") 表示用问号分隔，只保留前面部分，即去掉 ? 后面参数
            String urlWithoutParams = fullUrl.split("\\?")[0];
            System.out.println("去除参数后的 URL: " + urlWithoutParams);
            // 第二步：从链接中提取出真正的文件路径（objectName）
            // 假设现在是：https://xxx.aliyuncs.com/2025/05/21/abc.png
            // .split(".com/", 2) 表示用 ".com/" 分隔成两段：前面是域名，后面就是 objectName
            // 注意：不能写成 ".aliyuncs.com/"，因为 endpoint 可能变化
            String[] parts = urlWithoutParams.split(".com/", 2);
            // 判断是否成功提取
            if (parts.length < 2) {
                System.err.println("OSS路径格式错误，提取 objectName 失败");
                return;
            }
            // objectName 就是 OSS 中真实的路径，例如：2025/05/21/abc.png
            String objectName = parts[1];
            System.out.println("提取出的 objectName: " + objectName);
            // 第三步：连接 OSS 并执行删除操作
            // 使用你的配置创建 OSS 客户端
            OSS ossClient = new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);
            // 删除指定 bucket 中的文件
            ossClient.deleteObject(bucketName, objectName);
            // 删除成功提示
            System.out.println("OSS 删除成功！");
            // 关闭连接
            ossClient.shutdown();
        } catch (Exception e) {
            // 如果过程中出现异常，打印错误信息和详细堆栈
            System.err.println("OSS 删除失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 上传文件
     * @param file
     * @param folderName  user或caregiver/xx/{userId}
     * @return
     * @throws IOException
     */
    public String upload(MultipartFile file, String folderName) throws IOException {
        String datePath = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
        String fileName =  folderName + "/" + datePath + "/" + new Date().getTime() + "-" + file.getOriginalFilename();
        ClientBuilderConfiguration conf = new ClientBuilderConfiguration();
        conf.setProtocol(Protocol.HTTPS);
        // 创建 OSSClient 对象
        OSS ossClient = new OSSClientBuilder().build(
                endpoint, accessKeyId, accessKeySecret, conf
        );

        ossClient.putObject(bucketName, fileName, file.getInputStream());
        Date expiration = new Date(new Date().getTime() + 3600L * 1000 * 24 * 365 * 10);
        String url = ossClient.generatePresignedUrl(bucketName, fileName, expiration).toString();
        ossClient.shutdown();

        return url;
    }

    public static void test() {
        // 从配置文件中读取 OSS 的 endpoint（地域节点），例如：oss-cn-beijing.aliyuncs.com
        String endpoint = "oss-cn-beijing.aliyuncs.com";

        // 阿里云账号的 AccessKey ID，相当于用户名
        String accessKeyId = "LTAI5tHZtvMzbLrtG59kA4Xu";

        // 阿里云账号的 AccessKey Secret，相当于密码，具有访问 OSS 的权限
        String accessKeySecret = "******************************";

        // OSS 中你创建的 bucket 名称（存储空间）
        String picture = "鞋套 - 副本.png";
        String bucketName = "diligent-sister";
        ClientBuilderConfiguration conf = new ClientBuilderConfiguration();
        conf.setProtocol(Protocol.HTTPS);
        OSS ossClient = new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret, conf);
        String fileName = "system-picture/" + picture;
        PutObjectRequest putObjectRequest = new PutObjectRequest(bucketName, fileName, new File("F:\\file\\" + picture));
        ossClient.putObject(putObjectRequest);
        Date expiration = new Date(new Date().getTime() + 3600L * 1000 * 24 * 365 * 10);
        String url = ossClient.generatePresignedUrl(bucketName, fileName, expiration).toString();
        System.out.println(url);
    }

    public static void main(String[] args) {
        test();
    }
}
