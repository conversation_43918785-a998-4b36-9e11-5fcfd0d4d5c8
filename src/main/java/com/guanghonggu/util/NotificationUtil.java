package com.guanghonggu.util;

import com.google.gson.JsonObject;
import com.guanghonggu.JPush.MyJPushService;
import com.guanghonggu.JPush.PushBean;
import com.guanghonggu.constant.CommonConstants;
import com.guanghonggu.enums.AppType;
import com.guanghonggu.websocket.WebSocketServer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class NotificationUtil {

    @Autowired
    private WebSocketServer webSocketServer;

    @Autowired
    private MyJPushService myJPushService;

    /**
     * 发送WebSocket消息同时推送极光消息
     */
    public void sendNotificationToWebSocketAndJPush(String targetId, Object payload, String messageType, String pushTitle, String pushAlert, AppType appType, String... rids) {
        // 发送WebSocket消息
        webSocketServer.sendMessageToTarget(targetId, payload, messageType);
        // 发送极光推送消息
        sendNotificationToJPush(pushTitle, pushAlert, appType, rids);
    }

    public void sendNotificationToJPush(String pushTitle, String pushAlert, AppType appType, String... rids) {
        // 发送极光推送
        PushBean pushBean = new PushBean();
        pushBean.setAlert(pushAlert);
        pushBean.setTitle(pushTitle);
        JsonObject jsonObject = new JsonObject();
        jsonObject.addProperty("url", CommonConstants.PUSH_SKIP_HOME_INTENT);
        pushBean.setIntent(jsonObject);
        myJPushService.pushAllByRID(pushBean, appType, rids);
    }
}