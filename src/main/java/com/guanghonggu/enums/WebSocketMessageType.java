package com.guanghonggu.enums;

import lombok.Getter;

/**
 * @author：zzn
 * @description：
 * @createData：2025/7/15 14:03
 */
@Getter
public enum WebSocketMessageType {
    /**
     * 立即分配给阿姨订单：订单更新
     */
    ORDER_UPDATE("order_update"),
    NOTICE_UPDATE("notice_update"),
    /**
     * 预约阿姨 - 阿姨是否接受订单
     */
    WORKER_ORDER_RESPONSE("worker_order_response"),
    EDIT_ORDER_RESPONSE("edit_order_response"),

    /**
     * 等待阿姨处理退款
     */
    WAITING_REFUND_PROCESSING("waiting_refund_processing"),
    /**
     * 用户取消订单 - 直接退款
     */
    DIRECT_REFUNDS("direct_refunds"),
    /**
     * 用户获取定位信息
     */
    LOCATION_UPDATE("location_update"),
    /**
     * 阿姨下单商品推送管理端
     */
    SHOP_ORDER_TO_MANAGEMENT("shop_order_to_management"),
    /**
     * 用户下单 管理端提示
     */
    ORDER_RELEASED_MANAGEMENT("order_released_management"),
    HEARTBEAT("heartbeat");


    private final String type;

    WebSocketMessageType(String type) {
        this.type = type;
    }

}
