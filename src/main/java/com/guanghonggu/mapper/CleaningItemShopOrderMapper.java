package com.guanghonggu.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.guanghonggu.dto.CleaningItemShopOrderDTO;
import com.guanghonggu.dto.CleaningItemShopOrderDetailDTO;
import com.guanghonggu.entity.CleaningItemShopOrder;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【cleaning_item_shop_order(服务人员购买清洁工具订单表)】的数据库操作Mapper
* @createDate 2025-07-23 11:50:43
* @Entity com.guanghonggu.entity.CleaningItemShopOrder
*/
public interface CleaningItemShopOrderMapper extends BaseMapper<CleaningItemShopOrder> {

    IPage<CleaningItemShopOrderDTO> selectShopOrderPage(Page<CleaningItemShopOrderDTO> page, @Param("caregiverId") Long caregiverId);

    List<CleaningItemShopOrderDetailDTO> selectDetailsByOrderIds(@Param("orderIds") List<Long> orderIds);

}




