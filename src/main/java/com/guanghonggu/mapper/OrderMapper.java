package com.guanghonggu.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.guanghonggu.dto.OrderAttributeDTO;
import com.guanghonggu.dto.OrderDTO;
import com.guanghonggu.entity.Order;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
* <AUTHOR>
* @description 针对表【order(订单表)】的数据库操作Mapper
* @createDate 2025-04-27 10:56:56
* @Entity com.guanghonggu.entity.Order
*/
public interface OrderMapper extends BaseMapper<Order> {

    /**
     * 查询超过24小时未确认、已完成服务的订单
     * @param deadline
     * @return
     */
    List<String> selectOverdueUnconfirmedOrders(@Param("deadline") Date deadline);

    Page<OrderDTO> getOrdersByUserIdPaged(Page<OrderDTO> page, @Param("userId") Long userId);

    /**
     * 获取阿姨接单大厅订单分页列表
     */
    IPage<OrderDTO> getOrderHallList(Page<OrderDTO> page, Long serviceTypeId, String area);

    OrderDTO selectOrderAgainById(Long orderId);

    /**
     * 根据订单id查询订单详细信息
     * @param orderId  订单id
     * @return
     */
    OrderDTO selectOrderDetailById(Long orderId);

    Order selectByIdForUpdate(Long orderId);

    List<OrderDTO> getOrderById(@Param("orderId") Long orderId);

    List<OrderAttributeDTO> getOrderAttributesByOrderId(@Param("orderId") Long orderId);

    // 查询订单及地址信息的分页查询
    List<OrderDTO> getOrdersWithAddressInfo(Map<String, Object> params);

    List<OrderAttributeDTO> getOrderAttibutes(Long order_id);

    /**
     * 根据阿姨id查询阿姨未处理的预约单
     * @param caregiverId 阿姨id
     * @return
     */
    List<OrderDTO> listAppointmentOrdersByCaregiver(Long caregiverId);

    /**
     * 根据阿姨是否有未确认用户修改订单查询
     */
    List<OrderDTO> listAppointmentOrdersByCaregiverWithStatus(Long caregiverId, Integer status);

    /**
     * 根据阿姨是否有用户取消订单未确认的订单查询
     */
    List<OrderDTO> listCancelledOrdersByCaregiver(Long caregiverId, Integer status);

    /**
     * 根据用户id查询阿姨拒绝预约单
     */
    List<OrderDTO> listRejectAppointmentByUser(Long userId, Integer status);

    @MapKey("caregiverId")
    List<Map<String, Object>> countTodayOrdersForCaregivers(
            @Param("caregiverIds") List<Long> caregiverIds,
            @Param("todayStart") Date todayStart,
            @Param("todayEnd") Date todayEnd
    );


}




