package com.guanghonggu.mapper;

import com.guanghonggu.entity.UserCoupon;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.ArrayList;

/**
 * <AUTHOR>
 * @description 针对表【user_coupon(用户优惠卷表)】的数据库操作Mapper
 * @createDate 2025-04-27 10:56:56
 * @Entity com.guanghonggu.entity.UserCoupon
 */
public interface UserCouponMapper extends BaseMapper<UserCoupon> {

    /**
     * 更新过期优惠券状态
     */
    void updateExpireUserCouponsStatus();

    /**
     * 批量插入
     * @param userCoupons
     */
    void insertBatch(ArrayList<UserCoupon> userCoupons);

    /**
     * 更新优惠券为已使用
     * @param userCouponId  用户优惠券id
     * @return
     */
    int updateUsedIfUnused(@Param("userCouponId") Long userCouponId,
                           @Param("status") Integer  status);
}




