package com.guanghonggu.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.guanghonggu.dto.CouponDTO;
import com.guanghonggu.entity.Coupon;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【coupon(优惠卷表)】的数据库操作Mapper
* @createDate 2025-04-27 10:56:56
* @Entity com.guanghonggu.entity.Coupon
*/
public interface CouponMapper extends BaseMapper<Coupon> {

    /**
     * 获取用户优惠券列表
     * @param userId  用户id
     * @return
     */
    IPage<CouponDTO> listUserCoupon(Page<CouponDTO> page, @Param("userId") Long userId);

    /**
     * 查询优惠券
     * @param couponId  优惠券id
     * @param userId    用户id
     * @return
     */
    Coupon getCoupon(String couponId, Long userId);

    /**
     * 更新优惠券剩余数量
     * @param couponId  优惠券id
     * @return
     */
    int updateCouponRemainingQuantity(String couponId);
}