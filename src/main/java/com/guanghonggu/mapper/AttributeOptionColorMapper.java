package com.guanghonggu.mapper;

import com.guanghonggu.entity.AttributeOptionColor;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【attribute_option_color(属性选项颜色配置表)】的数据库操作Mapper
* @createDate 2025-06-23 16:59:42
* @Entity com.guanghonggu.entity.AttributeOptionColor
*/
public interface AttributeOptionColorMapper extends BaseMapper<AttributeOptionColor> {

    List<AttributeOptionColor> selectByAttributeKey();

    List<AttributeOptionColor> selectDefaultColors();
}




