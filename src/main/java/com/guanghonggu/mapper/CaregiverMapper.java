package com.guanghonggu.mapper;

import com.guanghonggu.dto.StatisticsDTO;
import com.guanghonggu.entity.Caregiver;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【caregiver(阿姨表)】的数据库操作Mapper
* @createDate 2025-04-27 10:56:56
* @Entity com.guanghonggu.entity.Caregiver
*/
@Mapper
public interface CaregiverMapper extends BaseMapper<Caregiver> {
    /**
     * 按城市统计阿姨数量
     */
    List<StatisticsDTO> getCityCaregiverCount();

}




