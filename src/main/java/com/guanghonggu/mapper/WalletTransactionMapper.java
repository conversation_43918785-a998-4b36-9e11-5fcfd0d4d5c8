package com.guanghonggu.mapper;

import com.guanghonggu.entity.WalletTransaction;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
* <AUTHOR>
* @description 针对表【wallet_transaction(钱包交易记录表)】的数据库操作Mapper
* @createDate 2025-04-27 10:56:56
* @Entity com.guanghonggu.entity.WalletTransaction
*/
@Mapper
public interface WalletTransactionMapper extends BaseMapper<WalletTransaction> {

    int updateStatusIfUnprocessed(@Param("transactionId") Long transactionId,
                                  @Param("transferBillNo") String transferBillNo,
                                  @Param("oldStatus") Integer oldStatus,
                                  @Param("newStatus") Integer newStatus);


    WalletTransaction selectByOutTradeNo(@Param("outTradeNo") String outTradeNo);
}




