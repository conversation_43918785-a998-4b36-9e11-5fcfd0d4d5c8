package com.guanghonggu.mapper;

import com.guanghonggu.entity.OrderAttribute;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【order_attribute】的数据库操作Mapper
* @createDate 2025-05-23 14:42:23
* @Entity com.guanghonggu.entity.OrderAttribute
*/
public interface OrderAttributeMapper extends BaseMapper<OrderAttribute> {

    void insertBatch(@Param("insertOrderAttributeList") List<OrderAttribute> insertOrderAttributeList);

}




