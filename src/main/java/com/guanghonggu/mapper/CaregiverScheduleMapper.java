package com.guanghonggu.mapper;

import com.guanghonggu.entity.CaregiverSchedule;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.Date;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【caregiver_schedule(阿姨日程表)】的数据库操作Mapper
* @createDate 2025-04-27 10:56:56
* @Entity com.guanghonggu.entity.CaregiverSchedule
*/
public interface CaregiverScheduleMapper extends BaseMapper<CaregiverSchedule> {


    List<Long> findCaregiversWithNoScheduleOnDate(@Param("caregiverIds") List<Long> caregiverIds,
                                                  @Param("appointmentDate") Date appointmentDate);

    List<Long> findCaregiversWithNoTimeConflict(@Param("caregiverIds") List<Long> caregiverIds,
                                                @Param("date") LocalDate date,
                                                @Param("appointmentStart") LocalTime appointmentStart,
                                                @Param("appointmentEnd") LocalTime appointmentEnd);
}




