package com.guanghonggu.mapper;

import com.guanghonggu.entity.OrderSequence;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @description 针对表【order_sequence】的数据库操作Mapper
 * @createDate 2025-05-27 11:54:24
 * @Entity com.guanghonggu.entity.OrderSequence
 */
public interface OrderSequenceMapper extends BaseMapper<OrderSequence> {

    OrderSequence selectByDate(@Param("seqDate") String seqDate);

    int insert(OrderSequence orderSequence);

    int updateSequence(@Param("seqDate") String seqDate);
}




