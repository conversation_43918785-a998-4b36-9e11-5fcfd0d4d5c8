package com.guanghonggu.mapper;

import com.guanghonggu.entity.WalletMonthlySummary;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
* <AUTHOR>
* @description 针对表【wallet_monthly_summary(用户每月钱包收支汇总表)】的数据库操作Mapper
* @createDate 2025-07-29 14:35:51
* @Entity com.guanghonggu.entity.WalletMonthlySummary
*/
public interface WalletMonthlySummaryMapper extends BaseMapper<WalletMonthlySummary> {

    WalletMonthlySummary selectByUserAndMonth(Long userId, Integer userType, int year, int month);
}




