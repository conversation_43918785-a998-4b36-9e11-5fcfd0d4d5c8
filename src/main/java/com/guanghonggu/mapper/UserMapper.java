package com.guanghonggu.mapper;

import com.guanghonggu.dto.StatisticsDTO;
import com.guanghonggu.entity.User;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【user(用户表)】的数据库操作Mapper
* @createDate 2025-04-27 10:56:56
* @Entity com.guanghonggu.entity.User
*/
@Mapper
public interface UserMapper extends BaseMapper<User> {
    /**
     * 查询各个城市用户数量
     */
    List<StatisticsDTO> getCityUserCount();

}




