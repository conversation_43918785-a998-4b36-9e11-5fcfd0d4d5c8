package com.guanghonggu.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.guanghonggu.dto.CleaningItemDTO;
import com.guanghonggu.dto.ResultDTO;
import com.guanghonggu.entity.CleaningItem;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【cleaning_item(清洁工具与清洁剂表)】的数据库操作Mapper
* @createDate 2025-07-03 09:17:50
* @Entity com.guanghonggu.entity.CleaningItem
*/
public interface CleaningItemMapper extends BaseMapper<CleaningItem> {

    IPage<CleaningItemDTO> listByCategoryId(Page<CleaningItemDTO> page, Long categoryId);

    CleaningItemDTO getCleaningItemById(String itemId);

}




