package com.guanghonggu.mapper;

import com.guanghonggu.entity.Evaluations;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;

/**
* <AUTHOR>
* @description 针对表【evaluations(评价表)】的数据库操作Mapper
* @createDate 2025-04-27 10:56:56
* @Entity com.guanghonggu.entity.Evaluations
*/
public interface EvaluationsMapper extends BaseMapper<Evaluations> {
    BigDecimal getAvgScoreByCaregiverId(@Param("caregiverId") Long caregiverId);
}




