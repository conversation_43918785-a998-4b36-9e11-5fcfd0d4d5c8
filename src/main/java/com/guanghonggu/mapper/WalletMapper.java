package com.guanghonggu.mapper;

import com.guanghonggu.entity.Wallet;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
* <AUTHOR>
* @description 针对表【wallet(钱包表)】的数据库操作Mapper
* @createDate 2025-04-27 10:56:56
* @Entity com.guanghonggu.entity.Wallet
*/
public interface WalletMapper extends BaseMapper<Wallet> {

    /**
     * 获取钱包信息并加锁
     * @param userId    用户/阿姨id
     * @param userType  类型 (1: 用户, 2: 阿姨)
     * @return
     */
    Wallet selectWalletByUserForUpdate(@Param("userId") Long userId,
                                       @Param("userType") Integer userType);
    @Select("SELECT * FROM wallet WHERE user_id = #{userId} LIMIT 1")
    Wallet selectByUserId(@Param("userId") Long userId);

    /**
     * 根据id查询钱包并加锁
     * @param walletId 钱包id
     * @return
     */
    Wallet selectWalletByIdForUpdate(@Param("walletId") Long walletId);
}




