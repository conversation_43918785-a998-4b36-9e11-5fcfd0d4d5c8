package com.guanghonggu.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.guanghonggu.entity.Location;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface LocationMapper extends BaseMapper<Location> {

    List<Location> findWithinDistance(@Param("lat") double lat,
                                      @Param("lng") double lng,
                                      @Param("radiusKm") int radiusKm,
                                      @Param("serviceTypeId") Long serviceTypeId);

}
