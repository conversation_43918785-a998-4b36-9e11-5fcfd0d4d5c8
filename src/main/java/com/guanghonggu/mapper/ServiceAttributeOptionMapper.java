package com.guanghonggu.mapper;

import com.guanghonggu.entity.ServiceAttributeOption;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【service_attribute_option】的数据库操作Mapper
* @createDate 2025-05-23 14:42:23
* @Entity com.guanghonggu.entity.ServiceAttributeOption
*/
public interface ServiceAttributeOptionMapper extends BaseMapper<ServiceAttributeOption> {

    List<ServiceAttributeOption> selectByAttributeKey(String attributeKey, String serviceTypeCode);
}




