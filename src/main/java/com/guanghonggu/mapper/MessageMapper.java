package com.guanghonggu.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.guanghonggu.dto.MessageDTO;
import com.guanghonggu.entity.Message;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface MessageMapper extends BaseMapper<Message> {

    /**
     * 分页查询用户系统消息
     * @param page      page
     * @param userId    用户id
     * @param userType  用户类型（1用户，2阿姨）
     * @return
     */
    IPage<MessageDTO> selectMessage(Page<MessageDTO> page,
                                    @Param("userId") Long userId,
                                    @Param("userType") Integer userType);
}
