package com.guanghonggu.mapper.dto;

import com.guanghonggu.dto.OrderDTO;
import com.guanghonggu.entity.Order;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * @author：zzn
 * @description：
 * @createData：2025/5/7 11:03
 */
@Mapper
public interface OrderDTOMapper {

    OrderDTOMapper INSTANCE = Mappers.getMapper(OrderDTOMapper.class);

    OrderDTO toOrderDTO(Order order);

}
