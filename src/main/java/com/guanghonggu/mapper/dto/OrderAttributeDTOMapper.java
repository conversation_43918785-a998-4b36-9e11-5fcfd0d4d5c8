package com.guanghonggu.mapper.dto;

import com.guanghonggu.dto.OrderAttributeDTO;
import com.guanghonggu.entity.OrderAttribute;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * @author：zzn
 * @description：
 * @createData：2025/5/7 11:03
 */
@Mapper
public interface OrderAttributeDTOMapper {

    OrderAttributeDTOMapper INSTANCE = Mappers.getMapper(OrderAttributeDTOMapper.class);

    List<OrderAttributeDTO> toOrderAttributeList(List<OrderAttribute> order);

}
