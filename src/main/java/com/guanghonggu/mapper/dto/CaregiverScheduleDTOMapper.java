package com.guanghonggu.mapper.dto;

import com.guanghonggu.dto.CaregiverScheduleDTO;
import com.guanghonggu.entity.CaregiverSchedule;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * @author：zzn
 * @description：钱包交易记录DTO转换接口
 * @createData：2025/4/30 17:15
 */
@Mapper
public interface CaregiverScheduleDTOMapper {

    /**
     * 获取 CouponMapper 实例
     */
    CaregiverScheduleDTOMapper INSTANCE = Mappers.getMapper(CaregiverScheduleDTOMapper.class);

    /**
     * 转换为DTO List
     * @param
     * @return
     */
    List<CaregiverScheduleDTO> toCaregiverScheduleDTOList(List<CaregiverSchedule> caregiverScheduleList);
}
