package com.guanghonggu.mapper.dto;

import com.guanghonggu.dto.CouponDTO;
import com.guanghonggu.entity.Coupon;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;


import java.util.List;

/**
 * @author：zzn
 * @description：优惠卷DTO映射
 * @createData：2025/4/28 10:56
 */
@Mapper
public interface CouponDTOMapper {

    // 获取 CouponMapper 实例
    CouponDTOMapper INSTANCE = Mappers.getMapper(CouponDTOMapper.class);

    CouponDTO toCouponDTO(Coupon coupon);

    List<CouponDTO> toCouponDTOList(List<Coupon> couponList);
}
