package com.guanghonggu.mapper.dto;

import com.guanghonggu.dto.SysPictureDTO;
import com.guanghonggu.entity.SysPicture;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface AddressDTOMapper {
    AddressDTOMapper INSTANCE = Mappers.getMapper(AddressDTOMapper.class);

    List<SysPictureDTO> toSysPictureDTOList(List<SysPicture> order);

}
