package com.guanghonggu.mapper.dto;

import com.guanghonggu.dto.WalletTransactionDTO;
import com.guanghonggu.entity.WalletTransaction;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * @author：zzn
 * @description：钱包交易记录DTO转换接口
 * @createData：2025/4/30 17:15
 */
@Mapper
public interface WalletTransactionDTOMapper {

    /**
     * 获取 CouponMapper 实例
     */
    WalletTransactionDTOMapper INSTANCE = Mappers.getMapper(WalletTransactionDTOMapper.class);

    /**
     * 转换为DTO List
     * @param walletTransaction
     * @return
     */
    List<WalletTransactionDTO> toWalletTransactionDTO(List<WalletTransaction> walletTransaction);
}
