package com.guanghonggu.mapper.dto;

import com.guanghonggu.dto.SysPictureDTO;
import com.guanghonggu.entity.SysPicture;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * @author：zzn
 * @description：
 * @createData：2025/5/7 11:03
 */
@Mapper
public interface SysPictureDTOMapper {

    SysPictureDTOMapper INSTANCE = Mappers.getMapper(SysPictureDTOMapper.class);

    List<SysPictureDTO> toSysPictureDTOList(List<SysPicture> order);

}
