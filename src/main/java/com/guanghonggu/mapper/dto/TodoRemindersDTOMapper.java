package com.guanghonggu.mapper.dto;

import com.guanghonggu.dto.ToDoRemindersDTO;
import com.guanghonggu.entity.ToDoReminders;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * @author：zzn
 * @description：待办事项DTO映射
 * @createData：2025/4/28 10:56
 */
@Mapper
public interface TodoRemindersDTOMapper {

    // 获取 CouponMapper 实例
    TodoRemindersDTOMapper INSTANCE = Mappers.getMapper(TodoRemindersDTOMapper.class);

    ToDoRemindersDTO toDoRemindersDTO(ToDoRemindersDTO toDoRemindersDTO);

    List<ToDoRemindersDTO> toDoRemindersDTOList(List<ToDoReminders> toDoRemindersList);
}
