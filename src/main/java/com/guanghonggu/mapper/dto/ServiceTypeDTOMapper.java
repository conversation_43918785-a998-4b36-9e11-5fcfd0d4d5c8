package com.guanghonggu.mapper.dto;

import com.guanghonggu.dto.ServiceTypeDTO;
import com.guanghonggu.dto.SysPictureDTO;
import com.guanghonggu.entity.ServiceType;
import com.guanghonggu.entity.SysPicture;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * @author：zzn
 * @description：
 * @createData：2025/5/23 15:27
 */
@Mapper
public interface ServiceTypeDTOMapper {

    ServiceTypeDTOMapper INSTANCE = Mappers.getMapper(ServiceTypeDTOMapper.class);

    List<ServiceTypeDTO> toSysPictureDTOList(List<ServiceType> order);
}
