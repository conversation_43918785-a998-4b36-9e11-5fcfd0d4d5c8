package com.guanghonggu.JPush;

import com.guanghonggu.enums.AppType;

/**
 * @author：zzn
 * @description：
 * @createData：2025/7/10 14:30
 */
public interface MyJPushService {
    boolean pushAll(PushBean pushBean, AppType appType);

    boolean pushIos(PushBean pushBean, AppType appType);

    boolean pushIos(PushBean pushBean, AppType appType, String... registids);

    boolean pushAndroid(PushBean pushBean, AppType appType);

    boolean pushAndroid(PushBean pushBean, AppType appType, String... registids);

    /**
     * 根据指定rid发送所有设备
     * @param pushBean
     * @param registids
     * @return
     */
    boolean pushAllByRID(PushBean pushBean, AppType appType, String... registids);

    String[] checkRegistrationIds(String[] registids);

}
