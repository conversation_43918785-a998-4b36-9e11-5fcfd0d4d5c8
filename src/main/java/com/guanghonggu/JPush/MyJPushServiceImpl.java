package com.guanghonggu.JPush;

import com.guanghonggu.enums.AppType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * @author：zzn
 * @description：
 * @createData：2025/7/10 14:31
 */
@Service
public class MyJPushServiceImpl implements MyJPushService {
    /**
     * 一次推送最大数量 (极光限制1000)
     */
    private static final int max_size = 800;

    @Autowired
    private JPushService jPushService;

    /**
     * 推送全部, 不支持附加信息
     *
     * @return
     */
    @Override
    public boolean pushAll(PushBean pushBean, AppType appType) {
        return jPushService.pushAll(pushBean, appType);
    }

    /**
     * 推送全部ios
     *
     * @return
     */
    @Override
    public boolean pushIos(PushBean pushBean, AppType appType) {
        return jPushService.pushIos(pushBean, appType);
    }

    /**
     * 推送ios 指定id
     *
     * @return
     */
    @Override
    public boolean pushIos(PushBean pushBean, AppType appType, String... registids) {
        registids = checkRegistrationIds(registids); // 剔除无效registed
        while (registids.length > max_size) { // 每次推送max_size个
            jPushService.pushIos(pushBean, appType, Arrays.copyOfRange(registids, 0, max_size));
            registids = Arrays.copyOfRange(registids, max_size, registids.length);
        }
        return jPushService.pushIos(pushBean, appType, registids);
    }

    /**
     * 推送全部android
     *
     * @return
     */
    @Override
    public boolean pushAndroid(PushBean pushBean, AppType appType) {
        return jPushService.pushAndroid(pushBean, appType);
    }

    /**
     * 推送android 指定id
     *
     * @return
     */
    @Override
    public boolean pushAndroid(PushBean pushBean, AppType appType, String... registids) {
        registids = checkRegistrationIds(registids); // 剔除无效registed
        while (registids.length > max_size) { // 每次推送max_size个
            jPushService.pushAndroid(pushBean, appType, Arrays.copyOfRange(registids, 0, max_size));
            registids = Arrays.copyOfRange(registids, max_size, registids.length);
        }
        return jPushService.pushAndroid(pushBean, appType, registids);
    }

    @Override
    public boolean pushAllByRID(PushBean pushBean, AppType appType, String... registids) {
        registids = checkRegistrationIds(registids); // 剔除无效registed
        while (registids.length > max_size) { // 每次推送max_size个
            jPushService.pushAllByRID(pushBean, appType, Arrays.copyOfRange(registids, 0, max_size));
            registids = Arrays.copyOfRange(registids, max_size, registids.length);
        }
        return jPushService.pushAllByRID(pushBean, appType, registids);
    }

    /**
     * 剔除无效registed
     *
     * @param registids
     * @return
     */
    @Override
    public String[] checkRegistrationIds(String[] registids) {
        List<String> regList = new ArrayList<String>(registids.length);
        for (String registid : registids) {
            if (registid != null && !registid.trim().isEmpty()) {
                regList.add(registid);
            }
        }
        return regList.toArray(new String[0]);
    }
}
