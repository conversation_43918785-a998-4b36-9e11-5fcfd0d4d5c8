package com.guanghonggu.JPush;

import cn.jpush.api.JPushClient;
import com.guanghonggu.enums.AppType;
import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;

/**
 * @author：zzn
 * @description：
 * @createData：2025/7/10 14:14
 */

@Configuration
public class JPushConfig {

    // 用户端 App 的配置
    @Value("${jPush.user.appKey}")
    private String userAppKey;

    @Value("${jPush.user.masterSecret}")
    private String userMasterSecret;

    // 服务端 App 的配置
    @Value("${jPush.caregiver.appKey}")
    private String caregiverAppKey;

    @Value("${jPush.caregiver.masterSecret}")
    private String caregiverMasterSecret;

    /**
     * -- GETTER --
     *  获取用户端推送客户端
     */
    @Getter
    private JPushClient userClient;
    /**
     * -- GETTER --
     *  获取服务端推送客户端
     */
    @Getter
    private JPushClient caregiverClient;

    @PostConstruct
    public void initJPushClients() {
        userClient = new JPushClient(userMasterSecret, userAppKey);
        caregiverClient = new JPushClient(caregiverMasterSecret, caregiverAppKey);
    }

    public JPushClient getClient(AppType appType) {
        switch (appType) {
            case USER:
                return userClient;
            case CAREGIVER:
                return caregiverClient;
            default:
                throw new IllegalArgumentException("未知App类型");
        }
    }

}
