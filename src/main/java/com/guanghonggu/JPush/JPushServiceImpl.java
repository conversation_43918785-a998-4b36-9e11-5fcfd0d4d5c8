package com.guanghonggu.JPush;

import cn.jiguang.common.resp.APIConnectionException;
import cn.jiguang.common.resp.APIRequestException;
import cn.jpush.api.JPushClient;
import cn.jpush.api.push.PushResult;
import cn.jpush.api.push.model.Platform;
import cn.jpush.api.push.model.PushPayload;
import cn.jpush.api.push.model.audience.Audience;
import cn.jpush.api.push.model.notification.AndroidNotification;
import cn.jpush.api.push.model.notification.IosNotification;
import cn.jpush.api.push.model.notification.Notification;
import com.guanghonggu.enums.AppType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @author：zzn
 * @description：
 * @createData：2025/7/10 14:21
 */

@Service
@Slf4j
public class JPushServiceImpl implements JPushService {

    @Autowired
    private JPushConfig jPushConfig;

    /**
     * 广播 (所有平台，所有设备, 不支持附加信息)
     *
     * @param pushBean 推送内容
     * @return
     */
    @Override
    public boolean pushAll(PushBean pushBean,AppType appType) {
        PushPayload build = PushPayload.newBuilder()
                .setPlatform(Platform.all())
                .setAudience(Audience.all())
                .setNotification(Notification.alert(pushBean.getAlert()))
                .build();
        return sendPush(build, appType);
    }

    /**
     * 广播 (所有平台，指定设备)
     *
     * @param pushBean
     * @param registids
     * @return
     */
    @Override
    public boolean pushAllByRID(PushBean pushBean, AppType appType, String... registids) {
        PushPayload payload = PushPayload.newBuilder()
                .setPlatform(Platform.all())
                .setAudience(Audience.registrationId(registids))
                .setNotification(Notification.newBuilder()
                        //指定通知内容为IOS
                        .addPlatformNotification(IosNotification.newBuilder()
                                .setAlert(pushBean.getAlert())
                                .setBadge(+1)      // 应用角标
                                .setSound("default") // 声音
                                .addExtras(pushBean.getExtras())
                                .build())
                        .addPlatformNotification(AndroidNotification.newBuilder()
                                .setAlert(pushBean.getAlert())
                                .setTitle(pushBean.getTitle())
                                .addExtras(pushBean.getExtras())
                                .setIntent(pushBean.getIntent())
                                .build())
                        .build())
                .build();
        return sendPush(payload, appType);
    }

    /**
     * ios广播
     *
     * @param pushBean 推送内容
     * @return
     */
    @Override
    public boolean pushIos(PushBean pushBean, AppType appType) {
        return sendPush(PushPayload.newBuilder()
                .setPlatform(Platform.ios())
                .setAudience(Audience.all())
                .setNotification(Notification.ios(pushBean.getAlert(), pushBean.getExtras()))
                .build(), appType);
    }

    /**
     * ios通过registid推送 (一次推送最多 1000 个)
     *
     * @param pushBean  推送内容
     * @param registids 推送id
     * @return
     */
    @Override
    public boolean pushIos(PushBean pushBean, AppType appType, String... registids) {
        return sendPush(PushPayload.newBuilder()
                .setPlatform(Platform.ios())
                .setAudience(Audience.registrationId(registids))
                .setNotification(Notification.ios(pushBean.getAlert(), pushBean.getExtras()))
                .build(), appType);
    }

    /**
     * android广播
     *
     * @param pushBean 推送内容
     * @return
     */
    @Override
    public boolean pushAndroid(PushBean pushBean, AppType appType) {
        return sendPush(PushPayload.newBuilder()
                .setPlatform(Platform.android())
                .setAudience(Audience.all())
                .setNotification(Notification.android(pushBean.getAlert(), pushBean.getTitle(), pushBean.getExtras()))
                .build(), appType);
    }

    /**
     * android通过registid推送 (一次推送最多 1000 个)
     *
     * @param pushBean  推送内容
     * @param registids 推送id
     * @return
     */
    @Override
    public boolean pushAndroid(PushBean pushBean, AppType appType, String... registids) {
        boolean flag = sendPush(PushPayload.newBuilder()
                .setPlatform(Platform.android())
                .setAudience(Audience.registrationId(registids))
                .setNotification(Notification.android(pushBean.getAlert(), pushBean.getTitle(), pushBean.getExtras()))
                .build(), appType);
        return flag;
    }

    /**
     * 调用api推送
     *
     * @param pushPayload 推送实体
     * @return
     */
    @Override
    public boolean sendPush(PushPayload pushPayload, AppType appType) {
        log.info("发送极光推送请求 [{}]: {}", appType, pushPayload);
        PushResult result = null;
        try {
            JPushClient client = jPushConfig.getClient(appType);
            result = client.sendPush(pushPayload);
        } catch (APIConnectionException e) {
            log.error("极光推送连接异常 [{}]: ", appType, e);
        } catch (APIRequestException e) {
            log.error("极光推送请求异常 [{}]: ", appType, e);
        }
        if (result != null && result.isResultOK()) {
            log.info("极光推送请求成功 [{}]: {}", appType, result);
            return true;
        } else {
            log.info("极光推送请求失败 [{}]: {}", appType, result);
            return false;
        }
    }
}

