package com.guanghonggu.JPush;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.gson.JsonObject;
import com.guanghonggu.enums.AppType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * @author：zzn
 * @description：
 * @createData：2025/7/10 14:33
 */
@RestController
public class JPushController {
    @Autowired
    private MyJPushService service;

    /**
     * 广播
     * @return
     */
    @PostMapping("/jpush")
    public boolean test(@RequestBody PushBean pushBean) {
        String alert = pushBean.getAlert();
        String title = pushBean.getTitle();
        pushBean.setTitle(title);
        pushBean.setAlert(alert);
        JsonObject jsonObject = new JsonObject();
        jsonObject.addProperty("url", "intent:#Intent;action=android.intent.action.MAIN;end");
        pushBean.setIntent(jsonObject);
        return service.pushAllByRID(pushBean, AppType.USER, "190e35f7e165fd1dcf2");
    }

}
