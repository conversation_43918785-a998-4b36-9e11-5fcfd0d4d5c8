package com.guanghonggu.JPush;

import cn.jpush.api.push.model.PushPayload;
import com.guanghonggu.enums.AppType;

/**
 * @author：zzn
 * @description：
 * @createData：2025/7/10 14:20
 */
public interface JPushService {
    boolean pushAll(PushBean pushBean, AppType appType);

    boolean pushAllByRID(PushBean pushBean, AppType appType, String... registids);

    boolean pushIos(PushBean pushBean, AppType appType);

    boolean pushIos(PushBean pushBean, AppType appType, String... registids);

    boolean pushAndroid(PushBean pushBean, AppType appType);

    boolean pushAndroid(PushBean pushBean, AppType appType, String... registids);

    boolean sendPush(PushPayload pushPayload, AppType appType);
}
