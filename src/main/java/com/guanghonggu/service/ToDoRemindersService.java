package com.guanghonggu.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.guanghonggu.dto.ResultDTO;
import com.guanghonggu.dto.ToDoRemindersDTO;
import com.guanghonggu.entity.ToDoReminders;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【to_do_reminders(代办提醒表)】的数据库操作Service
* @createDate 2025-04-27 10:56:56
*/
public interface ToDoRemindersService extends IService<ToDoReminders> {

    /**
     * 获取代办列表 状态（0: 待处理, 1: 已完成, 2: 已删除）
     * @param toDoRemindersDTO status
     * @return
     */
    ResultDTO<IPage<ToDoRemindersDTO>> listTodoReminders(ToDoRemindersDTO toDoRemindersDTO);

    /**
     * 更新待办提醒状态
     * @param remindersId 待办id
     * @return
     */
    ResultDTO<String> updateTodoRemindersStatus(String remindersId);

    /**
     * 删除待办提醒记录
     * @param remindersId 待办id
     * @return
     */
    ResultDTO<String> deleteTodoReminders(String remindersId);
}
