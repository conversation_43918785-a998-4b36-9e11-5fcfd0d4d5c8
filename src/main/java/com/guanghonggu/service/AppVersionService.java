package com.guanghonggu.service;

import com.guanghonggu.dto.ResultDTO;
import com.guanghonggu.entity.AppVersion;
import com.baomidou.mybatisplus.extension.service.IService;

/**
* <AUTHOR>
* @description 针对表【app_version】的数据库操作Service
* @createDate 2025-08-01 10:52:54
*/
public interface AppVersionService extends IService<AppVersion> {

    /**
     * 获取最新版本
     * @param platform  平台（ios/android）
     * @param appType   类型（1：用户，2：阿姨）
     * @return
     */
    ResultDTO<AppVersion> getLatestVersion(String platform,Integer appType);

}
