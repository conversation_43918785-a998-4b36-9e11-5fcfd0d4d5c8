package com.guanghonggu.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.guanghonggu.dto.CaregiverServiceTypeDTO;
import com.guanghonggu.dto.OrderDTO;
import com.guanghonggu.dto.ResultDTO;
import com.guanghonggu.entity.Order;
import com.baomidou.mybatisplus.extension.service.IService;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
* <AUTHOR>
* @description 针对表【order(订单表)】的数据库操作Service
* @createDate 2025-04-27 10:56:56
*/
public interface OrderService extends IService<Order> {

    /**
     * 查询各种类型订单数量
     */
    Map<String, Integer> getOrdersCountByUserId(Long userId);


    ResultDTO<OrderDTO> orderAgain(String orderId);

    /**
     * 阿姨预约大厅列表
     * @param orderDTO
     * @return
     */
    IPage<OrderDTO> getOrderByList(OrderDTO orderDTO);

    ResultDTO<OrderDTO> getByOrderIds(Long id);

    /**
     * 阿姨完成服务
     * @param orderId
     * @return
     */
    ResultDTO<String> updateOrderById(Long orderId);


    Page<Order> getOrdersByType(OrderDTO orderDTO,Integer page,Integer size); // 获取指定类型的订单列表

    /**
     * 根据订单id查询订单详情
     * @param id
     * @return
     */
    OrderDTO getOrderById(Long id);

    /**
     * 用户查询全部订单列表
     * @return
     */
    Page<OrderDTO> getOrdersByUserIdPaged(Integer page, Integer size, Long userId);


    /**
     * 根据阿姨id，订单日期，查询订单信息
     */
    Page<OrderDTO> getOrdersByCaregiverPaged(Integer page, Integer size, Long caregiversId, Date orderDate);

    /**
     *更改订单类型
     */
    ResultDTO<Long> putStatus(OrderDTO orderDTO);

    /**
     * 删除订单
     */
    Long deleteOrderId(OrderDTO orderDTO);

    /**
     * 阿姨同意或拒绝预约
     */
    ResultDTO<?> handleOrderDecision(OrderDTO dto);

    /**
     *阿姨取消订单
     */
    ResultDTO<?> cancelOrderByCaregiver(OrderDTO dto);

    /**
     * 阿姨同意或拒绝用户修改订单
     */
    boolean updateOrderStatus(OrderDTO orderDTO);

    /**
     * 用户修改未接单或已预约状态下的订单信息
     */
    ResultDTO<Map<String,Object>> updateOrderInfo(OrderDTO orderDTO);

    /**
     * 下单
     * @param orderDTO
     * @return
     */
    ResultDTO<Map<String, Object>> orderReleased(OrderDTO orderDTO);

    void checkCouponStatus(String userCouponId, Integer serviceTypeId);

    /**
     * 用户退款
     * @param orderId
     * @return
     */
    ResultDTO<String> refundOrder(String orderId);

    /**
     * 微信支付回调函数
     * @param request
     * @return
     */
    Map<String, Object> handleCallback(HttpServletRequest request);

    /**
     * 微信支付查单
     * @param orderId   订单编号
     * @return
     */
    ResultDTO<Map<String, Object>> searchWechatByOrderNumber(String orderId);

    /**
     * 用户确认订单
     * @param orderId   订单id
     * @return
     */
    ResultDTO<String> confirmOrder(String orderId);

    /**
     * 根据订单编号查询订单
     * @param orderNo 订单号
     * @return Order
     * 支付宝
     */
    Order getOrderByOrderNo(String orderNo);

    /**
     * 查询退款详细信息
     * @param orderId
     * @return
     */
    ResultDTO<Map<String, Object>> getRefundInfo(String orderId);

    /**
     * 根据阿姨id查询阿姨未处理的预约订单
     * @return
     */
    List<OrderDTO> getAppointmentOrdersByCaregiver();

    /**
     * 根据阿姨id查询阿姨为同意or拒绝的订单
     */
    List<OrderDTO> getUpdateOrderByCaregiver();

    /**
     * 根据阿姨id查询用户取消订单的信息
     */
    List<OrderDTO> getCancelOrderByCaregiver();

    /**
     * 根据用户id查询阿姨拒绝预约单
     */
    List<OrderDTO> getRejectAppointment();

    /**
     * 阿姨确认用户删除订单按钮
     */
    boolean UpConfirmUserCancellation(OrderDTO orderDTO);
}
