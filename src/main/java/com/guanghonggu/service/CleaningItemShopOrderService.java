package com.guanghonggu.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.guanghonggu.dto.CleaningItemShopOrderDTO;
import com.guanghonggu.dto.ResultDTO;
import com.guanghonggu.entity.CleaningItemShopOrder;
import com.baomidou.mybatisplus.extension.service.IService;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

/**
* <AUTHOR>
* @description 针对表【cleaning_item_shop_order(服务人员购买清洁工具订单表)】的数据库操作Service
* @createDate 2025-07-23 11:50:43
*/
public interface CleaningItemShopOrderService extends IService<CleaningItemShopOrder> {

    /**
     * 阿姨商城下单
     * @param cleaningItemShopOrderDTO
     * @return
     */
    ResultDTO<Map<String, Object>> placeShopOrder(CleaningItemShopOrderDTO cleaningItemShopOrderDTO);

    /**
     * 下单支付回调
     * @param request
     * @return
     */
    Map<String, Object> handleCallback(HttpServletRequest request);

    /**
     * 保洁商城订单 - 微信查单接口
     * @param orderNumber 商户订单号
     * @return
     */
    ResultDTO<Map<String, Object>> searchWechatByOrderNumber(String orderNumber);

    /**
     * 查询阿姨已购的物品列表
     * @return
     */
    ResultDTO<IPage<CleaningItemShopOrderDTO>> listShopOrderByCaregiver(Integer currentPage, Integer pageSize);
}
