package com.guanghonggu.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.guanghonggu.dto.AddressDTO;
import com.guanghonggu.entity.Address;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 地址服务接口
 * 提供地址相关的增删改查等操作的定义
 */
public interface AddressService extends IService<Address> {

    // ========== 查询类 ==========

    /**
     * 根据地址id查询地址信息
     */
    Address getAddressId(AddressDTO addressDTO);

    Page<Address> getAddressesByUserId(Long userId,Integer page,Integer size,Integer userType); // 根据用户ID查询地址

    // ========== 新增与修改类 ==========

    int updateAddress(AddressDTO addressDTO); // 更新地址

    int addAddress(AddressDTO addressDTO); // 新增地址（自动设为默认逻辑）


    boolean setDefaultAddress(AddressDTO addressDTO); // 设置默认地址

    // ========== 删除类 ==========

    int deleteAddress(AddressDTO addressDTO); // 删除单条地址

}
