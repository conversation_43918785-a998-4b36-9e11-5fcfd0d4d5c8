package com.guanghonggu.service;

import com.guanghonggu.dto.ComplaintDTO;
import com.guanghonggu.dto.ResultDTO;
import com.guanghonggu.entity.Complaint;
import com.baomidou.mybatisplus.extension.service.IService;

/**
* <AUTHOR>
* @description 针对表【complaint(投诉表)】的数据库操作Service
* @createDate 2025-04-27 10:56:56
*/
public interface ComplaintService extends IService<Complaint> {

    /*
    * 用户投诉
    * */
    ResultDTO<String> addComplaint(ComplaintDTO complaintDTO);

}
