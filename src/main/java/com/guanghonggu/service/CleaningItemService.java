package com.guanghonggu.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.guanghonggu.dto.CleaningItemDTO;
import com.guanghonggu.dto.ResultDTO;
import com.guanghonggu.entity.CleaningItem;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【cleaning_item(清洁工具与清洁剂表)】的数据库操作Service
* @createDate 2025-07-03 09:17:50
*/
public interface CleaningItemService extends IService<CleaningItem> {

    /**
     * 根据清洁类别查询清洁工具
     * @param cleaningItemDTO 根据categoryId查询
     * @return
     */
    ResultDTO<IPage<CleaningItemDTO>> listByCategoryId(CleaningItemDTO cleaningItemDTO);

    /**
     * 根据id查询详细信息，包括图片
     * @param itemId 项目id
     * @return
     */
    ResultDTO<CleaningItemDTO> getCleaningItemById(String itemId);

}
