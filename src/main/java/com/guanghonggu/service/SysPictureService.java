package com.guanghonggu.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.guanghonggu.dto.ResultDTO;
import com.guanghonggu.dto.SysPictureDTO;
import com.guanghonggu.entity.SysPicture;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【sys_picture】的数据库操作Service
* @createDate 2025-05-12 11:01:20
*/
public interface SysPictureService extends IService<SysPicture> {

    /**
     * 获取系统图片列表
     * @param pictureType 图片类型
     * @return
     */
    ResultDTO<List<SysPictureDTO>> listPictures(Integer pictureType);
}
