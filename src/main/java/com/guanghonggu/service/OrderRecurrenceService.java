package com.guanghonggu.service;

import com.guanghonggu.dto.OrderRecurrenceDTO;
import com.guanghonggu.dto.ResultDTO;
import com.baomidou.mybatisplus.extension.service.IService;
import com.guanghonggu.entity.OrderRecurrence;

/**
* <AUTHOR>
* @description 针对表【order_recurrence(周期订单规则表)】的数据库操作Service
* @createDate 2025-08-18 16:13:16
*/
public interface OrderRecurrenceService extends IService<OrderRecurrence> {

    /**
     * 修改周期订单规则
     * @param orderRecurrenceDTO
     * @return
     */
    ResultDTO<String> updateOrderRecurrence(OrderRecurrenceDTO orderRecurrenceDTO);

    /**
     * 取消周期订单
     * @param orderRecurrenceDTO
     * @return
     */
    ResultDTO<String> cancelOrderRecurrence(OrderRecurrenceDTO orderRecurrenceDTO);

    void settleOrder(OrderRecurrence recurrence);

}
