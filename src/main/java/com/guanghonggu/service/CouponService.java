package com.guanghonggu.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.guanghonggu.dto.CouponDTO;
import com.guanghonggu.dto.ResultDTO;
import com.guanghonggu.entity.Coupon;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;


/**
* <AUTHOR>
* @description 针对表【coupon(优惠卷表)】的数据库操作Service
* @createDate 2025-04-27 10:56:56
*/
public interface CouponService extends IService<Coupon> {

    /**
     * 获取用户优惠卷列表
     * @return
     */
    ResultDTO<IPage<CouponDTO>> listUserCoupon(CouponDTO couponDTO);

    /**
     * 发布优惠券（管理端）
     */
    void publishCoupon();

    /**
     * 领取优惠券
     * @param couponId 优惠券id
     * @return
     */
    ResultDTO<String> receiveCoupon(String couponId);

    /**
     * 获取优惠券详情
     * @param couponId  优惠券id
     * @return
     */
    ResultDTO<CouponDTO> getCoupon(String couponId);


    ResultDTO<String> issueNewUserCoupon(Long userId);
}
