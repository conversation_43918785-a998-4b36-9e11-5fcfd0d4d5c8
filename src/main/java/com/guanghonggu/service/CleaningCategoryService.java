package com.guanghonggu.service;

import com.guanghonggu.dto.ResultDTO;
import com.guanghonggu.entity.CleaningCategory;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【cleaning_category(清洁分类表)】的数据库操作Service
* @createDate 2025-07-03 09:17:50
*/
public interface CleaningCategoryService extends IService<CleaningCategory> {

    /**
     * 获取清洁商品分类
     * @return
     */
    ResultDTO<List<CleaningCategory>> listAllCategories();

}
