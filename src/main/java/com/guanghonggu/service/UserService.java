package com.guanghonggu.service;

import com.guanghonggu.dto.LoginDTO;
import com.guanghonggu.dto.MobileDTO;
import com.guanghonggu.dto.StatisticsDTO;
import com.guanghonggu.dto.ResultDTO;
import com.guanghonggu.dto.UserDTO;
import com.guanghonggu.entity.User;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【user(用户表)】的数据库操作Service
* @createDate 2025-04-27 10:56:56
*/
public interface UserService extends IService<User> {

    User selectByPhone(String phoneNumber);


    void insert(User user);


    User selectByOpenId(String openid);

    /**
     * 修改密码（内部使用）
     */
    int putPassword(UserDTO userDTO);

    int deleteUserPayPassword(String phoneNumber);

    ResultDTO<LoginDTO> getMobile(MobileDTO mobileDTO);

    ResultDTO<String> wxLoginCallback(String code);

    /**
     * 更新极光推送id
     * @param userDTO
     * @return
     */
    ResultDTO<String> updateRegistrationId(UserDTO userDTO);
}


