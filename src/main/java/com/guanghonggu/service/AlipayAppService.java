package com.guanghonggu.service;

import com.guanghonggu.dto.ResultDTO;
import com.guanghonggu.dto.WalletTransactionDTO;
import com.guanghonggu.entity.WalletTransaction;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

/**
 * @author：zzn
 * @description：
 * @createData：2025/6/19 13:44
 */
public interface AlipayAppService {
    ResultDTO<Map<String, Object>> searchAlipayByOrderId(String orderNumber);

    String handleRechargeNotify(HttpServletRequest request);

    void handleSuccessfulRecharge(WalletTransaction tx);

    ResultDTO<?> withdraw(WalletTransactionDTO dto);
}
