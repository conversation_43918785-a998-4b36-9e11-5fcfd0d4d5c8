package com.guanghonggu.service;

import com.guanghonggu.dto.CaregiverScheduleDTO;
import com.guanghonggu.dto.ResultDTO;
import com.guanghonggu.entity.CaregiverSchedule;
import com.baomidou.mybatisplus.extension.service.IService;

import java.sql.Time;
import java.time.LocalTime;
import java.util.Date;
import java.util.List;

import java.util.Map;

/**
* <AUTHOR>
* @description 针对表【caregiver_schedule(阿姨日程表)】的数据库操作Service
* @createDate 2025-04-27 10:56:56
*/
public interface CaregiverScheduleService extends IService<CaregiverSchedule> {
    /**
     * 根据阿姨ID查询该阿姨所有排班记录
     */
    List<CaregiverSchedule> getScheduleListByCaregiverId(CaregiverScheduleDTO caregiverScheduleDTO);

    /**
     * 删除指定日期的排班（可能是不可接单记录或系统安排记录）
     */
    Long deleteCaregiverSchedule(CaregiverScheduleDTO caregiverScheduleDTO);

    /**
     * 修改某条排班记录（如修改起止时间）
     */
    int updateCaregiverSchedule(CaregiverScheduleDTO caregiverScheduleDTO);

    /**
     * 根据阿姨ID 和 日期 查询该日的所有排班记录（可能有多个小时段）
     */
    ResultDTO<List<CaregiverSchedule>> getScheduleDate(CaregiverScheduleDTO dto);

    /**
     * 阿姨接单
     */
    ResultDTO<?> addOrderSchedule(CaregiverScheduleDTO dto);

    /**
     * 设置日程类型
     */
    ResultDTO<?> toggleScheduleStatus(CaregiverScheduleDTO caregiverScheduleDTO);

    /**
     * 添加“不可接单时间段”记录，表示阿姨手动设置休息（schedule_type = 0）
     */
    ResultDTO<?> blockSchedule(CaregiverScheduleDTO dto);

    /**
     * 查询某个日期下阿姨设置为“不可接单”的所有小时（hourList）
     */
    ResultDTO<List<Map<String, Object>>> getBlockedHourList(CaregiverScheduleDTO dto);

    /**
     * 判断某个时间段是否与当前已有排班冲突（包括安排中和不可接单记录）
     */
    boolean hasConflictSchedule(Long caregiverId, Date scheduleDate, LocalTime startTime, LocalTime endTime);

    /**
     * 设置阿姨下班时间
     */
    void setOffDutyTime(CaregiverScheduleDTO caregiverScheduleDTO);

    /**
     * 预约阿姨
     * @param caregiverScheduleDTO
     * @return
     */
    ResultDTO<Map<String, Object>> appointmentCaregiver(CaregiverScheduleDTO caregiverScheduleDTO);
}
