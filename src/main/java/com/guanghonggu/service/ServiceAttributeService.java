package com.guanghonggu.service;

import com.guanghonggu.dto.ServiceAttributeDTO;
import com.guanghonggu.entity.ServiceAttribute;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【service_attribute】的数据库操作Service
* @createDate 2025-05-23 14:42:23
*/
public interface ServiceAttributeService extends IService<ServiceAttribute> {

    List<ServiceAttributeDTO> getAttributesByServiceType(String serviceTypeCode);
}
