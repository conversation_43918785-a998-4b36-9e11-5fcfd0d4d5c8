package com.guanghonggu.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.guanghonggu.entity.CleaningItemShopOrderDetail;
import com.guanghonggu.service.CleaningItemShopOrderDetailService;
import com.guanghonggu.mapper.CleaningItemShopOrderDetailMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【cleaning_item_shop_order_detail(订单商品明细表)】的数据库操作Service实现
* @createDate 2025-07-24 14:49:32
*/
@Service
public class CleaningItemShopOrderDetailServiceImpl extends ServiceImpl<CleaningItemShopOrderDetailMapper, CleaningItemShopOrderDetail>
    implements CleaningItemShopOrderDetailService{

}




