package com.guanghonggu.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.guanghonggu.dto.EvaluationsDTO;
import com.guanghonggu.entity.Caregiver;
import com.guanghonggu.entity.Evaluations;
import com.guanghonggu.mapper.CaregiverMapper;
import com.guanghonggu.service.CaregiverService;
import com.guanghonggu.service.EvaluationsService;
import com.guanghonggu.mapper.EvaluationsMapper;
import com.guanghonggu.service.ResourceService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【evaluations(评价表)】的数据库操作Service实现
 * @createDate 2025-04-27 10:56:56
 */
@Service
public class EvaluationsServiceImpl extends ServiceImpl<EvaluationsMapper, Evaluations>
        implements EvaluationsService {
    @Autowired
    private EvaluationsMapper evaluationsMapper;
    @Autowired
    private ResourceService resourceService;
    @Autowired
    private CaregiverMapper caregiverMapper;

//    查询所有评价
    @Override
    public Page<Evaluations> getAllEvaluationsPaged(Integer page, Integer size) {
        Page<Evaluations> pageObj = new Page<>(page, size);
        QueryWrapper<Evaluations> queryWrapper = new QueryWrapper<>();
        return evaluationsMapper.selectPage(pageObj, queryWrapper);
    }

    /**
     * 根据阿姨id查询
     */
    @Override
    public Evaluations getEvaluatedId(EvaluationsDTO evaluationsDTO){
        QueryWrapper<Evaluations> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("evaluated_id",evaluationsDTO.getEvaluatedId());
        return evaluationsMapper.selectOne(queryWrapper);
    }

    @Override
    public Page<Evaluations> getEvaluationsPaged(Integer page, Integer size, Long evaluatorsId) {
        Page<Evaluations> pageObj = new Page<>(page, size);
        QueryWrapper<Evaluations> wrapper = new QueryWrapper<>();
        wrapper.eq("evaluators_id", evaluatorsId); // 添加条件：评价人ID匹配
        return evaluationsMapper.selectPage(pageObj, wrapper);
    }

    @Override
    public Evaluations getEvaluationsId(Long evaluatorsId) {
        return evaluationsMapper.selectById(evaluatorsId);
    }


    @Override
    public Long addEvaluationAndReturnId(EvaluationsDTO dto) {
        // 创建 Evaluation 对象
        Evaluations evaluation = new Evaluations();

        // 将 DTO 中的数据拷贝到 Evaluation 对象
        BeanUtils.copyProperties(dto, evaluation,"evaluations_id");

        // 获取被评价的 Caregiver ID（即 Evaluated_id）
        Long evaluatedId = dto.getEvaluatedId();

        // 1. 根据 Evaluated_id 获取该 Caregiver 的所有评价
        List<Evaluations> evaluationsList = evaluationsMapper.selectList(
                new QueryWrapper<Evaluations>().eq("evaluated_id", evaluatedId)
        );

        // 2. 计算整体评分（overall_evaluation、service_attitude、cleanliness_level）
        double totalScore = 0;
        int count = 0;

        for (Evaluations eval : evaluationsList) {
            // 累加每条评价的评分
            totalScore += eval.getOverallEvaluation() != null ? eval.getOverallEvaluation() : 0;
            totalScore += eval.getServiceAttitude() != null ? eval.getServiceAttitude() : 0;
            totalScore += eval.getCleanlinessLevel() != null ? eval.getCleanlinessLevel() : 0;
            count += 3; // 每条评价有三个评分项
        }

        // 3. 计算平均分
        double avgScore = count > 0 ? totalScore / count : 0;

        // 4. 更新 Caregiver 表的 avg_evaluation_score 字段
        LambdaUpdateWrapper<Caregiver> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(Caregiver::getCaregiverId, evaluatedId)  // 根据 Caregiver ID 来更新
                .set(Caregiver::getAvgEvaluationScore, avgScore);  // 更新 avg_evaluation_score 字段

        // 执行更新操作
        int rowsAffected = caregiverMapper.update(null, wrapper);  // 使用 null 因为更新通过 wrapper 来指定

        // 判断是否成功更新
        if (rowsAffected == 0) {
            throw new RuntimeException("更新 Caregiver 的 avg_evaluation_score 失败");
        }

        // 5. 将评价插入到 Evaluations 表
        evaluationsMapper.insert(evaluation);

        // 返回插入的评价 ID
        return evaluation.getEvaluationsId();
    }


    @Override
    public long deleteEvaluationsId(EvaluationsDTO evaluationsDTO) {
        Long evaluationsId = evaluationsDTO.getEvaluatedId();
        Long userId = evaluationsDTO.getUserId();

        // 第一步：非空校验
        if (evaluationsId == null || userId == null) {
            throw new IllegalArgumentException("评价ID或用户ID不能为空");
        }

        // 第二步：查评价记录，校验是否属于当前用户
        Evaluations evaluations = evaluationsMapper.selectById(evaluationsId);
        if (evaluations == null) {
            throw new RuntimeException("评价不存在");
        }

        if (!userId.equals(evaluations.getEvaluatorsId())) {
            throw new RuntimeException("无权删除他人的评价");
        }

        // 第三步：删除资源记录（resource + OSS 文件）
        resourceService.deleteByEvaluationId(evaluationsId);

        // 第四步：删除 evaluations 主记录
        int result = evaluationsMapper.deleteById(evaluationsId);
        if (result == 0) {
            throw new RuntimeException("删除失败：评价ID不存在");
        }

        // 第五步：返回评价ID给 Controller
        return evaluationsId;
    }
}