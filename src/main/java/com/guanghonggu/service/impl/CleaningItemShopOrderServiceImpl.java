package com.guanghonggu.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.guanghonggu.dto.CleaningItemShopOrderDTO;
import com.guanghonggu.dto.CleaningItemShopOrderDetailDTO;
import com.guanghonggu.dto.LoginUserDTO;
import com.guanghonggu.dto.ResultDTO;
import com.guanghonggu.entity.AdminUser;
import com.guanghonggu.entity.CleaningItem;
import com.guanghonggu.entity.CleaningItemShopOrder;
import com.guanghonggu.entity.CleaningItemShopOrderDetail;
import com.guanghonggu.entity.Wallet;
import com.guanghonggu.entity.WalletTransaction;
import com.guanghonggu.enums.WebSocketMessageType;
import com.guanghonggu.handler.exception.BizException;
import com.guanghonggu.mapper.AdminUserMapper;
import com.guanghonggu.mapper.CleaningItemMapper;
import com.guanghonggu.mapper.CleaningItemShopOrderDetailMapper;
import com.guanghonggu.mapper.WalletMapper;
import com.guanghonggu.mapper.WalletTransactionMapper;
import com.guanghonggu.service.CleaningItemShopOrderService;
import com.guanghonggu.mapper.CleaningItemShopOrderMapper;
import com.guanghonggu.strategy.pay.strategy.PayContext;
import com.guanghonggu.strategy.pay.strategy.PayQueryHandler;
import com.guanghonggu.strategy.pay.strategy.PayStrategy;
import com.guanghonggu.strategy.pay.wechat.WechatAPI;
import com.guanghonggu.websocket.WebSocketServer;
import com.wechat.pay.java.service.payments.model.Transaction;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【cleaning_item_shop_order(服务人员购买清洁工具订单表)】的数据库操作Service实现
 * @createDate 2025-07-23 11:50:43
 */
@Service
public class CleaningItemShopOrderServiceImpl extends ServiceImpl<CleaningItemShopOrderMapper, CleaningItemShopOrder>
        implements CleaningItemShopOrderService {

    @Autowired
    private CleaningItemMapper cleaningItemMapper;

    @Autowired
    private WalletMapper walletMapper;

    @Autowired
    private WalletTransactionMapper walletTransactionMapper;

    @Autowired
    private PayContext payContext;

    @Autowired
    private CleaningItemShopOrderMapper cleaningItemShopOrderMapper;

    @Autowired
    private PayQueryHandler payQueryHandler;

    @Autowired
    private WechatAPI wechatAPI;

    @Autowired
    private CleaningItemShopOrderDetailMapper cleaningItemShopOrderDetailMapper;
    @Autowired
    private WebSocketServer webSocketServer;

    @Autowired
    private AdminUserMapper adminUserMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultDTO<Map<String, Object>> placeShopOrder(CleaningItemShopOrderDTO request) {
        List<CleaningItemShopOrderDetailDTO> commodityList = request.getCommodityList();
        if (CollectionUtils.isEmpty(commodityList)) {
            throw new BizException("请选择要购买的商品");
        }
        // 总价、收货地址、支付方式、备注、支付密码
        BigDecimal totalPrice = request.getTotalPrice();
        Long addressId = request.getAddressId();
        Integer paymentMethod = request.getPaymentMethod();
        String remark = request.getRemark();
        String payPassword = request.getPayPassword();

        Long userId;
        Integer userType;
        String phone;
        // app跳转小程序下单
        if (request.getUserId() != null) {
            userId  = request.getUserId();
            userType = request.getUserType();
            phone = request.getPhone();
        } else {
            // 获取当前登录用户信息
            String loginIdAsString = StpUtil.getLoginIdAsString();
            LoginUserDTO loginInfo = (LoginUserDTO) StpUtil.getSession().get(loginIdAsString);
            phone = loginInfo.getPhone();
            userId = loginInfo.getUserId();
            userType = loginInfo.getUserType();
        }

        // 获取钱包信息
        LambdaQueryWrapper<Wallet> walletLambdaQueryWrapper = new LambdaQueryWrapper<>();
        walletLambdaQueryWrapper.eq(Wallet::getUserId, userId)
                .eq(Wallet::getUserType, userType);
        Wallet wallet = walletMapper.selectOne(walletLambdaQueryWrapper);
        Long walletId = wallet.getWalletId();


        for (CleaningItemShopOrderDetailDTO dto : commodityList) {
            CleaningItem item = cleaningItemMapper.selectById(dto.getItemId());
            if (item == null) throw new BizException("商品不存在");
            BigDecimal unitPrice = item.getPrice(); // 使用商品当前价格
            String name = item.getName();
            BigDecimal itemTotal = unitPrice.multiply(BigDecimal.valueOf(dto.getQuantity()));
            dto.setUnitPrice(unitPrice);
            dto.setTotalPrice(itemTotal);
            dto.setCommodityName(name);
//            totalPrice = totalPrice.add(itemTotal);
        }
        // 生成订单编号
        String orderNumber = OrderServiceImpl.generateOrderNumber("20", phone);
        CleaningItemShopOrder order = new CleaningItemShopOrder();
        order.setOrderNumber(orderNumber);
        order.setCaregiverId(userId);
        order.setTotalPrice(totalPrice);
        order.setAddressId(addressId);
        order.setStatus(4);
        order.setPaymentMethod(paymentMethod);
        order.setRemark(remark);
        order.setView(0);
        int insert = cleaningItemShopOrderMapper.insert(order);
        if (insert <= 0) {
            throw new BizException("购买失败");
        }

        // 插入订单明细表
        for (CleaningItemShopOrderDetailDTO dto : commodityList) {
            CleaningItemShopOrderDetail detail = new CleaningItemShopOrderDetail();
            detail.setShopOrderId(order.getId());
            detail.setItemId(dto.getItemId());
            detail.setQuantity(dto.getQuantity());
            detail.setItemName(dto.getCommodityName());
            detail.setUnitPrice(dto.getUnitPrice());
            detail.setTotalPrice(dto.getTotalPrice());
            cleaningItemShopOrderDetailMapper.insert(detail);
        }

        WalletTransaction walletTransaction = new WalletTransaction();
        walletTransaction.setWalletId(walletId);
        walletTransaction.setTransactionType(2);
        walletTransaction.setAmount(totalPrice);
        walletTransaction.setTransactionTime(new Date());
        walletTransaction.setOutTradeNo(order.getOrderNumber());
        walletTransaction.setStatus(0);
        walletTransaction.setPaymentChannel(paymentMethod);
        walletTransactionMapper.insert(walletTransaction);

        // 注意支付方式、支付密码
        // 获取钱包/微信/支付宝实例对象
        PayStrategy payStrategy = payContext.executePay(paymentMethod);
        // 调用钱包/微信/支付宝支付API
        HashMap<String, Object> map = new HashMap<>();
        map.put("userId", userId);
        map.put("userType", userType);
        map.put("payPassword", payPassword);
//        map.put("orderId", order.getOrderId());
        map.put("orderNumber", order.getOrderNumber());
        map.put("transactionType", 2); // 交易类型
        map.put("actualPaymentPrice", totalPrice); // 实付金额
        map.put("description", "物品购买");
        map.put("payMethods", paymentMethod);
        map.put("code", request.getCode());
        Map<String, Object> pay = payStrategy.pay(map);
        pay.get("walletPayAll");
        if (paymentMethod == 3) {
            // 更改订单状态，交易记录
            payQueryHandler.walletPayUpdateTransaction(pay, order.getOrderNumber(), 1);
            payQueryHandler.walletPayUpdateShopOrder(order.getId());
            // 推送管理端
            sendMessageToAdminUser(order.getId().toString(), orderNumber);
        }
        if (paymentMethod == 4 || paymentMethod == 5 || paymentMethod == 7) {
            Boolean walletPayAll = (Boolean) pay.get("walletPayAll");
            if (walletPayAll != null) {
                // 钱包全部支付
                payQueryHandler.walletPayUpdateTransaction(pay, order.getOrderNumber(), 1);
                payQueryHandler.walletPayUpdateShopOrder(order.getId());
                // 推送管理端
                sendMessageToAdminUser(order.getId().toString(), orderNumber);
            } else {
                payQueryHandler.walletPayUpdateTransaction(pay, order.getOrderNumber(), null);
            }
        }

        return ResultDTO.success("下单成功", pay);
    }

    @Override
    public Map<String, Object> handleCallback(HttpServletRequest request) {
        PayStrategy payStrategy = payContext.executePay(1); // 1 = 微信
        Transaction transaction = payStrategy.handleCallback(request);

        String tradeState = transaction.getTradeState().name();
        String outTradeNo = transaction.getOutTradeNo();

        CleaningItemShopOrder order = payQueryHandler.getShopOrderInfo(outTradeNo);
        Map<String, Object> resMap = new HashMap<>();
        resMap.put("code", 200);

        if (order == null) {
            log.error("微信回调 - 未找到订单，订单号: " + outTradeNo);
            return resMap;
        }

        switch (tradeState) {
            case "SUCCESS":
                payQueryHandler.handleShopPaySuccess(order, transaction);
                // 推送管理端
                sendMessageToAdminUser(order.getId().toString(), outTradeNo);
                break;
            case "CLOSED":
                payQueryHandler.handleShopPayClosed(order, transaction);
                break;
            default:
                break;
        }
        return resMap;
    }

    @Override
    public ResultDTO<Map<String, Object>> searchWechatByOrderNumber(String orderNumber) {
        Transaction transaction = wechatAPI.searchOrder(orderNumber);
        String tradeState = transaction.getTradeState().name();

        CleaningItemShopOrder order = payQueryHandler.getShopOrderInfo(orderNumber);
        if (order == null) {
            return ResultDTO.error("订单不存在");
        }

        switch (tradeState) {
            case "SUCCESS":
                payQueryHandler.handleShopPaySuccess(order, transaction);
                break;
            case "CLOSED":
                payQueryHandler.handleShopPayClosed(order, transaction);
                break;
            default:
                break;
        }
        HashMap<String, Object> map = new HashMap<>();
        map.put("status", "SUCCESS");
        return ResultDTO.success("查询成功", map);
    }

    @Override
    public ResultDTO<IPage<CleaningItemShopOrderDTO>> listShopOrderByCaregiver(Integer currentPage, Integer pageSize) {
        String loginIdAsString = StpUtil.getLoginIdAsString();
        LoginUserDTO loginInfo = (LoginUserDTO) StpUtil.getSession().get(loginIdAsString);
        Long caregiverId = loginInfo.getUserId();

        Page<CleaningItemShopOrderDTO> page = new Page<>(currentPage, pageSize);

        IPage<CleaningItemShopOrderDTO> orderPage = cleaningItemShopOrderMapper.selectShopOrderPage(page, caregiverId);

        List<CleaningItemShopOrderDTO> orders = orderPage.getRecords();
        if (CollectionUtils.isEmpty(orders)) {
            return ResultDTO.success("查询成功", orderPage);
        }

        List<Long> orderIds = orders.stream().map(CleaningItemShopOrderDTO::getId).collect(Collectors.toList());
        List<CleaningItemShopOrderDetailDTO> details = cleaningItemShopOrderMapper.selectDetailsByOrderIds(orderIds);

        // 根据订单ID进行分组
        Map<Long, List<CleaningItemShopOrderDetailDTO>> detailMap = details.stream()
                .collect(Collectors.groupingBy(CleaningItemShopOrderDetailDTO::getShopOrderId));

        // 组装商品明细到订单中
        for (CleaningItemShopOrderDTO order : orders) {
            order.setCommodityList(detailMap.getOrDefault(order.getId(), new ArrayList<>()));
        }

        return ResultDTO.success("查询成功", orderPage);
    }

    /**
     * 商城下单推送管理端
     * @param orderId
     * @param orderNumber
     */
    public void sendMessageToAdminUser(String orderId, String orderNumber) {
        LambdaQueryWrapper<AdminUser> adminUserLambdaQueryWrapper = new LambdaQueryWrapper<>();
        List<AdminUser> adminUsers = adminUserMapper.selectList(adminUserLambdaQueryWrapper);
        for (AdminUser adminUser : adminUsers) {
            Long id = adminUser.getId();
            JSONObject data = new JSONObject();
            data.put("orderId", orderId);
            data.put("orderNumber", orderNumber);
            webSocketServer.sendMessageToTarget("management:" + id, data, WebSocketMessageType.SHOP_ORDER_TO_MANAGEMENT.getType());
        }
    }
}




