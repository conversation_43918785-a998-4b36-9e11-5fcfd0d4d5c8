package com.guanghonggu.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.guanghonggu.entity.UserCoupon;
import com.guanghonggu.service.UserCouponService;
import com.guanghonggu.mapper.UserCouponMapper;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description 针对表【user_coupon(用户优惠卷表)】的数据库操作Service实现
 * @createDate 2025-04-27 10:56:56
 */
@Service
public class UserCouponServiceImpl extends ServiceImpl<UserCouponMapper, UserCoupon>
        implements UserCouponService {

}




