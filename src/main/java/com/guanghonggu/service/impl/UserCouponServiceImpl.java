package com.guanghonggu.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.guanghonggu.dto.ResultDTO;
import com.guanghonggu.dto.UserCouponDTO;
import com.guanghonggu.entity.Coupon;
import com.guanghonggu.entity.User;
import com.guanghonggu.entity.UserCoupon;
import com.guanghonggu.mapper.CouponMapper;
import com.guanghonggu.mapper.UserMapper;
import com.guanghonggu.service.UserCouponService;
import com.guanghonggu.mapper.UserCouponMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * <AUTHOR>
 * @description 针对表【user_coupon(用户优惠卷表)】的数据库操作Service实现
 * @createDate 2025-04-27 10:56:56
 */
@Service
public class UserCouponServiceImpl extends ServiceImpl<UserCouponMapper, UserCoupon>
        implements UserCouponService {

    @Autowired
    private CouponMapper couponMapper;
    @Autowired
    private UserCouponMapper userCouponMapper;
    @Autowired
    private UserMapper userMapper;

    @Override
    public ResultDTO<UserCouponDTO> grantRandomUserCoupon(UserCouponDTO userCouponDTO) {
        if (userCouponDTO.getUserId() == null) {
            return ResultDTO.error("老用户id不能为空");
        }
        if (userCouponDTO.getInviteeId() == null) {
            return ResultDTO.error("新用户id不能为空");
        }
        if (userCouponDTO.getUserId().equals(userCouponDTO.getInviteeId())) {
            return ResultDTO.error("不能邀请自己");
        }
        User user = new User();
        user.setUserId(userCouponDTO.getInviteeId());
        user.setUserSource(4);
        userMapper.updateById(user);

        Long couponId = 12L;
        Coupon coupon = couponMapper.selectById(couponId);
        if (coupon == null || coupon.getIsDelete() == 1 || coupon.getRemainingQuantity() <= 0) {
            return ResultDTO.error("优惠券已删除，或不存在");
        }

        Date collectStartTime = coupon.getCollectStartTime();
        Date collectEndTime = coupon.getCollectEndTime();
        Date currentDate = new Date();
        if (collectStartTime != null && collectStartTime.after(currentDate)) {
            return ResultDTO.error("领取时间未开始");  // 优惠券未开始
        }
        if (collectEndTime != null && collectEndTime.before(currentDate)) {
            return ResultDTO.error("领取时间已过期");  // 优惠券已过期
        }

        int updated = couponMapper.update(null,
                new UpdateWrapper<Coupon>()
                        .setSql("remaining_quantity = remaining_quantity - 1")
                        .eq("coupon_id", couponId)
                        .gt("remaining_quantity", 0)
        );
        if (updated != 1) {
            return ResultDTO.error("优惠券已发完");
        }
        UserCoupon userCoupon = new UserCoupon();
        userCoupon.setUserId(userCouponDTO.getUserId());
        userCoupon.setCouponId(couponId);
        userCoupon.setAcquireTime(currentDate);
        userCoupon.setExpirationTime(coupon.getValidEndTime());
        userCoupon.setStatus(1);
        userCoupon.setCreateTime(currentDate);
        userCoupon.setUpdateTime(currentDate);
        userCouponMapper.insert(userCoupon);

        return ResultDTO.notDataSuccess("领取成功");
    }
}




