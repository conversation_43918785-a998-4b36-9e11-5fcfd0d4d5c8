package com.guanghonggu.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.guanghonggu.dto.LoginUserDTO;
import com.guanghonggu.dto.ResultDTO;
import com.guanghonggu.dto.WalletDTO;
import com.guanghonggu.entity.Wallet;
import com.guanghonggu.entity.WalletMonthlySummary;
import com.guanghonggu.entity.WalletTransaction;
import com.guanghonggu.mapper.WalletMonthlySummaryMapper;
import com.guanghonggu.mapper.WalletTransactionMapper;
import com.guanghonggu.service.WalletService;
import com.guanghonggu.mapper.WalletMapper;
import com.guanghonggu.strategy.pay.strategy.PayContext;
import com.guanghonggu.strategy.pay.strategy.PayQueryHandler;
import com.guanghonggu.strategy.pay.strategy.PayStrategy;
import com.guanghonggu.strategy.pay.wechat.transfer.GetTransferBillByOutNo;
import com.guanghonggu.strategy.pay.wechat.transfer.WechatTransferNotify;
import com.guanghonggu.strategy.user.UserByTypeStrategyContext;
import com.guanghonggu.strategy.user.UserByTypeStrategyInterface;
import com.wechat.pay.java.service.payments.model.Transaction;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 针对表【wallet(钱包表)】的数据库操作Service实现
 * @createDate 2025-04-27 10:56:56
 */
@Slf4j
@Service
public class WalletServiceImpl extends ServiceImpl<WalletMapper, Wallet>
        implements WalletService {

    @Autowired
    private WalletMapper walletMapper; // 操作 wallet 表

    @Autowired
    private UserByTypeStrategyContext userByTypeStrategyContext;


    @Autowired
    private WalletTransactionMapper walletTransactionMapper;

    @Autowired
    private PayContext payContext;

    @Autowired
    private PayQueryHandler payQueryHandler;

    @Autowired
    private WechatTransferNotify wechatTransferNotify;

    @Autowired
    private WalletMonthlySummaryMapper walletMonthlySummaryMapper;


    @Override
    public ResultDTO<Wallet> getBalance() {
        String loginIdAsString = StpUtil.getLoginIdAsString();
        LoginUserDTO loginInfo = (LoginUserDTO) StpUtil.getSession().get(loginIdAsString);
        Long userId = loginInfo.getUserId();
        Integer userType = loginInfo.getUserType();
        UserByTypeStrategyInterface typeInstance = userByTypeStrategyContext.getTypeInstance(userType);
        return typeInstance.getBalance(userId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultDTO<Map<String, Object>> withdrawToPlatform(WalletDTO dto) {
        Long userId;
        Integer userType;
        String code = "";
        String openId;
        if (dto.getUserId() != null) {
            userId = dto.getUserId();
            userType = dto.getUserType();
            code = dto.getCode();
            JSONObject json = payQueryHandler.getWechatMiniOpenIdByCode(code);
            openId = json.getString("openid"); // 获取 openid

        } else {
            String loginIdAsString = StpUtil.getLoginIdAsString();
            LoginUserDTO loginInfo = (LoginUserDTO) StpUtil.getSession().get(loginIdAsString);
            userId = loginInfo.getUserId();
            userType = loginInfo.getUserType();
            // 获取用户openId
            UserByTypeStrategyInterface typeInstance = userByTypeStrategyContext.getTypeInstance(userType);
            openId = typeInstance.getOpenId(userId);
            if (openId == null) {
                return ResultDTO.error("未绑定微信");
            }
        }

        // 获取钱包并加行级锁
        Wallet wallet = walletMapper.selectWalletByUserForUpdate(userId, userType);
        if (wallet == null) {
            return ResultDTO.error("未找到钱包");
        }

        // 提现相关金额
        BigDecimal totalWithdrawAmount = dto.getBalance(); // 总提现金额（含税）
//        BigDecimal taxBalance = dto.getTaxBalance() != null ? dto.getTaxBalance() : BigDecimal.ZERO;
//        BigDecimal actualTransferAmount = totalWithdrawAmount.subtract(taxBalance); // 实际到账金额

        BigDecimal balance = wallet.getBalance() != null ? wallet.getBalance() : BigDecimal.ZERO;

        // 校验余额是否足够
        if (balance.compareTo(totalWithdrawAmount) < 0) {
            return ResultDTO.error("余额不足");
        }

        // 扣减钱包余额（扣总金额）
        wallet.setBalance(balance.subtract(totalWithdrawAmount));
        walletMapper.updateById(wallet);

        // 生成提现交易单号
        String outTradeNo = generateRechargeOrderNo(userId, 3);

        // 提现渠道判断
        Integer withdrawType = dto.getWithdrawType();
        int paymentChannel = (withdrawType == 1 || withdrawType == 3) ? 1 : withdrawType;

        // 记录交易流水（记录总金额）
        WalletTransaction transaction = new WalletTransaction();
        transaction.setWalletId(wallet.getWalletId());
        transaction.setTransactionType(3); // 提现
        transaction.setAmount(totalWithdrawAmount); // 总提现金额（含税）
        transaction.setTransactionTime(new Date());
        transaction.setStatus(0); // 待处理
        transaction.setOutTradeNo(outTradeNo);
        transaction.setPaymentChannel(paymentChannel);
        walletTransactionMapper.insert(transaction);

        // 构建支付参数（用于微信/支付宝提现）
        HashMap<String, Object> map = new HashMap<>();
        map.put("outBillNo", outTradeNo);
        map.put("openid", openId);
        map.put("transferAmount", totalWithdrawAmount); // 实际转账金额
        map.put("code", code);
        map.put("transferRemark", "提现");
        map.put("withdrawType", withdrawType);

        // 调用支付策略进行提现
        PayStrategy payStrategy = payContext.executePay(paymentChannel);
        Map<String, Object> withdraw = payStrategy.withdraw(map);

        String transferBillNo = String.valueOf(withdraw.get("transferBillNo"));
        String stateName = String.valueOf(withdraw.get("stateName"));
        String createTime = String.valueOf(withdraw.get("createTime"));
        String packageInfo = String.valueOf(withdraw.get("packageInfo"));

        // 更新交易记录（填写第三方单号）
        transaction.setExternalTradeNo(transferBillNo);
        walletTransactionMapper.updateById(transaction);
        // TODO 扣税记录
//        updateMonthlySummary(userId, userType, totalWithdrawAmount);
        // 封装返回信息
        HashMap<String, Object> resMap = new HashMap<>();
        resMap.put("stateName", stateName);
        resMap.put("createTime", createTime);
        resMap.put("packageInfo", packageInfo);
        resMap.put("mchId", withdraw.get("mchId"));
        resMap.put("appId", withdraw.get("appId"));

        return ResultDTO.success("申请提现成功", resMap);
    }

    /**
     * 微信提现 - 回调
     *
     * @param
     * @return
     */
    public Map<String, Object> handleCallback4Withdraw(HttpServletRequest request) {
        GetTransferBillByOutNo.TransferBillEntity transferBillEntity = wechatTransferNotify.handleCallback(request);
        String outBillNo = transferBillEntity.getOutBillNo();
        String transferBillNo = transferBillEntity.getTransferBillNo();
        String tradeState = transferBillEntity.getState().name();
        Long transferAmount = transferBillEntity.getTransferAmount();
        String openid = transferBillEntity.getOpenid();
        log.info("getOutTradeNo {}", outBillNo);
        log.info("getTransactionId{}", transferBillNo);
        log.info("getTradeState{}", tradeState);
        log.info("getAmount{}", transferAmount);
        log.info("openid{}", openid);
        HashMap<String, Object> resMap = new HashMap<>();
        resMap.put("code", 200);
        LambdaQueryWrapper<WalletTransaction> walletTransactionLambdaQueryWrapper = new LambdaQueryWrapper<>();
        walletTransactionLambdaQueryWrapper.eq(WalletTransaction::getOutTradeNo, outBillNo)
                .eq(WalletTransaction::getTransactionType, 3);
        WalletTransaction walletTransaction = walletTransactionMapper.selectOne(walletTransactionLambdaQueryWrapper);
        if (walletTransaction == null) {
            return resMap;
        }

        Wallet wallet = walletMapper.selectWalletByIdForUpdate(walletTransaction.getWalletId());
        if ("SUCCESS".equals(tradeState)) {
            int updateCount = walletTransactionMapper.updateStatusIfUnprocessed(
                    walletTransaction.getTransactionId(), transferBillNo, 0, 1);

            if (updateCount == 0) {
                log.info("【微信提现查单任务】订单 {} 已被其他线程处理，跳过", transferBillNo);
                return null;
            }

            log.info("【微信提现查单任务】提现成功并处理完成：{}", outBillNo);
        } else if ("FAIL".equals(tradeState) || "CANCELLED".equals(tradeState)) {
            payQueryHandler.handleFailedWithdraw(walletTransaction, wallet, outBillNo);
//            wallet.setBalance(wallet.getBalance().add(walletTransaction.getAmount()));
//            walletMapper.updateById(wallet);
//            orderServiceImpl.deleteWalletTransaction(outBillNo, 3);
            log.warn("【微信提现查单任务】订单已关闭：{}", outBillNo);
        }

        return resMap;
    }

    @Override
    public ResultDTO<Map<String, Object>> rechargeWithWechat(WalletDTO requestDTO) {
        String loginIdAsString = StpUtil.getLoginIdAsString();
        LoginUserDTO loginInfo = (LoginUserDTO) StpUtil.getSession().get(loginIdAsString);
        Long userId = loginInfo.getUserId();
        Integer userType = loginInfo.getUserType();

        Wallet wallet = walletMapper.selectWalletByUserForUpdate(userId, userType);
        Long walletId = wallet.getWalletId();

        // 生成内部交易订单号
        String outTradeNo = generateRechargeOrderNo(userId, 1);

        Integer payMethods = requestDTO.getPayMethods();
        int payMethods4DB;
        if (payMethods == 1 || payMethods == 6) {
            payMethods4DB = 1;
        } else {
            payMethods4DB = payMethods;
        }

        // 保存交易记录（状态为 0：待支付）
        WalletTransaction transaction = new WalletTransaction();
        transaction.setWalletId(walletId);
        transaction.setAmount(requestDTO.getBalance());
        transaction.setTransactionType(1); // 1=充值
        transaction.setStatus(0); // 待处理
        transaction.setPaymentChannel(payMethods4DB);
        transaction.setTransactionTime(new Date());
        transaction.setOutTradeNo(outTradeNo);
        walletTransactionMapper.insert(transaction);

        PayStrategy payStrategy = payContext.executePay(payMethods);
        HashMap<String, Object> map = new HashMap<>();
        map.put("userId", userId);
        map.put("userType", userType);
        map.put("orderNumber", outTradeNo);
        map.put("transactionType", 1); // 交易类型
        map.put("actualPaymentPrice", requestDTO.getBalance()); // 实付金额
        map.put("description", "充值");
        map.put("code", requestDTO.getCode());
        Map<String, Object> pay = payStrategy.pay(map);

        return ResultDTO.success("充值成功", pay);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> handleCallback4Recharge(HttpServletRequest request) {
        log.info("【微信支付回调】开始处理充值回调");

        Transaction transaction = payContext.executePay(1).handleCallback(request);
        String tradeState = transaction.getTradeState().name();
        String outTradeNo = transaction.getOutTradeNo();
        String transactionId = transaction.getTransactionId();
        String successTime = transaction.getSuccessTime();

        HashMap<String, Object> resMap = new HashMap<>();
        resMap.put("code", 200);
        WalletTransaction walletTransaction = payQueryHandler.getPendingRechargeTransaction(outTradeNo);
        if (walletTransaction == null) {
            log.warn("【微信支付回调】未找到待处理交易，订单号：{}", outTradeNo);
            return resMap;
        }

        if ("SUCCESS".equals(tradeState)) {
            OffsetDateTime offsetDateTime = OffsetDateTime.parse(successTime);
            Date payTime = Date.from(offsetDateTime.toInstant());
            payQueryHandler.handleRechargeSuccess(walletTransaction, transactionId, payTime, walletTransaction.getAmount(), 1); // 1 = 微信
        } else if ("CLOSED".equals(tradeState)) {
            payQueryHandler.handleRechargeClosed(walletTransaction);
        }

        return resMap;
    }

    @Override
    public Wallet getWalletInfo() {
        String loginIdAsString = StpUtil.getLoginIdAsString();
        LoginUserDTO loginInfo = (LoginUserDTO) StpUtil.getSession().get(loginIdAsString);
        Long userId = loginInfo.getUserId();
        Integer userType = loginInfo.getUserType();
        LambdaQueryWrapper<Wallet> walletLambdaQueryWrapper = new LambdaQueryWrapper<>();
        walletLambdaQueryWrapper
                .select(Wallet::getTotalIncome)
                .eq(Wallet::getUserId, userId)
                .eq(Wallet::getUserType, userType);
        Wallet wallet = walletMapper.selectOne(walletLambdaQueryWrapper);
        return wallet;
    }

    /**
     * 生成订单交易号 时分秒+用户id+交易类型+6位随机数
     *
     * @param userId          用户id
     * @param transactionType 交易类型 (1充值、2消费、3提现、4退款)
     * @return
     */
    public String generateRechargeOrderNo(Long userId, Integer transactionType) {
        String timePart = DateTimeFormatter.ofPattern("yyyyMMddHHmmss").format(LocalDateTime.now());

        String randomPart = String.valueOf((int) (Math.random() * 1_000_000)); // 6位随机数
        randomPart = String.format("%06d", Integer.parseInt(randomPart));

        return timePart + userId + transactionType + randomPart;
    }

    // 插入钱包记录
    public void insert(Wallet wallet) {
        walletMapper.insert(wallet);  // 调用 WalletMapper 插入记录
    }

    /**
     * 维护本月提现金额
     * @param userId 用户/阿姨id
     * @param userType 类型：1用户，2阿姨
     * @param withdrawAmount 提现金额
     */
    public void updateMonthlySummary(Long userId, Integer userType, BigDecimal withdrawAmount) {
        YearMonth now = YearMonth.now();
        int year = now.getYear();
        int month = now.getMonthValue();

        WalletMonthlySummary summary = walletMonthlySummaryMapper.selectByUserAndMonth(userId, userType, year, month);
        if (summary == null) {
            summary = new WalletMonthlySummary();
            summary.setUserId(userId);
            summary.setUserType(userType);
            summary.setYear(year);
            summary.setMonth(month);
            summary.setTotalIncome(BigDecimal.ZERO);
            summary.setTotalWithdraw(withdrawAmount);
            walletMonthlySummaryMapper.insert(summary);
        } else {
            summary.setTotalWithdraw(summary.getTotalWithdraw().add(withdrawAmount));
            walletMonthlySummaryMapper.updateById(summary);
        }
    }

}
