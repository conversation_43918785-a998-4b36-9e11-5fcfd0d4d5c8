package com.guanghonggu.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.guanghonggu.dto.LoginUserDTO;
import com.guanghonggu.dto.PhoneLoginDTO;
import com.guanghonggu.dto.ResultDTO;
import com.guanghonggu.entity.SysPicture;
import com.guanghonggu.entity.User;
import com.guanghonggu.entity.Wallet;
import com.guanghonggu.mapper.CaregiverMapper;
import com.guanghonggu.mapper.SysPictureMapper;
import com.guanghonggu.service.CouponService;
import com.guanghonggu.service.UserService;
import com.guanghonggu.service.WalletService;
import com.guanghonggu.service.WxPhoneLoginService;
import com.guanghonggu.strategy.pay.strategy.PayQueryHandler;
import com.guanghonggu.util.HttpClientUtil;
import com.guanghonggu.util.WXPayUtil;
import com.guanghonggu.util.WXPhoneDecryptor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Service
public class WxPhoneLoginServiceImpl implements WxPhoneLoginService {

    // 替换为你的小程序配置
    private final String APP_ID = "wx408023ec9b8f1c8f";
    private final String APP_SECRET = "cb2bdf05735f6a71f15d9dfa9a8d8cd3";
    // 微信支付配置（使用你自己的商户信息）
    private final String appId = "wx408023ec9b8f1c8f";      //小程序 AppID
    private final String mchId = "1688498995";          //小程序 AppID
    private final String notifyUrl = "https://guanghonggu.mynatapp.cc/wxPay/notify"; // 支付结果回调地址
    private final String privateKeyPath = "F:/apiclient_key.pem";          //私钥
    private final String apiV3Key = "aB3kLp9Xq2WyZmD7TfGhJvCs8RnUeYz0";         //接口加密密钥
    private final String serialNo = "23CC74D75041360AD9841DFA46FD65DE50BA47DB";         //商户证书序列号


    @Autowired
    private UserService userService;

    @Autowired
    private WalletService walletService;

    @Autowired
    private SysPictureMapper sysPictureMapper;

    @Autowired
    private CouponService couponService;

    @Autowired
    private PayQueryHandler payQueryHandler;

    // 微信登录接口：loginWithWeChat
    @Override
    public ResultDTO<?> loginWithWeChat(PhoneLoginDTO dto) {
        try {

            JSONObject json = payQueryHandler.getWechatMiniOpenIdByCode(dto.getCode());
            String sessionKey = json.getString("session_key");
            String openid = json.getString("openid"); // 获取 openid
            // 2. 解密手机号
            String phone = WXPhoneDecryptor.decrypt(dto.getEncryptedData(), sessionKey, dto.getIv());
            if (phone == null) {
                throw new RuntimeException("手机号解密失败");
            }

            // 3. 查询用户是否存在
            User user = userService.selectByPhone(phone);
            int isNewUser = 0;
            if (user == null) {
                isNewUser = 1;
                // 如果用户不存在，创建新用户
                user = new User();
                user.setPhoneNumber(phone);
                user.setRegistrationTime(new Date());
                user.setWechatOpenid(openid); // 保存 openid
                user.setNickname("用户" + phone.substring(phone.length() - 4));
                LambdaQueryWrapper<SysPicture> sysPictureLambdaQueryWrapper = new LambdaQueryWrapper<>();
                sysPictureLambdaQueryWrapper.eq(SysPicture::getPictureType, 1);
                SysPicture sysPicture = sysPictureMapper.selectOne(sysPictureLambdaQueryWrapper);

                user.setAvatar(sysPicture.getDownloadAddress());
                user.setUserSource(dto.getUserSource());
                userService.insert(user);

                // 新用户首次登录，创建钱包记录
                Wallet wallet = new Wallet();
                wallet.setUserId(user.getUserId());  // 设置钱包关联的用户ID
                wallet.setUserType(1);  // 设置用户类型，1 代表普通用户
                wallet.setBalance(BigDecimal.ZERO);  // 初始化余额为 0.0
                wallet.setRechargeBalance(BigDecimal.ZERO);  // 初始化可充值余额为 0.0
                wallet.setTotalIncome(BigDecimal.ZERO);  // 初始化总收入为 0.0
                wallet.setTotalSpent(BigDecimal.ZERO);  // 初始化总支出为 0.0
                walletService.insert(wallet);  // 假设你有一个 walletService 来操作钱包表

                ResultDTO<String> resultDTO = couponService.issueNewUserCoupon(user.getUserId());
            } else {
                // 如果用户已存在，更新 openid（防止用户换设备后 openid 变了）
                if (openid != null && !openid.equals(user.getWechatOpenid())) {
                    user.setWechatOpenid(openid);
                    userService.updateById(user);
                }
            }

            // 登录逻辑
            StpUtil.login("user:" + user.getUserId());

            LoginUserDTO loginUserDTO = new LoginUserDTO();
            loginUserDTO.setPhone(phone);
            loginUserDTO.setUserId(user.getUserId());
            loginUserDTO.setUserType(1);  // 设置用户类型
            StpUtil.getSession().set("user:" + user.getUserId(), loginUserDTO);
            String token = StpUtil.getTokenValue();

            Map<String, Object> data = new HashMap<>();
            data.put("token", token);
            data.put("phoneNumber", phone);
            data.put("openid", openid); // 返回给前端备用（可选）
            data.put("userId", user.getUserId());
            data.put("isNewUser", isNewUser);
            data.put("avatar", user.getAvatar());
            return ResultDTO.success("登录成功", data);
        } catch (Exception e) {
            e.printStackTrace();
            return ResultDTO.error("登录失败：" + e.getMessage());
        }
    }

}
