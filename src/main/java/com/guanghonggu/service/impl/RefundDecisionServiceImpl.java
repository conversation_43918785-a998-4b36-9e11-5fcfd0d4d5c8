package com.guanghonggu.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.guanghonggu.dto.LoginUserDTO;
import com.guanghonggu.dto.RefundDecisionDTO;
import com.guanghonggu.dto.ResultDTO;
import com.guanghonggu.entity.*;
import com.guanghonggu.handler.exception.BizException;
import com.guanghonggu.mapper.*;
import com.guanghonggu.service.RefundDecisionService;
import com.guanghonggu.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.prefs.BackingStoreException;

@Slf4j
@Service
public class RefundDecisionServiceImpl implements RefundDecisionService {

    @Autowired
    private RefundRejectReasonMapper refundRejectReasonMapper;
    @Autowired
    private OrderMapper orderMapper;
    @Autowired
    private WalletMapper walletMapper;
    @Autowired
    private WalletTransactionMapper walletTransactionMapper;

    @Autowired
    private OrderServiceImpl orderService;
    @Autowired
    private SysConfigMapper sysConfigMapper;

//    /**
//     *阿姨是否接受退款
//     */
//    @Override
//    @Transactional(rollbackFor = Exception.class)
//    public ResultDTO<?> decideRefund(RefundDecisionDTO dto) {
//        String loginId = StpUtil.getLoginIdAsString();
//        LoginUserDTO loginInfo = (LoginUserDTO) StpUtil.getSession().get(String.valueOf(loginId));
//        Long userId = loginInfo.getUserId();
//        Integer userType = loginInfo.getUserType();
//        try {
//            if (dto.getAccept()) {
//                // 根据 refundId 查询订单信息
//                Order order = orderMapper.selectById(dto.getRefundId());  // 使用 dto.getRefundId() 获取订单
//
//                if (order == null) {
//                    return ResultDTO.error("订单不存在，无法退款");
//                }
//
//                // 查询用户钱包
//                Wallet userWallet = walletMapper.selectWalletByUserForUpdate(userId, userType);
//                if (userWallet == null) {
//                    return ResultDTO.error("用户钱包不存在");
//                }
//                Long caregiversId = order.getCaregiversId();
//                //查询阿姨钱包
//                Wallet careviverWallet = walletMapper.selectWalletByUserForUpdate(caregiversId, 2);
//                if (careviverWallet == null) {
//                    return ResultDTO.error("阿姨钱包不存在");
//                }
//
//                // 退款金额
//                BigDecimal refundAmount = order.getActualPaymentPrice();  // 实际支付金额
//
//                // 计算退还给用户的金额：退款金额的50%
//                BigDecimal userRefundAmount = refundAmount.multiply(new BigDecimal("0.5"));
//
//                // 剩余50%金额给阿姨，但阿姨的50%需要抽成
//                BigDecimal remainingAmount = refundAmount.multiply(new BigDecimal("0.5"));
//
//                // 获取商家佣金比例
//                BigDecimal commissionRate = getCommissionRate();  // 获取商家佣金比例
//                BigDecimal merchantIncome = remainingAmount.multiply(commissionRate);  // 商家收入
//
//                // 阿姨的收入：剩余50%扣除商家抽成
//                BigDecimal caregiverIncome = remainingAmount.subtract(merchantIncome);  // 阿姨收入
//
//                // 增加用户钱包余额（退还给用户的50%）
//                userWallet.setBalance(userWallet.getBalance().add(userRefundAmount));
//                walletMapper.updateById(userWallet);
//
//                // 如果余额为 null，设置为 0
//                BigDecimal currentBalance = careviverWallet.getBalance();
//                if (currentBalance == null) {
//                    currentBalance = BigDecimal.ZERO;
//                }
//
//                // 增加阿姨收入到余额
//                careviverWallet.setBalance(currentBalance.add(caregiverIncome));
//
//                // 如果 totalIncome 为 null，设置为 0
//                BigDecimal totalIncome = careviverWallet.getTotalIncome();
//                if (totalIncome == null) {
//                    totalIncome = BigDecimal.ZERO;
//                }
//
//                // 增加阿姨收入到 totalIncome
//                careviverWallet.setTotalIncome(totalIncome.add(caregiverIncome));
//
//                // 更新钱包数据
//                walletMapper.updateById(careviverWallet);
//
//                // 在阿姨同意退款时，更新订单状态为 6
//                order.setStatus(6);
//
//
//                // 更新订单中的阿姨收入和商家收入
//                UpdateWrapper<Order> orderWrapper = new UpdateWrapper<>();
//                orderWrapper.eq("order_id", order.getOrderId());
//                orderWrapper.set("caregiver_income", caregiverIncome);  // 更新阿姨收入
//                orderWrapper.set("merchant_income", merchantIncome);    // 更新商家收入
//                orderMapper.update(null, orderWrapper);
//
//                // 插入退款记录
//                WalletTransaction transaction = new WalletTransaction();
//                transaction.setWalletId(userWallet.getWalletId());         // 用户钱包
//                transaction.setTransactionType(4);                      // 4 = 退款
//                transaction.setAmount(userRefundAmount);
//                transaction.setTransactionTime(new Date());             // 设置交易时间
//                transaction.setPaymentChannel(3);
//                transaction.setStatus(1);                               // 1 = 成功
//                transaction.setOutTradeNo(order.getOrderNumber());      // 商户订单号（从 order 中获取）
//                walletTransactionMapper.insert(transaction);
//
//                WalletTransaction transaction4caregiver = new WalletTransaction();
//                transaction.setWalletId(careviverWallet.getWalletId());     // 阿姨钱包
//                transaction.setTransactionType(5);                      // 5 = 收入
//                transaction.setAmount(caregiverIncome);
//                transaction.setTransactionTime(new Date());             // 设置交易时间
//                transaction.setPaymentChannel(3);
//                transaction.setStatus(1);                               // 1 = 成功
//                transaction.setOutTradeNo(order.getOrderNumber());      // 商户订单号（从 order 中获取）
//                walletTransactionMapper.insert(transaction4caregiver);
//
//
////                Date baseTime = new Date(); // 当前时间
////                int secondsOffset = 0;
////                orderService.insertOperationLog(order.getOrderId(), userId, 2, 6,
////                        "阿姨同意退款，退款完成", DateUtil.offsetTime(baseTime, secondsOffset));
//
//                return ResultDTO.success("阿姨已接受退款请求，已退还 50% 金额", null);
//            }
//            else {
//                // 拒绝退款 → 必须填写理由
//                if (dto.getRejectReason() == null || dto.getRejectReason().trim().isEmpty()) {
//                    return ResultDTO.error("拒绝理由不能为空");
//                }
//                // 在阿姨拒绝退款时，更新订单状态为 5
//                Order order = orderMapper.selectById(dto.getRefundId());  // 使用 dto.getRefundId() 获取订单
//                if (order == null) {
//                    return ResultDTO.error("订单不存在，无法处理");
//                }
//                order.setStatus(5); // 更新订单状态为拒绝退款（5）
//
//                // 插入拒绝记录
//                RefundRejectReason reason = new RefundRejectReason();
//                reason.setRefundId(dto.getRefundId());
//                reason.setCaregiverId(dto.getCaregiverId());
//                reason.setRejectReason(dto.getRejectReason());
//                refundRejectReasonMapper.insert(reason);
//
//                Date baseTime = new Date(); // 当前时间
//                int secondsOffset = 0;
//                orderService.insertOperationLog(dto.getRefundId(), userId, 2, 9, "阿姨拒绝退款，等待平台审核", DateUtil.offsetTime(baseTime, secondsOffset));
//                return ResultDTO.notDataSuccess("阿姨已拒绝退款并记录理由");
//            }
//        } catch (Exception e) {
//            log.error("处理失败：", e);
//            throw new BizException("处理失败");
//        }
//    }

    // 获取商家佣金比例
    private BigDecimal getCommissionRate() {
        // 通过 config_key 获取佣金比例
        QueryWrapper<SysConfig> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("config_key", "commission_rate");
        SysConfig sysConfig = sysConfigMapper.selectOne(queryWrapper);

        // 确保有数据，若没有返回 0
        if (sysConfig != null) {
            return new BigDecimal(sysConfig.getConfigValue());  // 返回配置的佣金比例
        }
        return BigDecimal.ZERO;  // 如果没有配置，返回默认的 0
    }
}