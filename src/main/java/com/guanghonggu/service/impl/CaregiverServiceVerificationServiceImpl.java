package com.guanghonggu.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.guanghonggu.constant.CommonConstants;
import com.guanghonggu.dto.CaregiverServiceVerificationDTO;
import com.guanghonggu.dto.LoginUserDTO;
import com.guanghonggu.dto.ResultDTO;
import com.guanghonggu.entity.CaregiverServiceVerification;
import com.guanghonggu.entity.Order;
import com.guanghonggu.entity.ServiceVerificationImage;
import com.guanghonggu.handler.exception.BizException;
import com.guanghonggu.mapper.OrderMapper;
import com.guanghonggu.mapper.ServiceVerificationImageMapper;
import com.guanghonggu.service.CaregiverServiceVerificationService;
import com.guanghonggu.mapper.CaregiverServiceVerificationMapper;
import com.guanghonggu.util.AliyunOssUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【caregiver_service_verification(阿姨服务验证)】的数据库操作Service实现
 * @createDate 2025-04-27 10:56:56
 */
@Service
public class CaregiverServiceVerificationServiceImpl extends ServiceImpl<CaregiverServiceVerificationMapper, CaregiverServiceVerification>
        implements CaregiverServiceVerificationService {


    @Autowired
    private CaregiverServiceVerificationMapper serviceVerificationMapper;


    @Autowired
    private AliyunOssUtil aliyunOssUtil;

    @Autowired
    private OrderMapper orderMapper;

    @Autowired
    private ServiceVerificationImageMapper serviceVerificationImageMapper;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultDTO<String> photoClock(CaregiverServiceVerificationDTO serviceVerification) {
        String loginIdAsString = StpUtil.getLoginIdAsString();
        LoginUserDTO loginUser = (LoginUserDTO) StpUtil.getSession().get(loginIdAsString);
        Long caregiversId = loginUser.getUserId();

        ArrayList<String> imageList = new ArrayList<>();
        List<MultipartFile> verificationPhotoList = serviceVerification.getVerificationPhotoList();
        for (MultipartFile punchCardPhoto : verificationPhotoList) {
            String uploadUrl = null;
            try {
                uploadUrl = aliyunOssUtil.upload(punchCardPhoto, CommonConstants.PICTURE_URL_CAREGIVER_VERIFICATION + caregiversId);
                imageList.add(uploadUrl);
            } catch (IOException e) {
                throw new BizException("上传失败");
            }
            if (uploadUrl == null) {
                throw new BizException("上传失败");
            }
        }
        Order order = new Order();
        Order originalOrder = orderMapper.selectById(serviceVerification.getOrderId());
        Date appointmentTime = originalOrder.getAppointmentTime();
        Date now = new Date();
        if (appointmentTime != null && now.after(appointmentTime)) {
            order.setIsLate(1);
        }

        order.setOrderId(serviceVerification.getOrderId());
        order.setStatus(2);
        orderMapper.updateById(order);

        CaregiverServiceVerification verification = new CaregiverServiceVerification();
        BeanUtils.copyProperties(serviceVerification, verification);
//        verification.setVerificationPhoto(uploadUrl);
        verification.setCaregiverId(caregiversId);
        verification.setArrivalTime(now);
        verification.setVerificationTime(now);
        verification.setOrderId(serviceVerification.getOrderId());
        verification.setStatus(1);
        int i = serviceVerificationMapper.insert(verification);
        if (i <= 0) {
            throw new BizException("打卡失败");
        }
        for (String image : imageList) {
            ServiceVerificationImage serviceVerificationImage = new ServiceVerificationImage();
            serviceVerificationImage.setServiceVerificationId(verification.getServiceVerificationId());
            serviceVerificationImage.setImageUrl(image);
            serviceVerificationImageMapper.insert(serviceVerificationImage);
        }

        return ResultDTO.notDataSuccess("打卡成功");
    }
}




