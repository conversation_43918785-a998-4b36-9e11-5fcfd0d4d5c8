package com.guanghonggu.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.guanghonggu.entity.CleaningItemImage;
import com.guanghonggu.service.CleaningItemImageService;
import com.guanghonggu.mapper.CleaningItemImageMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【cleaning_item_image(清洁物品图片表)】的数据库操作Service实现
* @createDate 2025-07-03 09:17:50
*/
@Service
public class CleaningItemImageServiceImpl extends ServiceImpl<CleaningItemImageMapper, CleaningItemImage>
    implements CleaningItemImageService{

}




