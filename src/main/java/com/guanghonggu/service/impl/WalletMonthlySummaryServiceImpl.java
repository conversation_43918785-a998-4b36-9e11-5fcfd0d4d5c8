package com.guanghonggu.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.guanghonggu.entity.WalletMonthlySummary;
import com.guanghonggu.service.WalletMonthlySummaryService;
import com.guanghonggu.mapper.WalletMonthlySummaryMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【wallet_monthly_summary(用户每月钱包收支汇总表)】的数据库操作Service实现
* @createDate 2025-07-29 14:35:51
*/
@Service
public class WalletMonthlySummaryServiceImpl extends ServiceImpl<WalletMonthlySummaryMapper, WalletMonthlySummary>
    implements WalletMonthlySummaryService{

}




