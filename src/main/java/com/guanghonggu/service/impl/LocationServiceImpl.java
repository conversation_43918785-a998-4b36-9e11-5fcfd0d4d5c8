package com.guanghonggu.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.guanghonggu.dto.LocationDTO;
import com.guanghonggu.entity.Location;
import com.guanghonggu.mapper.LocationMapper;
import com.guanghonggu.service.LocationService;
import com.guanghonggu.websocket.WebSocketServer;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class LocationServiceImpl implements LocationService {
    private final LocationMapper locationMapper;

    public LocationServiceImpl(LocationMapper locationMapper) {
        this.locationMapper = locationMapper;
    }

    @Autowired
    private WebSocketServer webSocketServer;

    @Override
    public void saveLocation(LocationDTO dto) {

        // 判断传入的 LocationDTO 是否为空
        if (dto == null) {
            throw new RuntimeException("LocationDTO cannot be null"); // 如果为空，抛出异常
        }

        // 判断用户 ID 是否为空
        if (dto.getCaregiverId() == null) {
            throw new RuntimeException("User ID cannot be null"); // 如果为空，抛出异常
        }

        // 判断经度和纬度是否为空
        if (dto.getLatitude() == null || dto.getLongitude() == null) {
            throw new RuntimeException("Location latitude or longitude is missing"); // 如果为空，抛出异常
        }

        try {
            // 创建一个 LambdaQueryWrapper，用于构造查询条件
            LambdaQueryWrapper<Location> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(Location::getCaregiverId, dto.getCaregiverId()) // 设置查询条件：根据用户ID查找
                    .last("LIMIT 1"); // 添加限制条件，最多查询一个结果

            // 查询数据库，判断是否已存在该用户的定位信息
            Location existing = locationMapper.selectOne(wrapper);

            if (existing == null) {  // 如果没有找到用户的定位信息，表示是第一次保存
                // 创建一个新的 Location 实体对象
                Location location = new Location();
                // 使用 BeanUtils.copyProperties 方法将 dto 中的属性复制到 location 中
                BeanUtils.copyProperties(dto, location);
                // 插入新记录到数据库
                locationMapper.insert(location);
            } else {  // 如果找到了已存在的定位信息，更新该记录
                // 更新经度、纬度信息
                existing.setLatitude(dto.getLatitude());
                existing.setLongitude(dto.getLongitude());
                // 更新数据库中的记录
                locationMapper.updateById(existing);
            }
            webSocketServer.pushLocationToUser(dto.getCaregiverId().toString(), dto.getLatitude(), dto.getLongitude());
        } catch (Exception e) {
            // 抛出自定义异常
            throw new RuntimeException("保存定位失败");
        }
    }


    @Override
    public Location getLocationByUserId(Long caregiverId) {
        if (caregiverId == null) return null;
        LambdaQueryWrapper<Location> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Location::getCaregiverId, caregiverId).last("LIMIT 1");

        return locationMapper.selectOne(wrapper);
    }


}