package com.guanghonggu.service.impl;

import com.aliyun.dypnsapi20170525.Client;
import com.aliyun.dypnsapi20170525.models.GetMobileRequest;
import com.aliyun.dypnsapi20170525.models.GetMobileResponse;
import com.aliyun.teaopenapi.models.Config;
import com.guanghonggu.service.AliyunDypnsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service
public class AliyunDypnsServiceImpl implements AliyunDypnsService {

    @Autowired
    private Client client;

    @Value("${aliyun.accessKey.id}")
    private String accessKeyId;

    @Value("${aliyun.accessKey.secret}")
    private String accessKeySecret;

    @Value("${aliyun.dypns.endpoint}")
    private String endpoint;

    @Override
    public String getMobileByToken(String accessToken) throws Exception {
        GetMobileRequest request = new GetMobileRequest().setAccessToken(accessToken);
        GetMobileResponse response = client.getMobile(request);

        String code = response.getBody().getCode();

        return response.getBody().getGetMobileResultDTO().getMobile(); // 推荐使用 getter
    }
}
