package com.guanghonggu.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.guanghonggu.dto.CleaningItemDTO;
import com.guanghonggu.dto.ResultDTO;
import com.guanghonggu.entity.CleaningItem;
import com.guanghonggu.entity.CleaningItemImage;
import com.guanghonggu.mapper.CleaningItemImageMapper;
import com.guanghonggu.service.CleaningItemService;
import com.guanghonggu.mapper.CleaningItemMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【cleaning_item(清洁工具与清洁剂表)】的数据库操作Service实现
 * @createDate 2025-07-03 09:17:50
 */
@Service
public class CleaningItemServiceImpl extends ServiceImpl<CleaningItemMapper, CleaningItem>
        implements CleaningItemService {

    @Autowired
    private CleaningItemMapper cleaningItemMapper;

    @Autowired
    private CleaningItemImageMapper cleaningItemImageMapper;

    @Override
    public ResultDTO<IPage<CleaningItemDTO>> listByCategoryId(CleaningItemDTO cleaningItemDTO) {
        Long categoryId = cleaningItemDTO.getCategoryId();
        Integer pageSize = cleaningItemDTO.getPageSize();
        Integer currentPage = cleaningItemDTO.getCurrentPage();
        Page<CleaningItemDTO> page = new Page<>(currentPage, pageSize);

        IPage<CleaningItemDTO> cleaningItemDTOList = cleaningItemMapper.listByCategoryId(page, categoryId);
        return ResultDTO.success("获取成功", cleaningItemDTOList);
    }

    @Override
    public ResultDTO<CleaningItemDTO> getCleaningItemById(String itemId) {
        CleaningItemDTO cleaningItemList = cleaningItemMapper.getCleaningItemById(itemId);
        LambdaQueryWrapper<CleaningItemImage> cleaningItemImageLambdaQueryWrapper = new LambdaQueryWrapper<>();
        cleaningItemImageLambdaQueryWrapper
                .select(CleaningItemImage::getImageUrl)
                .eq(CleaningItemImage::getCleaningItemId, itemId);
        List<CleaningItemImage> cleaningItemImages = cleaningItemImageMapper.selectList(cleaningItemImageLambdaQueryWrapper);
        List<String> imageUrlList = cleaningItemImages.stream()
                .map(CleaningItemImage::getImageUrl)
                .collect(Collectors.toList());
        cleaningItemList.setImageUrlList(imageUrlList);
        return ResultDTO.success("获取成功", cleaningItemList);
    }

}




