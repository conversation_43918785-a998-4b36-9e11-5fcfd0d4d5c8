package com.guanghonggu.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.guanghonggu.entity.SysConfig;
import com.guanghonggu.service.SysConfigService;
import com.guanghonggu.mapper.SysConfigMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

/**
* <AUTHOR>
* @description 针对表【sys_config(系统配置表)】的数据库操作Service实现
* @createDate 2025-05-28 14:23:18
*/
@Service
public class SysConfigServiceImpl extends ServiceImpl<SysConfigMapper, SysConfig>
    implements SysConfigService{

    @Autowired
    private SysConfigMapper sysConfigMapper;

    @Override
    public BigDecimal getCommissionRate() {
        String value = sysConfigMapper.selectValueByKey("commission_rate");
        if (StringUtils.isBlank(value)) {
            return new BigDecimal("0.20"); // 默认 20%
        }
        return new BigDecimal(value);
    }

    @Override
    public BigDecimal getRefundFeeRateNearService() {
        String value = sysConfigMapper.selectValueByKey("refund_fee_rate_near_service");
        if (StringUtils.isBlank(value)) {
            return new BigDecimal("0.05"); // 默认5%
        }
        return new BigDecimal(value);
    }

    @Override
    public int getRefundFeeMinutesNearService() {
        String value = sysConfigMapper.selectValueByKey("refund_fee_minutes_near_service");
        if (StringUtils.isBlank(value)) {
            return 15; // 默认15分钟
        }
        return Integer.parseInt(value);
    }

    @Override
    public BigDecimal getLateDeductedRateService() {
        String value = sysConfigMapper.selectValueByKey("late_deducted_rate");
        if (StringUtils.isBlank(value)) {
            return new BigDecimal("0.10"); // 默认10%
        }
        return new BigDecimal(value);
    }

}




