package com.guanghonggu.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.guanghonggu.dto.CancelReasonDTO;
import com.guanghonggu.entity.CancelReason;
import com.guanghonggu.mapper.CancelReasonMapper;
import com.guanghonggu.service.CancelReasonService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
@Service
public class CancelReasonServiceImpl extends ServiceImpl<CancelReasonMapper, CancelReason> implements CancelReasonService {

    @Autowired
    private CancelReasonMapper cancelReasonMapper;

    @Override
    public List<CancelReasonDTO> getCancelReasonList(Integer type){
        LambdaQueryWrapper<CancelReason> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CancelReason::getType,type);

        List<CancelReason> cancelReasons = cancelReasonMapper.selectList(queryWrapper);  // 执行查询

        // 将查询结果转换为 CancelReasonDTO
        List<CancelReasonDTO> cancelReasonDTOList = new ArrayList<>();
        for (CancelReason cancelReason : cancelReasons) {
            CancelReasonDTO dto = new CancelReasonDTO();
            BeanUtils.copyProperties(cancelReason, dto);  // 将 CancelReason 转换为 CancelReasonDTO
            cancelReasonDTOList.add(dto);
        }

        return cancelReasonDTOList;  // 返回转换后的结果
    }
}

