package com.guanghonggu.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.guanghonggu.constant.CommonConstants;
import com.guanghonggu.dto.LoginUserDTO;
import com.guanghonggu.dto.OrderAttributeDTO;
import com.guanghonggu.dto.OrderDTO;
import com.guanghonggu.dto.ResultDTO;
import com.guanghonggu.dto.ServiceAttributeDTO;
import com.guanghonggu.entity.Coupon;
import com.guanghonggu.entity.Order;
import com.guanghonggu.entity.OrderAttribute;
import com.guanghonggu.entity.OrderSequence;
import com.guanghonggu.entity.ServiceAttributeOption;
import com.guanghonggu.entity.ToDoReminders;
import com.guanghonggu.entity.UserCoupon;
import com.guanghonggu.entity.Wallet;
import com.guanghonggu.entity.WalletTransaction;
import com.guanghonggu.enums.AppType;
import com.guanghonggu.enums.WebSocketMessageType;
import com.guanghonggu.handler.exception.BizException;
import com.guanghonggu.mapper.CouponMapper;
import com.guanghonggu.mapper.OrderAttributeMapper;
import com.guanghonggu.mapper.OrderSequenceMapper;
import com.guanghonggu.mapper.ServiceAttributeOptionMapper;
import com.guanghonggu.mapper.ServiceTypeMapper;
import com.guanghonggu.mapper.ToDoRemindersMapper;
import com.guanghonggu.mapper.UserCouponMapper;
import com.guanghonggu.mapper.WalletMapper;
import com.guanghonggu.mapper.WalletTransactionMapper;
import com.guanghonggu.entity.*;
import com.guanghonggu.mapper.*;
import com.guanghonggu.entity.ServiceType;
import com.guanghonggu.mapper.dto.OrderAttributeDTOMapper;
import com.guanghonggu.service.OrderService;
import com.guanghonggu.mapper.OrderMapper;
import com.guanghonggu.strategy.pay.strategy.PayContext;
import com.guanghonggu.strategy.pay.strategy.PayStrategy;

import com.guanghonggu.strategy.user.UserByTypeStrategyContext;
import com.guanghonggu.strategy.user.UserByTypeStrategyInterface;
import com.guanghonggu.util.DateUtil;
import com.guanghonggu.util.NotificationUtil;
import com.guanghonggu.websocket.WebSocketServer;
import org.springframework.beans.factory.annotation.Autowired;
import com.guanghonggu.service.SysConfigService;
import com.guanghonggu.strategy.pay.strategy.PayQueryHandler;
import com.guanghonggu.strategy.pay.wechat.WechatAPI;
import com.wechat.pay.java.service.payments.model.Transaction;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import java.math.RoundingMode;
import java.time.*;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【order(订单表)】的数据库操作Service实现
 * @createDate 2025-04-27 10:56:56
 */
@Slf4j
@Service
public class OrderServiceImpl extends ServiceImpl<OrderMapper, Order>
        implements OrderService {
    @Autowired
    private OrderMapper orderMapper; // 注入订单 Mapper，用于访问数据库

    @Autowired
    private UserNotificationMapper userNotificationMapper;

    @Autowired
    private WalletMapper walletMapper;

    @Autowired
    private WalletTransactionMapper walletTransactionMapper;

    @Autowired
    private PayContext payContext;

    @Autowired
    private UserCouponMapper userCouponMapper;

    @Autowired
    private ToDoRemindersMapper toDoRemindersMapper;

    @Autowired
    private OrderAttributeMapper orderAttributeMapper;

    @Autowired
    private SysConfigService sysConfigService;

    @Autowired
    private WechatAPI wechatAPI;

    @Autowired
    private ServiceTypeMapper serviceTypeMapper;

    @Autowired
    private ServiceAttributeOptionMapper serviceAttributeOptionMapper;

    @Autowired
    private CouponMapper couponMapper;

    @Autowired
    private PayQueryHandler payQueryHandler;


    private static OrderSequenceMapper orderSequenceMapper;
    @Autowired
    private CaregiverScheduleMapper caregiverScheduleMapper;
    @Autowired
    private CaregiverServiceTypeMapper caregiverServiceTypeMapper;
    @Autowired
    private UserByTypeStrategyContext userByTypeStrategyContext;

    @Autowired
    private void setOrderSequenceMapper(OrderSequenceMapper orderSequenceMapper) {
        OrderServiceImpl.orderSequenceMapper = orderSequenceMapper;
    }

    @Autowired
    private CancelReasonMapper cancelReasonMapper;

    @Autowired
    private OrderOperationLogMapper orderOperationLogMapper;

    @Autowired
    private ServiceAttributeServiceImpl serviceAttributeService;

    @Autowired
    private WebSocketServer webSocketServer;

    @Autowired
    private CouponServiceImpl couponService;

    @Autowired
    private NotificationUtil notificationUtil;

    @Autowired
    private CaregiverMapper caregiverMapper;

    /**
     * 获取各种类型订单数量
     */
    @Override
    public Map<String, Integer> getOrdersCountByUserId(Long userId) {


        // 查询所有订单的数量
        int totalOrders = Math.toIntExact(orderMapper.selectCount(new QueryWrapper<Order>().eq("user_id", userId).eq("user_is_delete", 0).ne("status", 7).ne("status", 4)));

        // 查询 status=1 的订单数量
        int status1Count = Math.toIntExact(orderMapper.selectCount(new QueryWrapper<Order>().eq("status", 1).eq("user_is_delete", 0).eq("user_id", userId)));

        // 查询 status=2 的订单数量
        int status2Count = Math.toIntExact(orderMapper.selectCount(new QueryWrapper<Order>().eq("status", 2).eq("user_is_delete", 0).eq("user_id", userId)));

        // 查询 status=3 的订单数量
        int status3Count = Math.toIntExact(orderMapper.selectCount(new QueryWrapper<Order>().eq("status", 3).eq("user_is_delete", 0).eq("user_id", userId)));

        // 构建返回数据
        Map<String, Integer> result = new HashMap<>();
        result.put("totalOrders", totalOrders);     //全部
        result.put("status1Count", status1Count);       //已预约
        result.put("status2Count", status2Count);       //进行中
        result.put("status3Count", status3Count);       //已完成

        return result;
    }


    @Override
    public ResultDTO<OrderDTO> orderAgain(String orderId) {
        Order order = orderMapper.selectById(orderId);
        if (order == null) {
            return ResultDTO.error("订单不存在");
        }

//        Long serviceTypeId = order.getServiceTypeId();
//        ServiceType serviceType = serviceTypeMapper.selectById(serviceTypeId);
//        String name = serviceType.getName();
        LambdaQueryWrapper<OrderAttribute> orderAttributeLambdaQueryWrapper = new LambdaQueryWrapper<>();
        orderAttributeLambdaQueryWrapper.eq(OrderAttribute::getOrderId, orderId);
        List<OrderAttribute> orderAttributes = orderAttributeMapper.selectList(orderAttributeLambdaQueryWrapper);
        List<Long> optionIds = orderAttributes.stream()
                .map(OrderAttribute::getAttributeOptionId)
                .collect(Collectors.toList());

        // 查询 service_attribute_option 表
        List<ServiceAttributeOption> optionList = new ArrayList<>();
        if (!optionIds.isEmpty()) {
            optionList = serviceAttributeOptionMapper.selectBatchIds(optionIds);
        }
        List<ServiceAttributeDTO.OptionDTO> optionDTOS = serviceAttributeService.getOptionDTOS(optionList);

        // 5. 设置 selectValue（根据 attributeKey 映射）
        Map<String, String> keyValueMap = orderAttributes.stream()
                .collect(Collectors.toMap(OrderAttribute::getAttributeKey, OrderAttribute::getAttributeValue, (v1, v2) -> v1));
        for (ServiceAttributeDTO.OptionDTO optionDTO : optionDTOS) {
            String key = optionDTO.getAttributeKey();
            String value = keyValueMap.get(key);
            optionDTO.setSelectValue(value); // 赋值
        }
        OrderDTO originalOrder = orderMapper.selectOrderAgainById(Long.parseLong(orderId));
        originalOrder.setOptions(optionDTOS);

        return ResultDTO.success("查询成功", originalOrder);

    }

    @Override
    public IPage<OrderDTO> getOrderByList(OrderDTO orderDTO) {
        Integer pageSize = orderDTO.getPageSize();
        Integer currentPage = orderDTO.getCurrentPage();
        Long serviceTypeId = orderDTO.getServiceTypeId();
        String area = orderDTO.getArea();

        Page<OrderDTO> page = new Page<>(currentPage, pageSize);

        // 正确写法：直接接收分页对象
        IPage<OrderDTO> pageResult = orderMapper.getOrderHallList(page, serviceTypeId, area);

        if (!pageResult.getRecords().isEmpty()) {
            List<Long> orderIds = pageResult.getRecords().stream()
                    .map(OrderDTO::getOrderId)
                    .collect(Collectors.toList());

            // 查询属性表
            List<OrderAttribute> attributeList = orderAttributeMapper.selectList(
                    new LambdaQueryWrapper<OrderAttribute>()
                            .in(OrderAttribute::getOrderId, orderIds)
            );
            List<OrderAttributeDTO> orderAttributeList = OrderAttributeDTOMapper.INSTANCE.toOrderAttributeList(attributeList);
            // 按 orderId 分组
            Map<Long, List<OrderAttributeDTO>> attrMap = orderAttributeList.stream()
                    .collect(Collectors.groupingBy(OrderAttributeDTO::getOrderId));

            // 回填属性
            for (OrderDTO dto : pageResult.getRecords()) {
                dto.setAttributes(attrMap.getOrDefault(dto.getOrderId(), Collections.emptyList()));
            }
        }

        return pageResult;
    }

    /**
     * 获取指定类型订单列表 - 没用
     */
    @Override
    public Page<Order> getOrdersByType(OrderDTO orderDTO, Integer page, Integer size) {
        Page<Order> pageObj = new Page<>(page, size);
        QueryWrapper<Order> queryWrapper = new QueryWrapper<>();

        queryWrapper.eq("status", orderDTO.getStatus())
                .eq("user_id", orderDTO.getUserId());
        queryWrapper.orderByDesc("update_time");
        // 分页查询订单
        Page<Order> resultPage = orderMapper.selectPage(pageObj, queryWrapper);

        // 遍历每个订单，手动补充 serviceTypeName 字段
        for (Order order : resultPage.getRecords()) {
            if (order.getServiceTypeId() != null) {
                // 查询 service_type 表中对应的服务类型名称
                ServiceType serviceType = serviceTypeMapper.selectById(order.getServiceTypeId());
                if (serviceType != null) {
                    order.setServiceTypeName(serviceType.getName()); // 补充非数据库字段
                }
            }
        }
        // 返回最终结果，前端就能看到 serviceTypeName 字段了
        return resultPage;
    }

    /**
     * 根据订单ID查询
     */
    @Override
    public OrderDTO getOrderById(Long id) {
        // 查询订单信息
        List<OrderDTO> orderDTOList = orderMapper.getOrderById(id);

        // 如果订单列表不为空，取第一个订单信息
        OrderDTO orderDTO = (orderDTOList != null && !orderDTOList.isEmpty()) ? orderDTOList.get(0) : null;

        // 查询与订单关联的所有属性
        if (orderDTO != null) {
            List<OrderAttributeDTO> attributes = orderMapper.getOrderAttributesByOrderId(id);
            orderDTO.setAttributes(attributes);  // 设置属性到 OrderDTO
        }

        return orderDTO;
    }

    /**
     * 分页查询：根据用户 ID 获取订单列表
     */

    @Override
    public Page<OrderDTO> getOrdersByUserIdPaged(Integer page, Integer size, Long userId) {
        Page<OrderDTO> pageObj = new Page<>(page, size);
        return orderMapper.getOrdersByUserIdPaged(pageObj, userId);
    }

    /**
     * 根据阿姨id，订单日期，查询订单信息
     */
    @Override
    public Page<OrderDTO> getOrdersByCaregiverPaged(Integer page, Integer size, Long caregiversId, Date orderDate) {
        // 计算分页的起始位置
        int offset = (page - 1) * size;

        // 创建一个 Map 用于传递参数
        Map<String, Object> params = new HashMap<>();
        params.put("caregiversId", caregiversId);
        params.put("orderDate", orderDate != null ? new SimpleDateFormat("yyyy-MM-dd").format(orderDate) : null);
        params.put("page", offset);
        params.put("size", size);

        // 查询订单数据及地址信息
        List<OrderDTO> order = orderMapper.getOrdersWithAddressInfo(params);

        /// 查询每个订单的属性信息，并将其添加到每个订单对象中
        for (OrderDTO orderDTO : order) {
            List<OrderAttributeDTO> attributes = orderMapper.getOrderAttibutes(orderDTO.getOrderId());
            orderDTO.setAttributes(attributes);  // 设置订单的属性
        }


        // 创建并返回分页结果，分页结果中只包含数据，不查询总数
        Page<OrderDTO> pageObj = new Page<>(page, size);
        pageObj.setRecords(order);

        return pageObj;
    }


    /**
     * 更改订单类型
     */
    @Override
    public ResultDTO<Long> putStatus(OrderDTO dto) {
        // 1. 根据 orderId 查当前订单
        Order order = orderMapper.selectById(dto.getOrderId());
        if (order == null) {
            return ResultDTO.error("订单不存在");
        }

        // 2. 只允许从类型 8 改为类型 3
        if (order.getStatus() != 8 || dto.getStatus() != 3) {
            return ResultDTO.error("当前订单类型不允许修改为目标类型");
        }

        // 3. 执行更新
        UpdateWrapper<Order> wrapper = new UpdateWrapper<>();
        wrapper.eq("order_id", dto.getOrderId())
                .eq("user_id", dto.getUserId()); // 防止越权操作别人的订单

        Order updateOrder = new Order();
        updateOrder.setStatus(dto.getStatus());

        int rows = orderMapper.update(updateOrder, wrapper);

        if (rows > 0) {
            return ResultDTO.success("订单类型修改成功", dto.getOrderId());
        } else {
            return ResultDTO.error("订单更新失败，请稍后再试");
        }
    }

    /**
     * 删除订单
     */
    @Override
    public Long deleteOrderId(OrderDTO orderDTO) {
        String loginIdAsString = StpUtil.getLoginIdAsString();
        LoginUserDTO loginUser = (LoginUserDTO) StpUtil.getSession().get(loginIdAsString);
        Integer userType = loginUser.getUserType();
        UserByTypeStrategyInterface typeInstance = userByTypeStrategyContext.getTypeInstance(userType);
        typeInstance.deleteOrder(orderDTO.getOrderId());
        return orderDTO.getOrderId();
    }

    /**
     * 阿姨同意或拒绝预约单
     */
    @Override
    public ResultDTO<?> handleOrderDecision(OrderDTO dto) {
        try {
            // 1. 查询订单是否存在
            Order order = orderMapper.selectById(dto.getOrderId());
            if (order == null) {
                return ResultDTO.error("订单不存在");
            }

            // 2. 权限判断（仅当已分配阿姨时生效）
            if (order.getCaregiversId() != null && !order.getCaregiversId().equals(dto.getCaregiversId())) {
                return ResultDTO.error("无权操作此订单");
            }

            // 3. 状态判断
            if (order.getStatus() != 0) {
                return ResultDTO.error("当前订单状态已处理，不能修改");
            }

            // 4. 接受 or 拒绝逻辑
            String reasonText = null;
            if (dto.getAccept()) {
                order.setStatus(1); // 同意：设为已接单
                order.setCaregiversId(dto.getCaregiversId()); // 写入阿姨ID
                orderMapper.updateById(order);

            } else {
                // 获取订单的预约时间 appointment_time
                Date appointmentTime = order.getAppointmentTime(); // 订单的预约时间

                // 获取预约时间的年月日部分
                LocalDateTime orderStartTime = appointmentTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();

                // 只保留年月日部分，时分秒清零
                LocalDateTime dateOnly = orderStartTime.toLocalDate().atStartOfDay();

                // 转换为 Date 进行数据库匹配
                Date finalOrderStartTime = Date.from(dateOnly.atZone(ZoneId.systemDefault()).toInstant());

                // 将原始的预约时间转为 LocalTime（保留时分秒）
                LocalTime localStartTime = LocalTime.of(orderStartTime.getHour(), orderStartTime.getMinute(), orderStartTime.getSecond());

                // 拒绝时处理取消原因
                reasonText = resolveReasonText(dto.getReasonId(), dto.getCustomReason());
                // 检查拒绝理由是否为空
                if (reasonText == null || reasonText.trim().isEmpty()) {
                    return ResultDTO.error("拒绝理由不能为空");
                }

                // 删除对应的日程记录
                caregiverScheduleMapper.delete(new QueryWrapper<CaregiverSchedule>()
                        .eq("caregiver_id", dto.getCaregiversId())
                        .eq("schedule_date", finalOrderStartTime) // 只需要年月日部分进行匹配
                        .eq("schedule_start_time", localStartTime) // 匹配具体时间
                );

                doRefund(order, order.getActualPaymentPrice(), order.getUserId(), 9, "",2);
            }

            // 6. 写入用户通知
            UserNotification notification = new UserNotification();
            notification.setOrderId(dto.getOrderId());
            notification.setCaregiverId(dto.getCaregiversId());

            if (dto.getAccept()) {
                notification.setMessage("阿姨已接受您的预约，订单号：" + order.getOrderNumber());
            } else {
                notification.setMessage("阿姨拒绝了预约，理由是：" + reasonText);
            }

            userNotificationMapper.insert(notification);

            // 7. 返回前端结果
            return ResultDTO.success(dto.getAccept() ? "预约单已接受" : "预约单已拒绝", null);

        } catch (IllegalArgumentException e) {
            return ResultDTO.error(e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            return ResultDTO.error("处理失败：" + e.getMessage());
        }
    }

    /**
     * 解析用户选择的拒绝原因（包括“其他”自定义情况）
     */
    private String resolveReasonText(Long reasonId, String customReason) {
        // 1. 如果没有选择取消原因，抛出异常
        if (reasonId == null) {
            throw new IllegalArgumentException("请选择取消原因");
        }

        // 2. 查询数据库中的取消原因
        CancelReason reason = cancelReasonMapper.selectById(reasonId);
        if (reason == null) {
            throw new IllegalArgumentException("无效的取消原因");
        }

        // 3. 如果选择了"其他"，但提供了自定义理由
        if ("其他".equals(reason.getReasonText()) && customReason != null && !customReason.trim().isEmpty()) {
            // 如果有自定义理由，返回自定义理由
            return customReason;  // 返回用户输入的自定义拒绝理由
        }

        // 4. 返回数据库中存储的拒绝原因文本
        return reason.getReasonText();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultDTO<Map<String, Object>> orderReleased(OrderDTO orderDTO) {

        Long userId;
        Integer userType;
        String phone;
        if (orderDTO.getUserId() != null) {
            userId = orderDTO.getUserId();
            userType = orderDTO.getUserType();
            phone = orderDTO.getPhone();
        } else {
            String loginIdAsString = StpUtil.getLoginIdAsString();
            LoginUserDTO loginInfo = (LoginUserDTO) StpUtil.getSession().get(loginIdAsString);
            phone = loginInfo.getPhone();
            userId = loginInfo.getUserId();
            userType = loginInfo.getUserType();
        }

        LambdaQueryWrapper<Wallet> walletLambdaQueryWrapper = new LambdaQueryWrapper<>();
        walletLambdaQueryWrapper.eq(Wallet::getUserId, userId)
                .eq(Wallet::getUserType, userType);
        Wallet wallet = walletMapper.selectOne(walletLambdaQueryWrapper);
        Long walletId = wallet.getWalletId();

        Long caregiversId = orderDTO.getCaregiversId();
        Long serviceTypeId = orderDTO.getServiceTypeId();
        if (caregiversId != null) {
            // 预约阿姨
            // 查询该阿姨是否有该服务类型资格
            LambdaQueryWrapper<CaregiverServiceType> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(CaregiverServiceType::getCaregiverId, caregiversId)
                    .eq(CaregiverServiceType::getServiceTypeId, serviceTypeId);

            long count = caregiverServiceTypeMapper.selectCount(wrapper);
            if (count == 0) {
                throw new BizException("该服务人员尚未具备此服务类型的接单资格");
            }
        }

        if (StringUtils.isNotBlank(orderDTO.getUserCouponId())) {
            // 检查使用的优惠券是否过期
            UserCoupon userCoupon = userCouponMapper.selectById(orderDTO.getUserCouponId());

            if (userCoupon == null || userCoupon.getStatus() == 2) {
                return ResultDTO.error("优惠券已使用或已过期");
            }
            if (userCoupon.getExpirationTime().before(new Date())) {
                return ResultDTO.error("优惠券已过期");
            }
            Coupon coupon = couponMapper.selectById(userCoupon.getCouponId());
            Integer couponType = coupon.getCouponType();

            if (couponType != 0 && couponType != serviceTypeId.intValue()) {
                return ResultDTO.error("优惠券不可用于此服务类型");
            }

            // 更新优惠券为已使用
            int res = userCouponMapper.updateUsedIfUnused(userCoupon.getUserCouponId(), 2);
            if (res == 0) {
                return ResultDTO.error("优惠券已被使用或过期");
            }
        }

        Integer payMethods = orderDTO.getPayMethods();
        if (payMethods == 1 || payMethods == 6) {
            orderDTO.setPayMethods4DB(1);
        } else {
            orderDTO.setPayMethods4DB(payMethods);
        }
        int payMethods4DB = orderDTO.getPayMethods4DB();

        Order order = createAndInsertOrder(orderDTO, userId, phone, 7);

        List<OrderAttribute> list = orderDTO.getAttributes().stream().map(attr -> {
            OrderAttribute entity = new OrderAttribute();
            entity.setOrderId(order.getOrderId());
            entity.setLabel(attr.getLabel());
            entity.setAttributeKey(attr.getAttributeKey());
            entity.setAttributeValue(attr.getValue());
            entity.setAttributeOptionId(attr.getAttributeOptionId());
            return entity;
        }).collect(Collectors.toList());

        orderAttributeMapper.insertBatch(list);

        // 预估时间
        int estimatedTime = calculateEstimatedTime(list, orderDTO.getServiceTypeId());
        Order updateOrder = new Order();
        updateOrder.setOrderId(order.getOrderId());
        updateOrder.setEstimatedTime(estimatedTime);
        orderMapper.updateById(updateOrder);


        // 维护阿姨预约时间
        insertCaregiverSchedule(order, estimatedTime);

        WalletTransaction walletTransaction = new WalletTransaction();
        walletTransaction.setWalletId(walletId);
        walletTransaction.setTransactionType(2);
        walletTransaction.setAmount(orderDTO.getActualPaymentPrice());
        walletTransaction.setTransactionTime(new Date());
        walletTransaction.setOutTradeNo(order.getOrderNumber());
        walletTransaction.setStatus(0);
        walletTransaction.setPaymentChannel(payMethods4DB);
        walletTransactionMapper.insert(walletTransaction);

        // 注意支付方式、支付密码
        // 获取钱包/微信/支付宝实例对象
        PayStrategy payStrategy = payContext.executePay(payMethods);
        // 调用钱包/微信/支付宝支付API
        HashMap<String, Object> map = new HashMap<>();
        map.put("userId", userId);
        map.put("userType", userType);
        map.put("payPassword", orderDTO.getPayPassword());
//        map.put("orderId", order.getOrderId());
        map.put("orderNumber", order.getOrderNumber());
        map.put("transactionType", 2); // 交易类型
        map.put("actualPaymentPrice", orderDTO.getActualPaymentPrice()); // 实付金额
        map.put("description", "下单");
//        map.put("payMethods", payMethods);
        map.put("code", orderDTO.getCode());
        Map<String, Object> pay = payStrategy.pay(map);
        HashMap<String, Object> payload = new HashMap<>();
        payload.put("orderId", order.getOrderId());
        if (payMethods == 3) {
            // 如果是钱包支付，支付后在这里进行更新订单、插入交易记录
            // 如果是组合支付，钱包支付过后判断是否还需微信支付，不需要：维护订单、交易记录；需要：微信支付后维护订单、交易记录
            payQueryHandler.walletPayUpdateTransaction(pay, order.getOrderNumber(), 1);
            payQueryHandler.walletPayUpdateOrder(order.getOrderId());

            // 发放优惠券  微信/支付宝支付等待回调时发放
            Map<String, Object> grantedCoupon = couponService.grantRandomCoupon(userId);
            if (grantedCoupon != null) {
                pay.put("grantedCoupon", grantedCoupon);
            }
            webSocketServer.sendMessageToAdminUserByOrder();
            // 预约指定阿姨
            if (caregiversId != null) {
                Caregiver caregiver = caregiverMapper.selectById(caregiversId);
                webSocketServer.sendMessageToTarget(
                        "caregiver:" + caregiversId,
                        payload,
                        WebSocketMessageType.WORKER_ORDER_RESPONSE.getType()
                );
                notificationUtil.sendNotificationToJPush(
                        CommonConstants.PUSH_APPOINTMENT_ORDER_TITLE,
                        CommonConstants.PUSH_APPOINTMENT_ORDER_ALERT,
                        AppType.CAREGIVER,
                        caregiver.getRegistrationId());
            }
        }
        // 组合支付
        if (payMethods == 4 || payMethods == 5 || payMethods == 7) {
            Boolean walletPayAll = (Boolean) pay.get("walletPayAll");
            if (walletPayAll != null) {
                // 发放优惠券
                Map<String, Object> grantedCoupon = couponService.grantRandomCoupon(userId);
                if (grantedCoupon != null) {
                    pay.put("grantedCoupon", grantedCoupon);
                }
                webSocketServer.sendMessageToAdminUserByOrder();
                Caregiver caregiver = caregiverMapper.selectById(caregiversId);
                // 钱包全部支付 = 钱包支付
                payQueryHandler.walletPayUpdateTransaction(pay, order.getOrderNumber(), 1);
                payQueryHandler.walletPayUpdateOrder(order.getOrderId());
                if (caregiver != null) {
                    webSocketServer.sendMessageToTarget(
                            "caregiver:" + caregiversId,
                            payload,
                            WebSocketMessageType.WORKER_ORDER_RESPONSE.getType()
                    );
                    notificationUtil.sendNotificationToJPush(
                            CommonConstants.PUSH_APPOINTMENT_ORDER_TITLE,
                            CommonConstants.PUSH_APPOINTMENT_ORDER_ALERT,
                            AppType.CAREGIVER,
                            caregiver.getRegistrationId());
                }

            } else {
                payQueryHandler.walletPayUpdateTransaction(pay, order.getOrderNumber(), null);
            }
        }

        return ResultDTO.success("下单成功", pay);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultDTO<String> refundOrder(String orderId) {
        String loginIdAsString = StpUtil.getLoginIdAsString();
        LoginUserDTO loginInfo = (LoginUserDTO) StpUtil.getSession().get(loginIdAsString);
        Long userId = loginInfo.getUserId();

        // 查询原订单信息
        Order originalOrder = orderMapper.selectById(orderId);
        if (originalOrder == null) {
            return ResultDTO.error("订单不存在");
        }

        Integer status = originalOrder.getStatus();
        BigDecimal refundAmount = originalOrder.getActualPaymentPrice();
        Date now = new Date();

        if (refundAmount == null || refundAmount.compareTo(BigDecimal.ZERO) <= 0) {
            return ResultDTO.error("退款金额无效");
        }

        // 状态 0：未接单，直接退款
        if (status == 0) {
            return doRefund(originalOrder, refundAmount, userId, 6, "订单已取消并退款成功", 1);
        }

        // 状态 1：已预约
        if (status == 1) {
            Date appointmentTime = originalOrder.getAppointmentTime();
            // 未到服务时间，允许全额退款，xx分钟内扣除xx%手续费
            if (appointmentTime != null && now.before(appointmentTime)) {
                // 删除阿姨日程
                LambdaQueryWrapper<CaregiverSchedule> caregiverScheduleLambdaQueryWrapper = new LambdaQueryWrapper<>();
                caregiverScheduleLambdaQueryWrapper.eq(CaregiverSchedule::getOrderId, orderId);
                caregiverScheduleMapper.delete(caregiverScheduleLambdaQueryWrapper);

                // 距离预约时间的毫秒差值
                long diffMillis = appointmentTime.getTime() - now.getTime();
                // nearServiceMinutes分钟内取消扣费
                int nearServiceMinutes = sysConfigService.getRefundFeeMinutesNearService();
                // 小于等于15分钟（15 * 60 * 1000 毫秒）
                boolean isWithin15Min = diffMillis <= nearServiceMinutes * 60 * 1000L;
                if (isWithin15Min) {
                    BigDecimal feeRate = sysConfigService.getRefundFeeRateNearService();
                    BigDecimal refundRatio = BigDecimal.ONE.subtract(feeRate);           // 1 - 0.05 = 0.95
                    BigDecimal deductedAmount = refundAmount.multiply(refundRatio)
                            .setScale(2, RoundingMode.HALF_UP);

                    return doRefund(originalOrder, deductedAmount, userId, 6, "服务即将开始，退款成功", 1);
                } else {
                    // 全额退款
                    return doRefund(originalOrder, refundAmount, userId, 6, "服务未开始，退款成功", 1);
                }
            } else {
              return ResultDTO.error("已到服务时间，无法退款");
            }


            // 服务时间已到，检查服务人员是否到场
//            Long addressId = originalOrder.getAddressId();
//            Address address = addressMapper.selectById(addressId);
//            Double userLongitude = address.getLongitude();
//            Double userLatitude = address.getLatitude();
//
//
//            Location caregiverLocation = locationMapper.selectOne(new LambdaQueryWrapper<Location>().eq(
//                    Location::getCaregiverId, caregiversId
//            ));
//            Double caregiverLongitude = caregiverLocation.getLongitude();
//            Double caregiverLatitude = caregiverLocation.getLatitude();
//
//            Double distance = amapService.getDistance(userLongitude, userLatitude, caregiverLongitude, caregiverLatitude);
//            if (distance != null && distance > 100) {
//                // 阿姨未到100米内
//                doRefund(originalOrder, refundAmount, userId, 6, "阿姨未到服务地点，退款成功");
//            } else {
//                return getStringResultDTO(originalOrder, userId);
//            }
        }

        // 状态 2：服务中，需提交退款申请，等待阿姨是否接受
//        if (status == 2) {
//            return getStringResultDTO(originalOrder, userId);
//        }

        return ResultDTO.notDataSuccess("该订单当前状态不支持退款申请");
    }

    /**
     * 退款申请提交至阿姨，等待阿姨处理
     *
     * @param originalOrder
     * @param userId
     * @return
     */
    private ResultDTO<String> getStringResultDTO(Order originalOrder, Long userId) {
        insertRefundLogChain(originalOrder.getOrderId(), userId, originalOrder.getStatus() == 2);
        Order order = new Order();
        order.setOrderId(originalOrder.getOrderId());
        order.setStatus(5); // 退款中
        orderMapper.updateById(order);
        // 消息推送给服务人员
        // 插入代办提醒信息
        ToDoReminders reminder = getToDoReminders(originalOrder);
        toDoRemindersMapper.insert(reminder);
        HashMap<String, Object> payload = new HashMap<>();
        payload.put("orderId", originalOrder.getOrderId());
        webSocketServer.sendMessageToTarget("caregiver:" + order.getCaregiversId(), payload, WebSocketMessageType.WAITING_REFUND_PROCESSING.getType());
        return ResultDTO.notDataSuccess("订单退款已提交，请等待服务人员反馈结果");
    }


    @Override
    public Map<String, Object> handleCallback(HttpServletRequest request) {
        log.info("微信支付回调开始");
        PayStrategy payStrategy = payContext.executePay(1);
        Transaction transaction = payStrategy.handleCallback(request);
        String tradeState = transaction.getTradeState().name();
        String outTradeNo = transaction.getOutTradeNo();
        Integer total = transaction.getAmount().getTotal();

        BigDecimal amountYuan = new BigDecimal(total)
                .divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP);

        Order order = payQueryHandler.getOrderInfo(outTradeNo);
        if (order == null) {
            HashMap<String, Object> resMap = new HashMap<>();
            resMap.put("code", 200);
            return resMap;
        }
        switch (tradeState) {
            case "SUCCESS":
                payQueryHandler.handlePaySuccess(order, outTradeNo, transaction.getTransactionId(), transaction.getSuccessTime(), amountYuan);
                webSocketServer.sendMessageToAdminUserByOrder();
                // 发送消息给阿姨,更新列表
                Long caregiversId = order.getCaregiversId();
                if (caregiversId != null) {
                    Caregiver caregiver = caregiverMapper.selectById(caregiversId);
                    webSocketServer.sendMessageToTarget(
                            "caregiver:" + caregiversId,
                            "",
                            WebSocketMessageType.ORDER_UPDATE.getType()
                    );
                    notificationUtil.sendNotificationToJPush(
                            CommonConstants.PUSH_APPOINTMENT_ORDER_TITLE,
                            CommonConstants.PUSH_APPOINTMENT_ORDER_ALERT,
                            AppType.CAREGIVER,
                            caregiver.getRegistrationId());
                }
                break;
            case "CLOSED":
                payQueryHandler.handlePayClosed(order, outTradeNo);
                break;
            default:
                break;
        }
        HashMap<String, Object> resMap = new HashMap<>();
        resMap.put("code", 200);
        return resMap;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultDTO<Map<String, Object>> searchWechatByOrderNumber(String orderNumber) {
        Transaction transaction = wechatAPI.searchOrder(orderNumber);
        String tradeState = transaction.getTradeState().name();
        String outTradeNo = transaction.getOutTradeNo();

        Order order = payQueryHandler.getOrderInfo(orderNumber);
        if (order == null) {
            return ResultDTO.error("订单不存在");
        }

        switch (tradeState) {
            case "SUCCESS":
                BigDecimal amountYuan = new BigDecimal(transaction.getAmount().getTotal()).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP);
                return payQueryHandler.handlePaySuccess(order, outTradeNo, transaction.getTransactionId(), transaction.getSuccessTime(), amountYuan);
            case "CLOSED":
                return payQueryHandler.handlePayClosed(order, outTradeNo);
            default:
                return payQueryHandler.handleNotPay();
        }
    }


    public void updateWalletTransaction(String outTradeNo, String transactionId, BigDecimal amountYuan, Integer status) {
        LambdaQueryWrapper<WalletTransaction> walletTransactionLambdaQueryWrapper = new LambdaQueryWrapper<>();
        walletTransactionLambdaQueryWrapper.eq(WalletTransaction::getOutTradeNo, outTradeNo)
                .eq(WalletTransaction::getTransactionType, 2);
        WalletTransaction walletTransaction = new WalletTransaction();
        walletTransaction.setStatus(status);
        walletTransaction.setExternalTradeNo(transactionId);
        walletTransaction.setExternalAmount(amountYuan);
        walletTransactionMapper.update(walletTransaction, walletTransactionLambdaQueryWrapper);
    }

    /**
     * 未支付删除钱包记录
     *
     * @param outTradeNo
     * @param transactionType (1充值、2消费、3提现、4退款、5收入)
     */
    public void deleteWalletTransaction(String outTradeNo, Integer transactionType) {
        walletTransactionMapper.delete(
                new LambdaQueryWrapper<WalletTransaction>()
                        .eq(WalletTransaction::getOutTradeNo, outTradeNo)
                        .eq(WalletTransaction::getTransactionType, transactionType)
                        .eq(WalletTransaction::getStatus, 0)); // 仅删除未支付记录
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultDTO<String> confirmOrder(String orderId) {
        // 查询订单
        Order order = orderMapper.selectById(orderId);
        if (order == null) {
            return ResultDTO.error("订单不存在");
        }

        // 使用悲观锁查询阿姨的钱包
        Wallet wallet = walletMapper.selectWalletByUserForUpdate(order.getCaregiversId(), 2);
        if (wallet == null) {
            return ResultDTO.error("阿姨钱包不存在");
        }

        // 更新钱包余额
        // amount : 阿姨收入
        BigDecimal amount = order.getCaregiverIncome();
        if (amount == null || amount.compareTo(BigDecimal.ZERO) <= 0) {
            return ResultDTO.error("订单金额无效");
        }
        Integer isLate = order.getIsLate();
        BigDecimal deductedAmount = BigDecimal.ZERO;
        BigDecimal actualAmount = amount;

        if (isLate!= null && isLate == 1) {
            // 阿姨迟到 扣除x%
            BigDecimal deductedRate = sysConfigService.getLateDeductedRateService();
            if (deductedRate != null && deductedRate.compareTo(BigDecimal.ZERO) > 0) {
                // 计算扣款金额 = 收入 * 扣率
                deductedAmount = amount.multiply(deductedRate).setScale(2, RoundingMode.HALF_UP);
                // 实际到账 = 收入 - 扣款
                actualAmount = amount.subtract(deductedAmount);
            }

        }
        wallet.setBalance(wallet.getBalance().add(actualAmount));
        wallet.setTotalIncome(wallet.getTotalIncome().add(actualAmount));
        walletMapper.updateById(wallet);

        // 插入钱包交易记录
        Date now = new Date();
        WalletTransaction record = new WalletTransaction();
        record.setWalletId(wallet.getWalletId());
        record.setAmount(actualAmount);
        record.setStatus(1);
        record.setTransactionTime(now);
        record.setTransactionType(5); // 类型：订单收入
        walletTransactionMapper.insert(record);

        // 更新订单状态
        Order updateOrder = new Order();
        updateOrder.setOrderId(order.getOrderId());
        updateOrder.setStatus(3); // 3 = 已完成
        updateOrder.setOrderCompletionTime(now);
        updateOrder.setCaregiverIncome(actualAmount);
        updateOrder.setMerchantIncome(order.getMerchantIncome().add(deductedAmount));
        orderMapper.updateById(updateOrder);

        return ResultDTO.notDataSuccess("订单确认成功");
    }

    /**
     * 创建订单并插入数据库
     *
     * @param orderDTO 订单数据传输对象
     * @param userId   用户ID
     * @param phone    用户手机号（用于生成订单号）
     * @param status   订单状态（如 0：未支付，1：已预约）
     * @return 创建成功返回订单对象，否则抛异常或返回 null
     */
    public Order createAndInsertOrder(OrderDTO orderDTO, Long userId, String phone, Integer status) {
        // 生成订单编号
        String orderNumber = generateOrderNumber(orderDTO.getServiceTypeId().toString(), phone);

        // 实际支付金额
        BigDecimal actualPayment = orderDTO.getActualPaymentPrice();
        BigDecimal totalPrice = orderDTO.getTotalPrice();

        // 抽成比例
        BigDecimal commissionRate = sysConfigService.getCommissionRate();
        // 计算分成
        BigDecimal caregiverIncome = totalPrice.subtract(new BigDecimal(20)).multiply(BigDecimal.ONE.subtract(commissionRate)).setScale(2, RoundingMode.HALF_UP);
        BigDecimal merchantIncome = totalPrice.subtract(caregiverIncome); // 剩余部分作为平台服务费

        Order order = new Order();
        order.setOrderNumber(orderNumber);
        order.setUserId(userId);
        order.setCaregiversId(orderDTO.getCaregiversId());
        order.setAddressId(orderDTO.getAddressId());
        order.setStatus(status);
        order.setServiceTypeId(orderDTO.getServiceTypeId());
        order.setAppointmentTime(orderDTO.getAppointmentTime());
        order.setTotalPrice(orderDTO.getTotalPrice());
        order.setPreferentialPrice(orderDTO.getPreferentialPrice());
        order.setActualPaymentPrice(actualPayment);
        order.setPayMethods(orderDTO.getPayMethods());
        order.setView(0);
        order.setCouponId(StringUtils.isBlank(orderDTO.getUserCouponId()) ? null : Long.valueOf(orderDTO.getUserCouponId()));
        Integer orderType = 2;
        if (orderDTO.getCaregiversId() != null) {
            orderType = 3;
        }
        order.setOrderType(orderType);

        // 设置收入字段
        order.setCaregiverIncome(caregiverIncome);
        order.setMerchantIncome(merchantIncome);

        int result = orderMapper.insert(order);
        if (result <= 0) {
            throw new RuntimeException("订单保存失败");
        }

        return order;
    }

    /**
     * 生成订单编号
     *
     * @param orderType   订单类型 服务类型。阿姨清洁物品商城为20
     * @param phoneNumber 手机号
     * @return
     */
    public static String generateOrderNumber(String orderType, String phoneNumber) {
        Calendar now = Calendar.getInstance();

        // 年、月、日、时分秒
        String year = String.valueOf(now.get(Calendar.YEAR));
        String month = String.format("%02d", now.get(Calendar.MONTH) + 1); // 月份从0开始
        String day = String.format("%02d", now.get(Calendar.DAY_OF_MONTH));
        String time = new SimpleDateFormat("HHmmss").format(now.getTime());

        // 4位随机数
        int randomNum = new Random().nextInt(9000) + 1000;

        // 订单类型（建议固定两位）
        String typeCode = String.format("%01d", Integer.parseInt(orderType));

        // 手机号后四位
        String phoneSuffix = phoneNumber.length() >= 2
                ? phoneNumber.substring(phoneNumber.length() - 2)
                : String.format("%02d", Integer.parseInt(phoneNumber));

        long todaySequence = getTodaySequence();
//        String sequenceStr = String.format("%05d", todaySequence);
        // 拼接成订单号
        return year + randomNum + month + typeCode + day + time + phoneSuffix + todaySequence;
    }

    /**
     * 获取今天序列号
     *
     * @return
     */
    public static long getTodaySequence() {
        String today = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        OrderSequence orderSequence = orderSequenceMapper.selectByDate(today);
        if (orderSequence == null) {
            // 第一次插入，序列号为 1
            OrderSequence insertOrderSequence = new OrderSequence();
            insertOrderSequence.setSeqDate(today);
            insertOrderSequence.setCurrentSeq(1L);
            insertOrderSequence.setUpdateTime(new Date());

            orderSequenceMapper.insert(insertOrderSequence);
            return 1;
        } else {
            // 已存在，更新并返回
            orderSequenceMapper.updateSequence(today);
            OrderSequence updated = orderSequenceMapper.selectByDate(today);
            return updated.getCurrentSeq();
        }
    }

    /**
     * 更新待支付订单状态
     *
     * @param outTradeNo
     * @param transactionId
     * @param successTime
     * @param expectedStatus
     */
    public void updateOrderPayment(String outTradeNo, String transactionId, String successTime, int expectedStatus) {
        Date payTime = null;
        if (StringUtils.isNotBlank(successTime)) {
            // 解析带时区的时间，例如 "2025-07-04T10:45:02+08:00"
            OffsetDateTime offsetDateTime = OffsetDateTime.parse(successTime);
            payTime = Date.from(offsetDateTime.toInstant());
        }

        // 先查询现有订单
        LambdaQueryWrapper<Order> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Order::getOrderNumber, outTradeNo)
                .eq(Order::getStatus, 7);
        Order existingOrder = orderMapper.selectOne(queryWrapper);

        if (existingOrder == null) {
            log.warn("未找到待支付订单: {}", outTradeNo);
            return;
        }

        // 创建更新对象
        Order order = new Order();
        order.setPayTime(payTime);
        order.setStatus(expectedStatus);

        // 根据现有订单的支付订单号情况来设置
        if (StringUtils.isBlank(existingOrder.getPayOrderNumber())) {
            order.setPayOrderNumber(transactionId);
        } else {
            order.setSecondaryPayOrderNumber(transactionId);
        }

        // 更新订单
        LambdaQueryWrapper<Order> updateWrapper = new LambdaQueryWrapper<>();
        updateWrapper.eq(Order::getOrderNumber, outTradeNo)
                .eq(Order::getStatus, 7);

        orderMapper.update(order, updateWrapper);
    }

    /**
     * 用户退款 ： 退款扣除手续费归商家收入
     *
     * @param order
     * @param refundAmount
     * @param userId
     * @param newStatus
     * @param successMessage
     * @param refundType        退款类型：1-用户退款，2-阿姨拒绝订单退款
     * @return
     */
    private ResultDTO<String> doRefund(Order order, BigDecimal refundAmount, Long userId, Integer newStatus, String successMessage, Integer refundType) {
        // 加锁查询用户钱包
        Wallet originalWallet = walletMapper.selectWalletByUserForUpdate(userId, 1);
        if (originalWallet == null) {
            return ResultDTO.error("用户钱包不存在");
        }

        // 查询该订单原交易记录
        String orderNumber = order.getOrderNumber();
        WalletTransaction originalTransaction = walletTransactionMapper.selectOne(
                new LambdaQueryWrapper<WalletTransaction>()
                        .eq(WalletTransaction::getOutTradeNo, orderNumber)
                        .eq(WalletTransaction::getTransactionType, 2)
        );

        if (originalTransaction == null) {
            return ResultDTO.error("未找到原始交易记录");
        }

        BigDecimal totalPaid = originalTransaction.getAmount();
        if (totalPaid == null || totalPaid.compareTo(BigDecimal.ZERO) <= 0) {
            return ResultDTO.error("原交易金额无效");
        }

        // 计算退款比例和剩余未退金额
        BigDecimal refundRatio = refundAmount.divide(totalPaid, 10, RoundingMode.HALF_UP);
        BigDecimal remainAmount = totalPaid.subtract(refundAmount)
                .setScale(2, RoundingMode.HALF_UP);

        // 原始支付来源金额
        BigDecimal rechargeAmount = originalTransaction.getRechargeAmount() == null ? BigDecimal.ZERO : originalTransaction.getRechargeAmount();
        BigDecimal balanceAmount = originalTransaction.getBalanceAmount() == null ? BigDecimal.ZERO : originalTransaction.getBalanceAmount();
        BigDecimal externalAmount = originalTransaction.getExternalAmount() == null ? BigDecimal.ZERO : originalTransaction.getExternalAmount();

        // 按比例拆分应退金额
        BigDecimal refundRechargeAmount = rechargeAmount.multiply(refundRatio).setScale(2, RoundingMode.HALF_UP);
        BigDecimal refundBalanceAmount = balanceAmount.multiply(refundRatio).setScale(2, RoundingMode.HALF_UP);
        BigDecimal refundExternalAmount = externalAmount.multiply(refundRatio).setScale(2, RoundingMode.HALF_UP);

        // 更新用户钱包（退款）
        Wallet updateWallet = new Wallet();
        updateWallet.setWalletId(originalWallet.getWalletId());
        updateWallet.setRechargeBalance(originalWallet.getRechargeBalance().add(refundRechargeAmount));
        updateWallet.setBalance(originalWallet.getBalance()
                .add(refundBalanceAmount)
                .add(refundExternalAmount));
        walletMapper.updateById(updateWallet);

        // 插入退款交易记录
        WalletTransaction refundTransaction = new WalletTransaction();
        refundTransaction.setWalletId(originalWallet.getWalletId());
        refundTransaction.setTransactionType(4); // 退款
        refundTransaction.setAmount(refundAmount);
        refundTransaction.setTransactionTime(new Date());
        refundTransaction.setStatus(1); // 成功
        refundTransaction.setRechargeAmount(refundRechargeAmount);
        refundTransaction.setBalanceAmount(refundBalanceAmount);
        refundTransaction.setExternalAmount(refundExternalAmount);
        refundTransaction.setOutTradeNo(orderNumber);
        walletTransactionMapper.insert(refundTransaction);
        // 初始化
//        BigDecimal caregiverIncome = BigDecimal.ZERO;
//        BigDecimal merchantIncome = BigDecimal.ZERO;
        // 如果存在剩余未退金额，则作为阿姨收入入账
//        if (remainAmount.compareTo(BigDecimal.ZERO) > 0 && order.getCaregiversId() != null) {
//            Wallet auntWallet = walletMapper.selectWalletByUserForUpdate(order.getCaregiversId(), 2); // 2 = 阿姨
//            if (auntWallet != null) {
//                // 抽成比例
//                BigDecimal commissionRate = sysConfigService.getCommissionRate();
//                // 阿姨收入
//                caregiverIncome = remainAmount.multiply(BigDecimal.ONE.subtract(commissionRate)).setScale(2, RoundingMode.HALF_UP);
//                // 平台收入
//                merchantIncome = remainAmount.subtract(caregiverIncome); // 剩余部分作为平台服务费
//
//                // 更新阿姨钱包余额
//                Wallet updateAuntWallet = new Wallet();
//                updateAuntWallet.setWalletId(auntWallet.getWalletId());
//                updateAuntWallet.setBalance(auntWallet.getBalance().add(caregiverIncome));
//                walletMapper.updateById(updateAuntWallet);
//
//                // 插入阿姨收入记录
//                WalletTransaction incomeTransaction = new WalletTransaction();
//                incomeTransaction.setWalletId(auntWallet.getWalletId());
//                incomeTransaction.setTransactionType(3); // 收入
//                incomeTransaction.setAmount(caregiverIncome);
//                incomeTransaction.setTransactionTime(new Date());
//                incomeTransaction.setStatus(1); // 成功
//                incomeTransaction.setOutTradeNo(orderNumber);
//                walletTransactionMapper.insert(incomeTransaction);
//            }
//        }

        // 更新订单状态
        Order updateOrder = new Order();
        updateOrder.setOrderId(order.getOrderId());
        updateOrder.setStatus(newStatus);
        updateOrder.setOrderCompletionTime(new Date());
        updateOrder.setCaregiverIncome(BigDecimal.ZERO);
        updateOrder.setMerchantIncome(remainAmount);

        updateOrder.setCaregiverRefundConfirmation(0);
        orderMapper.updateById(updateOrder);

        insertRefundLogChain(order.getOrderId(), userId, order.getStatus() == 2);
        if (refundType == 1) {
            HashMap<String, Object> payload = new HashMap<>();
            payload.put("orderId", order.getOrderId());
            webSocketServer.sendMessageToTarget("caregiver:" + order.getCaregiversId(), payload, WebSocketMessageType.DIRECT_REFUNDS.getType());
            Caregiver caregiver = caregiverMapper.selectById(order.getCaregiversId());
            if (caregiver != null) {
                notificationUtil.sendNotificationToJPush(
                        CommonConstants.PUSH_REFUND_ORDER_TITLE,
                        CommonConstants.PUSH_REFUND_ORDER_ALERT,
                        AppType.CAREGIVER,
                        caregiver.getRegistrationId());
            }
        }
        return ResultDTO.notDataSuccess(successMessage);
    }

    /**
     * 插入订单退款记录
     *
     * @param orderId   订单id
     * @param userId    用户id
     * @param needAudit 是否立即处理退款
     */
    public void insertRefundLogChain(Long orderId, Long userId, boolean needAudit) {
        Date baseTime = new Date(); // 当前时间
        int secondsOffset = 0;

        // 申请退款
        insertOperationLog(orderId, userId, 1, 5, "申请退款", DateUtil.offsetTime(baseTime, secondsOffset++));

        // 退款审核中
        insertOperationLog(orderId, 0L, 4, 7, "退款审核中", DateUtil.offsetTime(baseTime, secondsOffset++));

        if (!needAudit) {
            // 退款成功
            insertOperationLog(orderId, 0L, 4, 6, "退款已处理，退款成功", DateUtil.offsetTime(baseTime, secondsOffset));
        }
    }

    /**
     * 插入订单操作记录
     *
     * @param orderId       订单id
     * @param operatorId    操作人id
     * @param operatorType  操作人类型
     * @param operationType 操作类型
     * @param operationDesc 操作描述
     */
    public void insertOperationLog(Long orderId, Long operatorId, Integer operatorType, Integer operationType, String operationDesc, Date time) {
        OrderOperationLog log = new OrderOperationLog();
        log.setOrderId(orderId);
        log.setOperatorId(operatorId);
        log.setOperatorType(operatorType);
        log.setOperationType(operationType);
        log.setOperationDesc(operationDesc);
        log.setCreateTime(time);
        orderOperationLogMapper.insert(log);
    }

    private static ToDoReminders getToDoReminders(Order originalOrder) {
        ToDoReminders reminder = new ToDoReminders();
        reminder.setCaregiverId(originalOrder.getCaregiversId());
        reminder.setOrderId(originalOrder.getOrderId());
        reminder.setTitle("用户申请订单退款处理");
        reminder.setDescription("订单号 " + originalOrder.getOrderId() + " 已提交退款申请，请及时处理");
        reminder.setReminderTime(new Date()); // 当前时间提醒
        reminder.setStatus(1); // 1: 待处理
        return reminder;
    }

    /**
     * 计算服务预估时间
     *
     * @param list        服务类别具体属性
     * @param serviceType 服务类别id
     * @return
     */
    public int calculateEstimatedTime(List<OrderAttribute> list, Long serviceType) {
        String code = serviceTypeMapper.selectById(serviceType).getCode();
        List<ServiceAttributeOption> optionList = serviceAttributeOptionMapper.selectList(
                new LambdaQueryWrapper<ServiceAttributeOption>().eq(ServiceAttributeOption::getServiceTypeCode, code));

        if ("pioneer_clean".equals(code)) {
            return 720;
        }

        // 总时间累加
        BigDecimal totalEstimatedTime = BigDecimal.ZERO;
        BigDecimal multiplicationFactor = BigDecimal.ZERO;
        BigDecimal totalMultiple = BigDecimal.ONE;

        BigDecimal cleanArea = BigDecimal.ONE;
        for (OrderAttribute attr : list) {
            String attrKey = attr.getAttributeKey();
            String attrValue = attr.getAttributeValue();
            if ("clean_area".equals(attrKey)) {
                cleanArea = new BigDecimal(attrValue);
            }
        }
        for (OrderAttribute attr : list) {
            String attrKey = attr.getAttributeKey();
            String attrValue = attr.getAttributeValue();
            for (ServiceAttributeOption option : optionList) {
                if (!option.getAttributeKey().equals(attrKey)) {
                    continue;
                }

                BigDecimal estimatedTime = option.getEstimatedTime();
                if (estimatedTime == null) {
                    continue;
                }

                String label = option.getLabel();
                String calculationMethod = option.getCalculationMethod();
                boolean matched = false;

                // 特殊处理 inner_window_frame，直接乘以 cleanArea
                if ("内窗框".equals(attrValue) && "内窗框".equals(label)) {
                    totalEstimatedTime = totalEstimatedTime.add(estimatedTime.multiply(cleanArea));
                    continue; // 跳过当前循环，避免重复匹配
                }

                // 常规匹配方式：值相等 或者 值在区间范围
                if (label.equals(attrValue)) {
                    matched = true;
                } else if (label.matches("\\d+\\s*-\\s*\\d+") && NumberUtils.isCreatable(attrValue)) {
                    String[] parts = label.split("-");
                    int lower = Integer.parseInt(parts[0].trim());
                    int upper = Integer.parseInt(parts[1].trim());
                    int value = new BigDecimal(attrValue.trim()).intValue();

                    if (value >= lower && value <= upper) {
                        matched = true;
                    }
                }

                if (matched) {
                    if ("*".equals(calculationMethod)) {
                        if (NumberUtils.isCreatable(attrValue)) {
                            BigDecimal numericValue = new BigDecimal(attrValue.trim());
                            multiplicationFactor = estimatedTime.multiply(numericValue);
                        } else {
                            // 计算glass_size_through：玻璃大小是否能通过人
                            totalMultiple = totalMultiple.multiply(estimatedTime);
                        }
                    } else {
                        totalEstimatedTime = totalEstimatedTime.add(estimatedTime);
                    }
                }
            }
        }
        return (totalEstimatedTime.add(multiplicationFactor))
                .multiply(totalMultiple)
                .setScale(0, RoundingMode.HALF_UP)
                .intValue();
    }

    /**
     * 修改未接单或已预约状态下的订单信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultDTO<Map<String,Object>> updateOrderInfo(OrderDTO orderDTO) {
        String loginIdAsString = StpUtil.getLoginIdAsString();
        LoginUserDTO loginInfo = (LoginUserDTO) StpUtil.getSession().get(loginIdAsString);
        String phone = loginInfo.getPhone();
        Long userId = loginInfo.getUserId();
        Integer userType = loginInfo.getUserType();
        LambdaQueryWrapper<Wallet> walletLambdaQueryWrapper = new LambdaQueryWrapper<>();
        walletLambdaQueryWrapper.eq(Wallet::getUserId, userId)
                .eq(Wallet::getUserType, userType);
        String orderNumber = OrderServiceImpl.generateOrderNumber("20", phone);
        Integer payMethods = orderDTO.getPayMethods();

        Map<String, Object> pay = null; // 声明pay变量

        // 1. 查询原始订单数据
        Order order = orderMapper.selectById(orderDTO.getOrderId());
        if (order == null) {
            throw new RuntimeException("订单不存在");
        }

        // 2. 判断是否为未接单或已预约状态（一般 Controller 判断过了，这里保险再加）
        if (order.getStatus() != 0 && order.getStatus() != 1) {
            throw new RuntimeException("仅允许修改未接单或已预约状态的订单");
        }
        //判断是否可修改
        LocalDateTime now = LocalDateTime.now(); // 初始化now变量
        if (orderDTO.getAppointmentTime() != null) {
            LocalDateTime appointmentTime = orderDTO.getAppointmentTime().toInstant()
                    .atZone(ZoneId.systemDefault()).toLocalDateTime();
            if (appointmentTime.isBefore(now)) {
                throw new RuntimeException("不能修改今天之前的预约时间");
            }
        }

        // 3. 判断是否临近 15 分钟，并且用户已确认

        boolean needPayServiceFee = false;
        BigDecimal serviceFee = BigDecimal.ZERO;

        if (order.getAppointmentTime() != null) {
            LocalDateTime oldTime = order.getAppointmentTime()
                    .toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
            Duration duration = Duration.between(now, oldTime);

            if (!duration.isNegative() && duration.toMinutes() <= 15) {
                if (!Boolean.TRUE.equals(orderDTO.getConfirmModify())) {
                    throw new RuntimeException("临近预约时间，修改需支付 5% 服务费，请先确认");
                }

                // 计算服务费
                BigDecimal actualPayment = order.getActualPaymentPrice();
                if (actualPayment != null) {
                    serviceFee = actualPayment.multiply(new BigDecimal("0.05"))
                            .setScale(2, RoundingMode.HALF_UP);
                    needPayServiceFee = true;
                }
            }
        }

        // 4. 如果需要支付服务费，先处理支付
        if (needPayServiceFee && serviceFee.compareTo(BigDecimal.ZERO) > 0) {
            try {
                // 获取钱包/微信/支付宝实例对象
                PayStrategy payStrategy = payContext.executePay(payMethods);

                // 构建支付请求参数
                HashMap<String, Object> map = new HashMap<>();
                map.put("userId", userId);
                map.put("userType", userType);
                map.put("payPassword", orderDTO.getPayPassword());
                map.put("secondaryOrderNumber", orderNumber); // 生成新的交易号
                map.put("transactionType", 2);
                map.put("actualPaymentPrice", serviceFee); // 支付服务费金额
                map.put("description", "订单修改服务费");
                map.put("payMethods", payMethods);
                map.put("code", orderDTO.getCode());

                // 调用支付接口
                pay = payStrategy.pay(map); // 赋值给外部声明的pay变量

                // 处理支付结果
                if (payMethods == 3) {
                    // 钱包支付，支付后更新交易记录
                    payQueryHandler.walletPayUpdateTransaction(pay, (String) map.get("orderNumber"), 1);
                } else if (payMethods == 4 || payMethods == 5 || payMethods == 7) {
                    // 组合支付处理
                    Boolean walletPayAll = (Boolean) pay.get("walletPayAll");
                    if (walletPayAll != null && walletPayAll) {
                        payQueryHandler.walletPayUpdateTransaction(pay, (String) map.get("orderNumber"), 1);
                    } else {
                        payQueryHandler.walletPayUpdateTransaction(pay, (String) map.get("orderNumber"), null);
                    }
                }
                // 微信/支付宝支付会通过回调处理，这里不需要额外处理

                // 支付成功后更新订单的平台收益和实付金额
                BigDecimal oldIncome = order.getMerchantIncome();
                if (oldIncome == null) {
                    oldIncome = BigDecimal.ZERO;
                }
                order.setMerchantIncome(oldIncome.add(serviceFee)); // 叠加收益
                order.setActualPaymentPrice(order.getActualPaymentPrice().add(serviceFee)); // 增加实付金额

            } catch (Exception e) {
                log.error("支付服务费失败", e);
                throw new RuntimeException("支付服务费失败: " + e.getMessage());
            }
        }

        // 5. 将原预约时间保存到 lastAppointmentTime 字段中
        orderDTO.setLastAppointmentTime(order.getAppointmentTime());

        // 6. 设置修改字段
        if (orderDTO.getAppointmentTime() != null) {
            order.setAppointmentTime(orderDTO.getAppointmentTime());
            order.setLastAppointmentTime(orderDTO.getLastAppointmentTime());
        }
        order.setSecondaryOrderNumber(orderNumber);
        order.setStatus(10); // 修改状态为 10

        // 7. 最终统一更新一次
        boolean updateSuccess = orderMapper.updateById(order) > 0;

        // 8. 在 caregiver_schedule 表中插入新数据
        if (updateSuccess && order.getCaregiversId() != null && orderDTO.getAppointmentTime() != null) {
            try {
                CaregiverSchedule caregiverSchedule = new CaregiverSchedule();
                caregiverSchedule.setCaregiverId(order.getCaregiversId());
                caregiverSchedule.setScheduleType(1);

                // 获取预约时间的年月日部分
                LocalDateTime appointmentDate = order.getAppointmentTime().toInstant()
                        .atZone(ZoneId.systemDefault()).toLocalDateTime().toLocalDate().atStartOfDay();
                Date scheduleDate = Date.from(appointmentDate.atZone(ZoneId.systemDefault()).toInstant());
                caregiverSchedule.setScheduleDate(scheduleDate);

                // 获取预约时间的时分秒部分
                LocalTime appointmentTimeOnly = order.getAppointmentTime().toInstant()
                        .atZone(ZoneId.systemDefault()).toLocalDateTime().toLocalTime();

                // 获取订单的预计时间（单位为分钟）
                int estimatedTime = order.getEstimatedTime();
                LocalTime scheduleEndTime = appointmentTimeOnly.plusMinutes(estimatedTime);

                caregiverSchedule.setScheduleStartTime(appointmentTimeOnly);
                caregiverSchedule.setScheduleEndTime(scheduleEndTime);

                // 插入数据到 caregiver_schedule 表
                caregiverScheduleMapper.insert(caregiverSchedule);
            } catch (Exception e) {
                log.error("插入排班数据失败", e);
                // 不抛出异常，避免影响主流程
            }
        }

        Map<String, Object> result = new HashMap<>();
        result.put("success", updateSuccess);
        if (needPayServiceFee) {
            result.put("serviceFee", serviceFee);
            result.put("payResult", pay);
        }

        return ResultDTO.success("修改成功", result);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateOrderStatus(OrderDTO orderDTO) {
        String loginIdAsString = StpUtil.getLoginIdAsString();
        LoginUserDTO loginInfo = (LoginUserDTO) StpUtil.getSession().get(loginIdAsString);
        Long userId = loginInfo.getUserId();
        // 查询订单信息
        Order order = orderMapper.selectById(orderDTO.getOrderId());
        BigDecimal refundAmount = order.getActualPaymentPrice();
        Integer status = order.getStatus();

        LambdaUpdateWrapper<Order> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(Order::getOrderId, orderDTO.getOrderId())
                .set(Order::getStatus, orderDTO.getStatus());
        boolean updateSuccess = orderMapper.update(null, updateWrapper) > 0;

        // 如果更新成功，并且 status = 1，则删除 caregiver_schedule 表中的相关记录
        if (updateSuccess && orderDTO.getStatus() == 1) {
            LocalDateTime appointmentDate = order.getAppointmentTime().toInstant()
                    .atZone(ZoneId.systemDefault()).toLocalDateTime().toLocalDate().atStartOfDay();
            Date scheduleDate = Date.from(appointmentDate.atZone(ZoneId.systemDefault()).toInstant());

            // 提取订单的 last_appointment_time 的时分秒部分
            LocalTime lastAppointmentTimeOnly = order.getLastAppointmentTime().toInstant()
                    .atZone(ZoneId.systemDefault()).toLocalDateTime().toLocalTime();

            // 调整为正确的 Time 类型
            java.sql.Time lastTime = java.sql.Time.valueOf(lastAppointmentTimeOnly);

            // 创建删除条件
            LambdaQueryWrapper<CaregiverSchedule> deleteWrapper = new LambdaQueryWrapper<>();
            deleteWrapper.eq(CaregiverSchedule::getCaregiverId, order.getCaregiversId())  // 根据阿姨ID删除
                    .eq(CaregiverSchedule::getScheduleDate, scheduleDate)  // 根据年月日部分删除
                    .eq(CaregiverSchedule::getScheduleStartTime, lastTime);  // 根据 last_appointment_time 的时分秒部分删除

            // 执行删除操作
            int deletedCount = caregiverScheduleMapper.delete(deleteWrapper);  // 删除匹配条件的记录
        } else if (updateSuccess && orderDTO.getStatus() == 0) { // 如果更新成功，并且状态为 0（取消）
            if (order.getOrderType() == 3) {
                // 执行删除排班操作
                LocalDateTime latAppointmentDate = order.getLastAppointmentTime().toInstant()
                        .atZone(ZoneId.systemDefault()).toLocalDateTime().toLocalDate().atStartOfDay();
                LocalTime lastAppointmentTimeOnly = order.getLastAppointmentTime().toInstant()
                        .atZone(ZoneId.systemDefault()).toLocalDateTime().toLocalTime();

                LocalDateTime appointmentDate = order.getAppointmentTime().toInstant()
                        .atZone(ZoneId.systemDefault()).toLocalDateTime().toLocalDate().atStartOfDay();
                LocalTime appointmentTimeOnly = order.getAppointmentTime().toInstant()
                        .atZone(ZoneId.systemDefault()).toLocalDateTime().toLocalTime();

                java.sql.Time lastTime = java.sql.Time.valueOf(lastAppointmentTimeOnly);
                java.sql.Time appointmentTime = java.sql.Time.valueOf(appointmentTimeOnly);

                // 创建删除条件
                LambdaQueryWrapper<CaregiverSchedule> deleteWrapper = new LambdaQueryWrapper<>();
                deleteWrapper.eq(CaregiverSchedule::getCaregiverId, order.getCaregiversId())
                        .eq(CaregiverSchedule::getScheduleDate, Date.from(latAppointmentDate.atZone(ZoneId.systemDefault()).toInstant()))
                        .eq(CaregiverSchedule::getScheduleStartTime, lastTime);
                caregiverScheduleMapper.delete(deleteWrapper);

                LambdaQueryWrapper<CaregiverSchedule> additionalDeleteWrapper = new LambdaQueryWrapper<>();
                additionalDeleteWrapper.eq(CaregiverSchedule::getCaregiverId, order.getCaregiversId())
                        .eq(CaregiverSchedule::getScheduleDate, Date.from(appointmentDate.atZone(ZoneId.systemDefault()).toInstant()))
                        .eq(CaregiverSchedule::getScheduleStartTime, appointmentTime);
                caregiverScheduleMapper.delete(additionalDeleteWrapper);

                // 排班删除后，将订单状态更新为 9
//                LambdaUpdateWrapper<Order> updateWrapperForStatus9 = new LambdaUpdateWrapper<>();
//                updateWrapperForStatus9.eq(Order::getOrderId, orderDTO.getOrderId())
//                        .set(Order::getStatus, 9);
//                orderMapper.update(null, updateWrapperForStatus9);  // 更新订单状态为 9
                doRefund(order, refundAmount, order.getUserId(), 9, "", 2);
            } else {
                LocalDateTime latAppointmentDate = order.getLastAppointmentTime().toInstant()
                        .atZone(ZoneId.systemDefault()).toLocalDateTime().toLocalDate().atStartOfDay();
                LocalTime lastAppointmentTimeOnly = order.getLastAppointmentTime().toInstant()
                        .atZone(ZoneId.systemDefault()).toLocalDateTime().toLocalTime();

                LocalDateTime appointmentDate = order.getAppointmentTime().toInstant()
                        .atZone(ZoneId.systemDefault()).toLocalDateTime().toLocalDate().atStartOfDay();
                LocalTime appointmentTimeOnly = order.getAppointmentTime().toInstant()
                        .atZone(ZoneId.systemDefault()).toLocalDateTime().toLocalTime();

                java.sql.Time lastTime = java.sql.Time.valueOf(lastAppointmentTimeOnly);
                java.sql.Time appointmentTime = java.sql.Time.valueOf(appointmentTimeOnly);

                // 创建删除条件
                LambdaQueryWrapper<CaregiverSchedule> deleteWrapper = new LambdaQueryWrapper<>();
                deleteWrapper.eq(CaregiverSchedule::getCaregiverId, order.getCaregiversId())
                        .eq(CaregiverSchedule::getScheduleDate, Date.from(latAppointmentDate.atZone(ZoneId.systemDefault()).toInstant()))
                        .eq(CaregiverSchedule::getScheduleStartTime, lastTime);
                caregiverScheduleMapper.delete(deleteWrapper);

                LambdaQueryWrapper<CaregiverSchedule> additionalDeleteWrapper = new LambdaQueryWrapper<>();
                additionalDeleteWrapper.eq(CaregiverSchedule::getCaregiverId, order.getCaregiversId())
                        .eq(CaregiverSchedule::getScheduleDate, Date.from(appointmentDate.atZone(ZoneId.systemDefault()).toInstant()))
                        .eq(CaregiverSchedule::getScheduleStartTime, appointmentTime);
                caregiverScheduleMapper.delete(additionalDeleteWrapper);
            }
        }
        if (status == 10){
            doRefund(order, refundAmount, userId, 9, "订单已取消并退款成功", 2);
        }

        return updateSuccess;
    }

    @Override
    public boolean UpConfirmUserCancellation(OrderDTO orderDTO) {
        LambdaUpdateWrapper<Order> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(Order::getOrderId, orderDTO.getOrderId()) // 确保 orderId 正确
                .set(Order::getCaregiverRefundConfirmation, 1); // 设置更新字段

        int rowsAffected = orderMapper.update(null, updateWrapper); // 更新时不传实体，使用 updateWrapper
        return rowsAffected > 0; // 返回是否更新成功
    }

    /**
     * 阿姨取消订单
     */
    @Override
    public ResultDTO<?> cancelOrderByCaregiver(OrderDTO dto) {
        try {
            String loginId = StpUtil.getLoginIdAsString();
            LoginUserDTO loginInfo = (LoginUserDTO) StpUtil.getSession().get(String.valueOf(loginId));
            Long userId = loginInfo.getUserId();
            Integer userType = loginInfo.getUserType();
            // 查询用户钱包
            Wallet userWallet = walletMapper.selectWalletByUserForUpdate(userId, userType);
            if (userWallet == null) {
                return ResultDTO.error("用户钱包不存在");
            }

            // 第一步：查询订单是否存在
            Order order = orderMapper.selectById(dto.getOrderId());
            if (order == null) {
                return ResultDTO.error("订单不存在");
            }

            // 第二步：校验阿姨ID是否匹配该订单
            if (!order.getCaregiversId().equals(userId)) {
                return ResultDTO.error("该订单不属于当前阿姨");
            }

            // 第三步：校验订单状态是否允许取消（例如状态为已预约/进行中）
            if (order.getStatus() != 1 && order.getStatus() != 2) {
                return ResultDTO.error("该订单状态不可取消");
            }

            // 第四步：校验取消原因是否存在
            if (dto.getReasonId() == null) {
                return ResultDTO.error("请选择取消原因");
            }

            CancelReason reason = cancelReasonMapper.selectById(dto.getReasonId());
            String reasonText;
            if (reason == null) {
                return ResultDTO.error("无效的取消原因");
            }

            // 如果选择了“其他”，则使用用户输入的自定义理由
            if ("其他".equals(reason.getReasonText())) {
                if (dto.getCustomReason() == null || dto.getCustomReason().trim().isEmpty()) {
                    return ResultDTO.error("请选择或填写取消原因");
                }
                reasonText = dto.getCustomReason();  // 使用用户输入的自定义拒绝理由
            } else {
                reasonText = reason.getReasonText();  // 返回数据库中存储的拒绝原因
            }

            // 第五步：更新订单状态为已取消（设为 4）
            LambdaUpdateWrapper<Order> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(Order::getOrderId, dto.getOrderId())
                    .set(Order::getStatus, 0)
                            .set(Order::getCaregiversId,null);
            // 执行更新操作
            orderMapper.update(null, updateWrapper);

            // 第六步：提醒用户（写入通知表）
            UserNotification userNotification = new UserNotification();
            userNotification.setOrderId(dto.getOrderId());
            userNotification.setCaregiverId(userId);
            userNotification.setMessage("阿姨取消了订单，原因：" + reasonText);
            userNotificationMapper.insert(userNotification);

            // 第七步：构造返回数据
            Map<String, Object> result = new HashMap<>();
            result.put("orderId", dto.getOrderId());
            result.put("actualPaymentPrice", order.getActualPaymentPrice());
            result.put("reasonText", reasonText);



            return ResultDTO.success("订单取消成功", result);

        } catch (Exception e) {
            log.error("取消订单失败", e);
            throw new BizException("取消失败");
        }
    }

    @Override
    public Order getOrderByOrderNo(String orderNo) {
        LambdaQueryWrapper<Order> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Order::getOrderNumber, orderNo);
        return orderMapper.selectOne(queryWrapper);
    }


    @Override
    public ResultDTO<Map<String, Object>> getRefundInfo(String orderId) {
        if (StringUtils.isBlank(orderId)) {
            return ResultDTO.error("订单ID不能为空");
        }

        // 查询订单
        Order order = orderMapper.selectById(orderId);
        if (order == null) {
            return ResultDTO.error("订单不存在");
        }

        Map<String, Object> result = new HashMap<>();
//        result.put("refundStatusDesc", getRefundStatusDesc(order.getStatus()));
        result.put("actualPaymentPrice", order.getActualPaymentPrice());
//        result.put("payMethods", order.getPayMethods());
        result.put("payTime", order.getPayTime());
        result.put("orderNumber", order.getOrderNumber());

        // 查询退款相关日志
        List<Integer> refundOps = Arrays.asList(5, 6, 7); // 退款相关操作码
        List<OrderOperationLog> logs = orderOperationLogMapper.selectList(
                new LambdaQueryWrapper<OrderOperationLog>()
                        .eq(OrderOperationLog::getOrderId, orderId)
                        .in(OrderOperationLog::getOperationType, refundOps)
                        .orderByAsc(OrderOperationLog::getCreateTime)
        );

        List<Map<String, Object>> operationLogs = logs.stream().map(log -> {
            Map<String, Object> item = new HashMap<>();
//            item.put("operationType", log.getOperationType());
            item.put("operationDesc", log.getOperationDesc());
//            item.put("operatorType", log.getOperatorType());
//            item.put("operatorId", log.getOperatorId());
            item.put("createTime", log.getCreateTime());
            return item;
        }).collect(Collectors.toList());

        result.put("operationLogs", operationLogs);

        return ResultDTO.success("查询成功", result);
    }


    @Override
    public List<OrderDTO> getAppointmentOrdersByCaregiver() {
        String loginIdAsString = StpUtil.getLoginIdAsString();
        LoginUserDTO loginInfo = (LoginUserDTO) StpUtil.getSession().get(loginIdAsString);
        Long caregiverId = loginInfo.getUserId();
        List<OrderDTO> orders = orderMapper.listAppointmentOrdersByCaregiver(caregiverId);
        List<Long> orderIds = orders.stream()
                .map(OrderDTO::getOrderId)
                .collect(Collectors.toList());

        if (!orderIds.isEmpty()) {
            // 查询属性表
            List<OrderAttribute> attributeList = orderAttributeMapper.selectList(
                    new LambdaQueryWrapper<OrderAttribute>()
                            .in(OrderAttribute::getOrderId, orderIds)
            );
            List<OrderAttributeDTO> orderAttributeList = OrderAttributeDTOMapper.INSTANCE.toOrderAttributeList(attributeList);
            // 按 orderId 分组
            Map<Long, List<OrderAttributeDTO>> attrMap = orderAttributeList.stream()
                    .collect(Collectors.groupingBy(OrderAttributeDTO::getOrderId));

            // 回填属性
            for (OrderDTO dto : orders) {
                dto.setAttributes(attrMap.getOrDefault(dto.getOrderId(), Collections.emptyList()));
            }
        }
        return orders;
    }

    @Override
    public List<OrderDTO> getUpdateOrderByCaregiver() {
        // 获取当前登录用户（阿姨）的 ID
        String loginIdAsString = StpUtil.getLoginIdAsString();
        LoginUserDTO loginInfo = (LoginUserDTO) StpUtil.getSession().get(loginIdAsString);
        Long caregiverId = loginInfo.getUserId();

        // 查询当前登录阿姨 status = 10 的所有预约订单
        List<OrderDTO> orders = orderMapper.listAppointmentOrdersByCaregiverWithStatus(caregiverId, 10);

        // 获取所有订单的 ID 列表
        List<Long> orderIds = orders.stream()
                .map(OrderDTO::getOrderId)
                .collect(Collectors.toList());

        if (!orderIds.isEmpty()) {
            // 查询订单属性表，获取与订单相关的属性
            List<OrderAttribute> attributeList = orderAttributeMapper.selectList(
                    new LambdaQueryWrapper<OrderAttribute>()
                            .in(OrderAttribute::getOrderId, orderIds)
            );

            // 将查询到的属性数据转换成 DTO 对象
            List<OrderAttributeDTO> orderAttributeList = OrderAttributeDTOMapper.INSTANCE.toOrderAttributeList(attributeList);
            // 按 orderId 分组
            Map<Long, List<OrderAttributeDTO>> attrMap = orderAttributeList.stream()
                    .collect(Collectors.groupingBy(OrderAttributeDTO::getOrderId));

            // 回填属性
            for (OrderDTO dto : orders) {
                dto.setAttributes(attrMap.getOrDefault(dto.getOrderId(), Collections.emptyList()));
            }
        }
        return orders;
    }

    @Override
    public List<OrderDTO> getCancelOrderByCaregiver() {
        // 获取当前登录用户（阿姨）的 ID
        String loginIdAsString = StpUtil.getLoginIdAsString();
        LoginUserDTO loginInfo = (LoginUserDTO) StpUtil.getSession().get(loginIdAsString);
        Long caregiverId = loginInfo.getUserId();

        // 查询当前登录阿姨 status = 6 的所有预约订单
        List<OrderDTO> orders = orderMapper.listCancelledOrdersByCaregiver(caregiverId, 6);

        // 获取所有订单的 ID 列表
        List<Long> orderIds = orders.stream()
                .map(OrderDTO::getOrderId)
                .collect(Collectors.toList());

        if (!orderIds.isEmpty()) {
            // 查询订单属性表，获取与订单相关的属性
            List<OrderAttribute> attributeList = orderAttributeMapper.selectList(
                    new LambdaQueryWrapper<OrderAttribute>()
                            .in(OrderAttribute::getOrderId, orderIds)
            );

            // 将查询到的属性数据转换成 DTO 对象
            List<OrderAttributeDTO> orderAttributeList = OrderAttributeDTOMapper.INSTANCE.toOrderAttributeList(attributeList);

            // 按 orderId 分组
            Map<Long, List<OrderAttributeDTO>> attrMap = orderAttributeList.stream()
                    .collect(Collectors.groupingBy(OrderAttributeDTO::getOrderId));

            // 回填属性
            for (OrderDTO dto : orders) {
                dto.setAttributes(attrMap.getOrDefault(dto.getOrderId(), Collections.emptyList()));
            }
        }

        // 返回带有属性信息的订单列表
        return orders;
    }

    @Override
    public List<OrderDTO> getRejectAppointment() {
        String loginIdAsString = StpUtil.getLoginIdAsString();
        LoginUserDTO loginInfo = (LoginUserDTO) StpUtil.getSession().get(loginIdAsString);
        Long userId = loginInfo.getUserId();

        List<OrderDTO> orders = orderMapper.listRejectAppointmentByUser(userId, 9);
        List<Long> orderIds = orders.stream()
                .map(OrderDTO::getOrderId)
                .collect(Collectors.toList());
        if (!orderIds.isEmpty()) {
            List<OrderAttribute> attributeList = orderAttributeMapper.selectList(
                    new LambdaQueryWrapper<OrderAttribute>()
                            .in(OrderAttribute::getOrderId, orderIds)
            );
            List<OrderAttributeDTO> orderAttributeList = OrderAttributeDTOMapper.INSTANCE.toOrderAttributeList(attributeList);
            Map<Long, List<OrderAttributeDTO>> attrMap = orderAttributeList.stream()
                    .collect(Collectors.groupingBy(OrderAttributeDTO::getOrderId));
            for (OrderDTO dto : orders) {
                dto.setAttributes(attrMap.getOrDefault(dto.getOrderId(), Collections.emptyList()));
            }
        }
        return orders;
    }

    // 根据订单id查询订单详情
    @Override
    public ResultDTO<OrderDTO> getByOrderIds(Long orderId) {
        OrderDTO order = orderMapper.selectOrderDetailById(orderId);
        LambdaQueryWrapper<OrderAttribute> orderAttributeLambdaQueryWrapper = new LambdaQueryWrapper<>();
        orderAttributeLambdaQueryWrapper.eq(OrderAttribute::getOrderId, orderId);
        List<OrderAttribute> orderAttributes = orderAttributeMapper.selectList(orderAttributeLambdaQueryWrapper);
        List<OrderAttributeDTO> orderAttributeList = OrderAttributeDTOMapper.INSTANCE.toOrderAttributeList(orderAttributes);
        order.setAttributes(orderAttributeList);
        return ResultDTO.success("查询成功", order);
    }

    // 阿姨完成服务
    @Override
    public ResultDTO<String> updateOrderById(Long orderId) {

        Order order = orderMapper.selectById(orderId);
        if (order.getStatus() != 2) {
            return ResultDTO.error("当前状态无法完成服务");
        }
        order.setStatus(8);
        order.setServiceEndTime(new Date());
        int i = orderMapper.updateById(order);
        if (i == 0) {
            return ResultDTO.error("完成失败，请重试");
        }
        return ResultDTO.notDataSuccess("完成成功");
    }

    /**
     * 判断是否为预约阿姨
     *
     * @param order
     * @param estimatedTime
     */
    public void insertCaregiverSchedule(Order order, int estimatedTime) {
        Long caregiversId = order.getCaregiversId();
        if (caregiversId == null || order.getAppointmentTime() == null) {
            return;
        }

        // 获取开始时间和结束时间
        Date appointmentTime = order.getAppointmentTime();

        LocalDateTime appointmentDateTime = LocalDateTime.ofInstant(
                appointmentTime.toInstant(), ZoneId.systemDefault());
        // 完成时间
        LocalDateTime endDateTime = appointmentDateTime.plusMinutes(estimatedTime);

        // 遍历时间段，按天拆分插入
        LocalDateTime segmentStart = appointmentDateTime;

        while (!segmentStart.toLocalDate().isAfter(endDateTime.toLocalDate())) {
            LocalDateTime segmentEnd;

            if (segmentStart.toLocalDate().equals(endDateTime.toLocalDate())) {
                // 最后一天：结束时间为实际结束时间
                segmentEnd = endDateTime;
            } else {
                // 中间天：结束时间为 23:59:59
                segmentEnd = segmentStart.toLocalDate().atTime(LocalTime.MAX);
            }

            CaregiverSchedule schedule = new CaregiverSchedule();
            schedule.setScheduleType(1);
            schedule.setCaregiverId(caregiversId);

            // scheduleDate 只取年月日
            schedule.setScheduleDate(Date.from(segmentStart.toLocalDate()
                    .atStartOfDay(ZoneId.systemDefault()).toInstant()));

            // 开始时间、结束时间（只存时分秒）
            schedule.setScheduleStartTime(segmentStart.toLocalTime());
            schedule.setScheduleEndTime(segmentEnd.toLocalTime());
            schedule.setOrderId(order.getOrderId());
            // 插入数据库
            caregiverScheduleMapper.insert(schedule);

            // 下一段开始时间：第二天的 00:00:00
            segmentStart = segmentEnd.toLocalDate().plusDays(1).atStartOfDay();
        }
    }

}

