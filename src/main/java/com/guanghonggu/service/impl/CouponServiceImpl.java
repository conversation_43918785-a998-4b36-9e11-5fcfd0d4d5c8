package com.guanghonggu.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.guanghonggu.entity.GiftCouponProbability;
import com.guanghonggu.entity.UserVip;
import com.guanghonggu.constant.HttpStatusConstants;
import com.guanghonggu.dto.CouponDTO;
import com.guanghonggu.dto.LoginUserDTO;
import com.guanghonggu.dto.ResultDTO;
import com.guanghonggu.entity.Coupon;
import com.guanghonggu.entity.UserCoupon;
import com.guanghonggu.mapper.GiftCouponProbabilityMapper;
import com.guanghonggu.mapper.UserCouponMapper;
import com.guanghonggu.mapper.UserVipMapper;
import com.guanghonggu.mapper.dto.CouponDTOMapper;
import com.guanghonggu.service.CouponService;
import com.guanghonggu.mapper.CouponMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 * @description 针对表【coupon(优惠卷表)】的数据库操作Service实现
 * @createDate 2025-04-27 10:56:56
 */
@Service
public class CouponServiceImpl extends ServiceImpl<CouponMapper, Coupon>
        implements CouponService {

    @Autowired
    private CouponMapper couponMapper;

    @Autowired
    private UserCouponMapper userCouponMapper;

    @Autowired
    private PlatformTransactionManager txManger;

    @Autowired
    private UserVipMapper userVipMapper;

    @Autowired
    private GiftCouponProbabilityMapper giftCouponProbabilityMapper;

    public static final int WELCOME_COUPON_TYPE = 2;


    @Override
    public ResultDTO<IPage<CouponDTO>> listUserCoupon(CouponDTO couponDTO) {
        String loginIdAsString = StpUtil.getLoginIdAsString();
        LoginUserDTO loginUser = (LoginUserDTO) StpUtil.getSession().get(loginIdAsString);
        Long userId = loginUser.getUserId();

        int currentPage = couponDTO.getCurrentPage() != null ? couponDTO.getCurrentPage() : 1;
        int pageSize = couponDTO.getPageSize() != null ? couponDTO.getPageSize() : 10;
        Page<CouponDTO> page = new Page<>(currentPage, pageSize);
        IPage<CouponDTO> couponList = couponMapper.listUserCoupon(page, userId);

        return ResultDTO.success("获取用户优惠券列表成功", couponList);
    }

    @Override
    public void publishCoupon() {

    }

    @Override
    public ResultDTO<String> receiveCoupon(String couponId) {
        String loginIdAsString = StpUtil.getLoginIdAsString();
        LoginUserDTO loginInfo = (LoginUserDTO) StpUtil.getSession().get(loginIdAsString);
        Long userId = loginInfo.getUserId();
        // 先加锁再开启事务
        synchronized (userId.toString().intern()) {
            // 开启事务
            TransactionDefinition definition = TransactionDefinition.withDefaults();
            TransactionStatus transaction = txManger.getTransaction(definition);
            try {
                // 1. 查询
                Coupon coupon = couponMapper.getCoupon(couponId, userId);
                ResultDTO<String> resultDTO = this.checkCoupon(coupon, userId);
                if (resultDTO.getCode() == HttpStatusConstants.ERROR) {
                    return resultDTO;
                }

                // 2. 更新优惠券剩余数量（乐观锁）
                int res = couponMapper.updateCouponRemainingQuantity(couponId);
                if (res <= 0) {
                    return ResultDTO.error("优惠券已领完");
                }

                // 获取用户优惠券过期时间
                LocalDateTime now = LocalDateTime.now();
                LocalDateTime twoDaysLater = now.plusDays(coupon.getValidDay());
                // 过期天数推算的到期时间
                Date date = Date.from(twoDaysLater.atZone(ZoneId.systemDefault()).toInstant());

                // 该优惠券到期时间
                Date validEndTime = coupon.getValidEndTime();

                // 获取最小到期时间
                Date minDate = date.before(validEndTime) ? date : validEndTime;
                // 3. 插入用户优惠券记录
                UserCoupon userCoupon = new UserCoupon();
                userCoupon.setUserId(userId);
                userCoupon.setCouponId(Long.valueOf(couponId));

                userCoupon.setExpirationTime(minDate);
                userCoupon.setAcquireTime(new Date());
                userCoupon.setStatus(1);  // 1: 未使用
                userCouponMapper.insert(userCoupon);
                txManger.commit(transaction);
                return ResultDTO.notDataSuccess("领取优惠券成功");
            } catch (Exception e) {
                log.error("领取优惠券失败", e);
                txManger.rollback(transaction);
                return ResultDTO.error("领取优惠券失败");
            }
        }
    }

    @Override
    public ResultDTO<CouponDTO> getCoupon(String couponId) {
//        LambdaQueryWrapper<Coupon> couponLambdaQueryWrapper = new LambdaQueryWrapper<>();
//        couponLambdaQueryWrapper
//                .select(Coupon::getCouponId, Coupon::getTitle, Coupon::getDescription, Coupon::getCouponType,
//                        Coupon::getFullAmount, Coupon::getDiscountValue, Coupon::getMaxDiscount,
//                        Coupon::getValidDay, Coupon::getLimitCount, Coupon::getValidStartTime,
//                        Coupon::getValidEndTime, Coupon::getCollectStartTime, Coupon::getCollectEndTime)
//                .eq(Coupon::getCouponId, couponId);
        Coupon coupon = couponMapper.selectById(couponId);
        if (coupon == null) {
            return ResultDTO.error("优惠券不存在");
        }
        CouponDTO couponDTO = CouponDTOMapper.INSTANCE.toCouponDTO(coupon);

        return ResultDTO.success("获取优惠券信息成功", couponDTO);
    }

    /**
     * 检查优惠券是否可发放
     *
     * @param coupon
     * @param userId
     * @return
     */
    public ResultDTO<String> checkCoupon(Coupon coupon, Long userId) {
        if (coupon == null || coupon.getRemainingQuantity() <= 0) {
            return ResultDTO.error("优惠券已领完");  // 优惠券无效或已领完
        }

        Date collectStartTime = coupon.getCollectStartTime();
        Date collectEndTime = coupon.getCollectEndTime();
        Date currentDate = new Date();
        if (collectStartTime != null && collectStartTime.after(currentDate)) {
            return ResultDTO.error("领取时间未开始");  // 优惠券未开始
        }
        if (collectEndTime != null && collectEndTime.before(currentDate)) {
            return ResultDTO.error("领取时间已过期");  // 优惠券已过期
        }

        LambdaQueryWrapper<UserCoupon> lambdaQueryWrapper = new LambdaQueryWrapper<UserCoupon>()
                .eq(UserCoupon::getUserId, userId)
                .eq(UserCoupon::getCouponId, coupon.getCouponId())
                .in(UserCoupon::getStatus, 1);
        UserCoupon exist = userCouponMapper.selectOne(lambdaQueryWrapper); // 只查有效状态

        if (exist != null) {
            return ResultDTO.error("已领取过此优惠券");
        }

        // 限领次数
        Integer limitCount = coupon.getLimitCount();
        LambdaQueryWrapper<UserCoupon> userCouponLambdaQueryWrapper = new LambdaQueryWrapper<>();
        userCouponLambdaQueryWrapper.eq(UserCoupon::getUserId, userId)
                .eq(UserCoupon::getCouponId, coupon.getCouponId());
        Long count = userCouponMapper.selectCount(userCouponLambdaQueryWrapper);
        if (count >= limitCount) {
            return ResultDTO.error("已超过领取次数");
        }
        return ResultDTO.notDataSuccess("领取成功");
    }

    /**
     * 发放充值会员优惠券
     *
     * @param userId 用户id
     */
    public void giveDiscountCoupons(Long userId, BigDecimal rechargeAmount) {

        LambdaQueryWrapper<UserVip> userVipLambdaQueryWrapper = new LambdaQueryWrapper<>();
        userVipLambdaQueryWrapper.eq(UserVip::getRechargeBalance, rechargeAmount);
        UserVip userVip = userVipMapper.selectOne(userVipLambdaQueryWrapper);
        Long couponId = userVip.getCouponId();
        Integer issuedCount = userVip.getIssuedCount();
        ArrayList<UserCoupon> userCoupons = new ArrayList<>();
        Coupon coupon = couponMapper.selectById(couponId);
        if (coupon == null) {
            throw new RuntimeException("优惠券不存在");
        }
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime twoDaysLater = now.plusDays(coupon.getValidDay());
        Date date = Date.from(twoDaysLater.atZone(ZoneId.systemDefault()).toInstant());

        // 该优惠券到期时间
        Date validEndTime = coupon.getValidEndTime();

        // 获取最小到期时间
        Date minDate = date.before(validEndTime) ? date : validEndTime;

        for (int i = 0; i < issuedCount; i++) {
            UserCoupon userCoupon = new UserCoupon();
            userCoupon.setUserId(userId);
            userCoupon.setCouponId(couponId);
            Date nowDate = new Date();
            userCoupon.setExpirationTime(minDate);
            userCoupon.setAcquireTime(nowDate);
            userCoupon.setStatus(1);  // 1: 未使用
            userCoupons.add(userCoupon);
        }
        userCouponMapper.insertBatch(userCoupons);
    }


    /**
     * 注册成功下发优惠卷
     */
    @Override
    public ResultDTO<String> issueNewUserCoupon(Long userId) {

        LambdaQueryWrapper<Coupon> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Coupon::getDistributionMethod, WELCOME_COUPON_TYPE)
                .eq(Coupon::getIsDelete, 0);

        Coupon coupon = couponMapper.selectOne(wrapper);
        if (coupon == null) {
            return ResultDTO.error("优惠券不存在");
        }
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime twoDaysLater = now.plusDays(coupon.getValidDay());
        Date date = Date.from(twoDaysLater.atZone(ZoneId.systemDefault()).toInstant());

        // 该优惠券到期时间
        Date validEndTime = coupon.getValidEndTime();

        // 获取最小到期时间
        Date minDate = date.before(validEndTime) ? date : validEndTime;
        UserCoupon userCoupon = new UserCoupon();
        userCoupon.setUserId(userId);
        userCoupon.setCouponId(coupon.getCouponId());
        userCoupon.setAcquireTime(new Date());
        userCoupon.setStatus(1);
        userCoupon.setExpirationTime(minDate);
        userCouponMapper.insert(userCoupon);
        return ResultDTO.notDataSuccess("领取成功");
    }

    /**
     * 随机发放一张优惠券给指定用户
     * 60% 概率发放 15 元优惠券
     * 20% 概率发放 10 元优惠券
     * 20% 概率发放 5 元优惠券
     *
     * @param userId 用户ID
     * @return 如果发放成功，返回包含优惠券信息的 Map；否则返回 null
     */
    public Map<String, Object> grantRandomCoupon(Long userId) {
        // 1. 查询 gift_coupon_probability 表，获取所有有效的概率配置
        List<GiftCouponProbability> probabilityList = giftCouponProbabilityMapper.selectList(
                new LambdaQueryWrapper<GiftCouponProbability>().orderByAsc(GiftCouponProbability::getId)
        );

        if (CollectionUtils.isEmpty(probabilityList)) {
            log.warn("未配置优惠券发放概率");
            return null; // 无配置
        }

        // 2. 随机抽取一个优惠券（加权随机）
        double random = Math.random(); // 0~100之间
        double cumulative = 0.0;
        Long selectedCouponId = null;

        for (GiftCouponProbability config : probabilityList) {
            cumulative += config.getReleaseProbability().doubleValue();
            if (random <= cumulative) {
                selectedCouponId = config.getCouponId();
                break;
            }
        }

        if (selectedCouponId == null) {
            return null; // 没选中
        }

        // 3. 查询 coupon 表，确认是否是有效券
        Coupon coupon = couponMapper.selectById(selectedCouponId);
        if (coupon == null || coupon.getIsDelete() != null && coupon.getIsDelete() == 1) {
            log.warn("优惠券不存在或无效");
            return null; // 优惠券无效
        }

        // 3. 创建用户优惠券实体并设置属性
        UserCoupon userCoupon = new UserCoupon();
        userCoupon.setUserId(userId);
        userCoupon.setCouponId(coupon.getCouponId());
        userCoupon.setStatus(1); // 未使用
        userCoupon.setAcquireTime(new Date());

        LocalDateTime now = LocalDateTime.now();
        LocalDateTime twoDaysLater = now.plusDays(coupon.getValidDay());
        Date date = Date.from(twoDaysLater.atZone(ZoneId.systemDefault()).toInstant());

        // 该优惠券到期时间
        Date validEndTime = coupon.getValidEndTime();

        // 获取最小到期时间
        Date minDate = date.before(validEndTime) ? date : validEndTime;
        // 设置过期时间
        userCoupon.setExpirationTime(minDate);

        // 插入用户优惠券记录
        userCouponMapper.insert(userCoupon);

        // 4. 构建返回信息
        Map<String, Object> grantedCoupon = new HashMap<>();
        grantedCoupon.put("couponId", coupon.getCouponId());
        grantedCoupon.put("discountAmount", coupon.getDiscountValue());
        grantedCoupon.put("expirationTime", userCoupon.getExpirationTime());

        return grantedCoupon;
    }
}




