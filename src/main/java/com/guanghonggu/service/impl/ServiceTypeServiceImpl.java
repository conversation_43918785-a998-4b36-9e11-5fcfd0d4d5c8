package com.guanghonggu.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.guanghonggu.dto.ServiceTypeDTO;
import com.guanghonggu.entity.ServiceType;
import com.guanghonggu.mapper.dto.ServiceTypeDTOMapper;
import com.guanghonggu.service.ServiceTypeService;
import com.guanghonggu.mapper.ServiceTypeMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【service_type(服务类型表)】的数据库操作Service实现
 * @createDate 2025-04-29 16:18:24
 */
@Service
public class ServiceTypeServiceImpl extends ServiceImpl<ServiceTypeMapper, ServiceType>
        implements ServiceTypeService {

    @Autowired
    private ServiceTypeMapper serviceTypeMapper;

    @Override
    public List<ServiceTypeDTO> getServiceTypeList() {

        // 查询所有服务类型并按 sort 排序
        List<ServiceType> serviceTypes = serviceTypeMapper.selectList(new LambdaQueryWrapper<ServiceType>().orderByAsc(ServiceType::getSort));
        List<ServiceTypeDTO> sysPictureDTOList = ServiceTypeDTOMapper.INSTANCE.toSysPictureDTOList(serviceTypes);
        return sysPictureDTOList;
    }
}




