package com.guanghonggu.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.guanghonggu.dto.ResultDTO;
import com.guanghonggu.dto.UserDTO;
import com.guanghonggu.dto.LoginDTO;
import com.guanghonggu.dto.LoginUserDTO;
import com.guanghonggu.dto.MobileDTO;
import com.guanghonggu.dto.WxAccessTokenResponse;
import com.guanghonggu.dto.WxUserInfo;
import com.guanghonggu.entity.SysPicture;
import com.guanghonggu.entity.User;
import com.guanghonggu.entity.Wallet;
import com.guanghonggu.handler.exception.BizException;
import com.guanghonggu.mapper.SysPictureMapper;
import com.guanghonggu.mapper.WalletMapper;
import com.guanghonggu.service.AliyunDypnsService;
import com.guanghonggu.service.CouponService;
import com.guanghonggu.service.UserService;
import com.guanghonggu.mapper.UserMapper;
import com.guanghonggu.strategy.user.UserByTypeStrategyContext;
import com.guanghonggu.strategy.user.UserByTypeStrategyInterface;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import static com.guanghonggu.util.MD5Utils.verify;

/**
 * <AUTHOR>
 * @description 针对表【user(用户表)】的数据库操作Service实现
 * @createDate 2025-04-27 10:56:56
 */
@Slf4j
@Service
public class UserServiceImpl extends ServiceImpl<UserMapper, User>
        implements UserService {

    @Value("${wx.app.appId}")
    private String appid;

    @Value("${wx.app.appSecret}")
    private String appSecret;


    @Autowired
    private UserMapper userMapper;


    @Autowired
    private SysPictureMapper sysPictureMapper;

    @Autowired
    private AliyunDypnsService aliyunDypnsService;
    @Autowired
    private CouponService couponService;
    @Autowired
    private WalletMapper walletMapper;

    @Autowired
    private UserByTypeStrategyContext userByTypeStrategyContext;

    @Override
    public User selectByPhone(String phoneNumber) {
        LambdaQueryWrapper<User> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(User::getPhoneNumber, phoneNumber);
        User user = userMapper.selectOne(wrapper);
        return user;

    }


    @Override
    public void insert(User user) {
        userMapper.insert(user);
    }


    @Override
    public User selectByOpenId(String openid) {
        LambdaQueryWrapper<User> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(User::getWechatOpenid, openid);
        return userMapper.selectOne(wrapper);
    }

    /**
     * 修改密码（内部使用）
     */
    @Override
    public int putPassword(UserDTO userDTO){
       User user = new User();
       user.setPayPassword(userDTO.getPayPassword());
       UpdateWrapper<User> wrapper = new UpdateWrapper<>();
       wrapper.eq("user_id", userDTO.getUserId());
       return userMapper.update(user, wrapper);
    }

    /**
     * 删除支付密码
     */
    @Override
    public int deleteUserPayPassword(String phoneNumber){
        UpdateWrapper<User> wrapper = new UpdateWrapper<>();
        wrapper.eq("phone_number", phoneNumber);
        wrapper.set("pay_password", null);
        return userMapper.update(null, wrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultDTO<LoginDTO> getMobile(MobileDTO mobileDTO) {

        String token = mobileDTO.getToken();

        String phoneNumber = null;
        try {
            phoneNumber = aliyunDypnsService.getMobileByToken(token);
        } catch (Exception e) {
            log.error("获取手机号失败", e);
            throw new BizException("获取手机号失败");
        }

        User user = selectByPhone(phoneNumber);
        int isNewUser = 0;
        // 如果用户不存在，注册
        if (user == null) {
            isNewUser = 1;
            user = new User();
            user.setPhoneNumber(phoneNumber);
            user.setRegistrationTime(new Date());
            user.setNickname("用户" + phoneNumber.substring(phoneNumber.length() - 4));
            LambdaQueryWrapper<SysPicture> sysPictureLambdaQueryWrapper = new LambdaQueryWrapper<>();
            sysPictureLambdaQueryWrapper.eq(SysPicture::getPictureType, 1);
            SysPicture sysPicture = sysPictureMapper.selectOne(sysPictureLambdaQueryWrapper);

            user.setAvatar(sysPicture.getDownloadAddress());
            user.setRegistrationId(mobileDTO.getRegistrationId());
            userMapper.insert(user);

            Wallet wallet = new Wallet();
            wallet.setUserId(user.getUserId());
            wallet.setUserType(1);
            wallet.setBalance(new BigDecimal(0));
            wallet.setRechargeBalance(new BigDecimal(0));
            wallet.setTotalIncome(new BigDecimal(0));
            wallet.setTotalSpent(new BigDecimal(0));
            walletMapper.insert(wallet);

            // 发放优惠券
            ResultDTO<String> resultDTO = couponService.issueNewUserCoupon(user.getUserId());
        } else {
            User updateUser = new User();
            updateUser.setUserId(user.getUserId());
            updateUser.setRegistrationId(mobileDTO.getRegistrationId());
            userMapper.updateById(updateUser);
        }

        // 登录并设置 session
        StpUtil.login("user:" + user.getUserId());

        LoginUserDTO loginUserDTO = new LoginUserDTO();
        loginUserDTO.setUserId(user.getUserId());
        loginUserDTO.setUserType(1);
        loginUserDTO.setPhone(phoneNumber);
        StpUtil.getSession().set("user:" + user.getUserId(), loginUserDTO);

        // 返回 token 和手机号
        LoginDTO loginDTO = new LoginDTO();
        loginDTO.setIsNewUser(isNewUser);
        loginDTO.setUserId(user.getUserId());
        loginDTO.setPhoneNumber(phoneNumber);
        loginDTO.setToken(StpUtil.getTokenValue());
        loginDTO.setAvatar(user.getAvatar());
        return ResultDTO.success("登录成功", loginDTO);

    }

    @Override
    public ResultDTO<String> wxLoginCallback(String code) {
        // 获取access_token
        String tokenUrl = "https://api.weixin.qq.com/sns" +
                "/oauth2/access_token?appid=" + appid + "&secret=" + appSecret + "&code=" + code + "&grant_type=authorization_code";

        // 发送http请求
        RestTemplate restTemplate = new RestTemplate();
        WxAccessTokenResponse tokenResponse = restTemplate
                .getForObject(tokenUrl, WxAccessTokenResponse.class);
        if (tokenResponse == null) {
            return ResultDTO.error("获取用户信息失败");
        }

        // 获取用户信息
        String userInfoUrl = "https://api.weixin.qq.com/sns" +
                "/userinfo?access_token=" + tokenResponse.getAccess_token() + "&openid=" + tokenResponse.getOpenid() + "&lang=zh_CN";

        WxUserInfo userInfo = restTemplate.getForObject(userInfoUrl, WxUserInfo.class);

        if (userInfo == null) {
            return ResultDTO.error("获取用户信息失败");
        }
        User user = selectByOpenId(userInfo.getOpenid());

        if (user == null) {
//            user = new User();
//            user.setWechatOpenid(userInfo.getOpenid());
//            user.setRegistrationTime(new Date());
//            userMapper.insert(user);
            return ResultDTO.error("该微信未绑定账号");
        }

        // 登录并设置 session
        StpUtil.login("user:" + user.getUserId());

        String phoneNumber = user.getPhoneNumber();
        LoginUserDTO loginUserDTO = new LoginUserDTO();
        loginUserDTO.setUserId(user.getUserId());
        loginUserDTO.setUserType(1);
        loginUserDTO.setPhone(phoneNumber);
        StpUtil.getSession().set("user:" + user.getUserId(), loginUserDTO);

        // 返回 token 和手机号
        LoginDTO loginDTO = new LoginDTO();
        loginDTO.setUserId(user.getUserId());
        loginDTO.setPhoneNumber(phoneNumber);
        loginDTO.setToken(StpUtil.getTokenValue());
        loginDTO.setAvatar(user.getAvatar());
        return ResultDTO.success("登录成功", StpUtil.getTokenValue());
    }

    @Override
    public ResultDTO<String> updateRegistrationId(UserDTO userDTO) {
        String registrationId = userDTO.getRegistrationId();
        if (registrationId == null) {
            return ResultDTO.error("参数错误");
        }
        String loginIdAsString = StpUtil.getLoginIdAsString();
        LoginUserDTO loginInfo = (LoginUserDTO) StpUtil.getSession().get(loginIdAsString);
        Long userId = loginInfo.getUserId();
        Integer userType = loginInfo.getUserType();
        UserByTypeStrategyInterface typeInstance = userByTypeStrategyContext.getTypeInstance(userType);
        typeInstance.updateRegistrationId(userId, registrationId);

        return ResultDTO.notDataSuccess("绑定成功");
    }
}




