package com.guanghonggu.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.guanghonggu.dto.ResultDTO;
import com.guanghonggu.entity.CleaningCategory;
import com.guanghonggu.service.CleaningCategoryService;
import com.guanghonggu.mapper.CleaningCategoryMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【cleaning_category(清洁分类表)】的数据库操作Service实现
 * @createDate 2025-07-03 09:17:50
 */
@Service
public class CleaningCategoryServiceImpl extends ServiceImpl<CleaningCategoryMapper, CleaningCategory>
        implements CleaningCategoryService {

    @Autowired
    private CleaningCategoryMapper categoryMapper;

    @Override
    public ResultDTO<List<CleaningCategory>> listAllCategories() {
        LambdaQueryWrapper<CleaningCategory> cleaningCategoryLambdaQueryWrapper = new LambdaQueryWrapper<>();
        cleaningCategoryLambdaQueryWrapper
                .select(CleaningCategory::getId, CleaningCategory::getName,
                        CleaningCategory::getLevel, CleaningCategory::getSortOrder,
                        CleaningCategory::getImageUrl, CleaningCategory::getRemark)
                .orderByAsc(CleaningCategory::getSortOrder);
        List<CleaningCategory> categories = categoryMapper.selectList(cleaningCategoryLambdaQueryWrapper);
        return ResultDTO.success("获取成功", categories);
    }
}




