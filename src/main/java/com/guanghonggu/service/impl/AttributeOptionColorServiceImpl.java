package com.guanghonggu.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.guanghonggu.entity.AttributeOptionColor;
import com.guanghonggu.service.AttributeOptionColorService;
import com.guanghonggu.mapper.AttributeOptionColorMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【attribute_option_color(属性选项颜色配置表)】的数据库操作Service实现
* @createDate 2025-06-23 16:59:42
*/
@Service
public class AttributeOptionColorServiceImpl extends ServiceImpl<AttributeOptionColorMapper, AttributeOptionColor>
    implements AttributeOptionColorService{

}




