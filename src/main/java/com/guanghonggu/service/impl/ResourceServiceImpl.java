package com.guanghonggu.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.guanghonggu.entity.Resource;
import com.guanghonggu.mapper.ResourceMapper;
import com.guanghonggu.service.ResourceService;
import com.guanghonggu.util.AliyunOssUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

@Service
public class ResourceServiceImpl implements ResourceService {
    @Autowired
    private ResourceMapper resourceMapper;
    @Autowired
    private AliyunOssUtil aliyunOssUtil;

    @Override
    public void saveResource(Long evaluationsId, Long userId, String objectName) {
        // 第1步：创建一个 Resource 实体对象（对应 resource 表中的一条记录）
        Resource res = new Resource();
        // 第2步：设置评价主键 ID（让这个资源知道它属于哪一条评价）
        res.setEvaluationsId(evaluationsId);
        // 第3步：设置上传人 ID（也就是哪个用户上传的这张图）
        res.setUserId(userId);
        // 第4步：设置阿里云 OSS 返回的文件路径（objectName）
        // 例如：
        //  - "2025/05/21/abc.jpg" （推荐）
        //  - 或者："https://xxx.oss-cn-beijing.aliyuncs.com/2025/05/21/abc.jpg"（临时访问链接 不推荐长期存）
        res.setObjectName(objectName);
        // 第5步：设置创建时间（当前时间戳）
        res.setCreateTime(LocalDateTime.now());
        // 第6步：设置更新时间（初始和创建时间一致）
        res.setUpdateTime(LocalDateTime.now());
        // 第7步：插入数据库，保存这条资源信息
        resourceMapper.insert(res);
    }

    @Override
    public void deleteByEvaluationId(Long evaluationId) {
        // 第1步：构造查询条件（QueryWrapper 用于查询 MyBatis-Plus）
        // 查询条件：resource 表中 evaluations_id = 当前评价ID 的所有记录
        QueryWrapper<Resource> wrapper = new QueryWrapper<>();
        wrapper.eq("evaluations_id", evaluationId);
        // 第2步：查出所有对应评价的图片/文件资源（可能有多条）
        List<Resource> list = resourceMapper.selectList(wrapper);
        // 第3步：循环每条记录，执行阿里云 OSS 删除
        for (Resource res : list) {
            // 获取数据库中保存的文件路径（完整链接 or OSS 路径）
            String ossPath = res.getObjectName();
            if (ossPath != null) {
                // 只保留 objectName（即 OSS 的文件内部路径）
                // 如果是完整URL（如 https://xxx.oss-cn-beijing.aliyuncs.com/xxx.jpg），就去掉前缀
                String objectName = ossPath.replace("https://diligent-sister.oss-cn-beijing.aliyuncs.com/", "");
                // 调用阿里云工具类，删除该文件
                aliyunOssUtil.delete(objectName);
            }
        }
        // 第4步：删除 resource 表中这些记录（数据库层清理）
        resourceMapper.delete(wrapper);
    }
}

