package com.guanghonggu.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.guanghonggu.dto.CaregiverScheduleDTO;
import com.guanghonggu.dto.LoginUserDTO;
import com.guanghonggu.dto.OrderDTO;
import com.guanghonggu.dto.ResultDTO;
import com.guanghonggu.entity.Address;
import com.guanghonggu.entity.Caregiver;
import com.guanghonggu.entity.CaregiverSchedule;
import com.guanghonggu.entity.Coupon;
import com.guanghonggu.entity.Location;
import com.guanghonggu.entity.Order;
import com.guanghonggu.entity.OrderAttribute;
import com.guanghonggu.entity.UserCoupon;
import com.guanghonggu.entity.Wallet;
import com.guanghonggu.entity.WalletTransaction;
import com.guanghonggu.enums.WebSocketMessageType;
import com.guanghonggu.handler.exception.BizException;
import com.guanghonggu.mapper.AddressMapper;
import com.guanghonggu.mapper.CouponMapper;
import com.guanghonggu.mapper.LocationMapper;
import com.guanghonggu.mapper.OrderAttributeMapper;
import com.guanghonggu.mapper.OrderMapper;
import com.guanghonggu.mapper.UserCouponMapper;
import com.guanghonggu.mapper.WalletMapper;
import com.guanghonggu.mapper.WalletTransactionMapper;
import com.guanghonggu.mapper.CaregiverMapper;
import com.guanghonggu.service.CaregiverScheduleService;
import com.guanghonggu.mapper.CaregiverScheduleMapper;
import com.guanghonggu.strategy.pay.strategy.PayContext;
import com.guanghonggu.strategy.pay.strategy.PayStrategy;
import com.guanghonggu.websocket.WebSocketServer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.Calendar;
import java.util.*;

import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【caregiver_schedule(阿姨日程表)】的数据库操作Service实现
 * @createDate 2025-04-27 10:56:56
 */
@Slf4j
@Service
public class CaregiverScheduleServiceImpl extends ServiceImpl<CaregiverScheduleMapper, CaregiverSchedule>
        implements CaregiverScheduleService {

    @Autowired
    private CaregiverScheduleMapper caregiverScheduleMapper;
    @Autowired
    private CaregiverMapper caregiverMapper;

    @Autowired
    private OrderServiceImpl orderService;

    @Autowired
    private PayContext payContext;

    @Autowired
    private OrderAttributeMapper orderAttributeMapper;

    @Autowired
    private OrderMapper orderMapper;

    @Autowired
    private WalletMapper walletMapper;

    @Autowired
    private WalletTransactionMapper walletTransactionMapper;

    @Autowired
    private UserCouponMapper userCouponMapper;

    @Autowired
    private CouponMapper couponMapper;

    @Autowired
    private WebSocketServer webSocketServer;

    @Autowired
    private AddressMapper addressMapper;

    @Autowired
    private LocationMapper locationMapper;

    /**
     * 查询阿姨的日程安排（未来预约）
     */
    @Override
    public List<CaregiverSchedule> getScheduleListByCaregiverId(CaregiverScheduleDTO caregiverScheduleDTO) {
        QueryWrapper<CaregiverSchedule> wrapper = new QueryWrapper<>();

        // 获取当前日期和传入的 scheduleDate
        LocalDate today = LocalDate.now();
        LocalDate scheduleDate = caregiverScheduleDTO.getScheduleDate().toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDate();

        // 查询条件：根据 caregiverId 和 scheduleDate 筛选
        wrapper.eq("caregiver_id", caregiverScheduleDTO.getCaregiverId())
                .eq("schedule_date", caregiverScheduleDTO.getScheduleDate());

        // 查询数据
        List<CaregiverSchedule> list = this.list(wrapper);

        // 如果传入的是今天的日期，添加从8点到当前时间的虚拟时间段
        if (scheduleDate.isEqual(today)) {
            // 创建一个新的时间段：当天8点到当前时间

            LocalTime eightAM = LocalTime.of(8, 0);  // 今天8点
            LocalTime now = LocalTime.now();

            CaregiverSchedule timeSlot = new CaregiverSchedule();
            timeSlot.setCaregiverId(9999L);
            timeSlot.setScheduleDate(caregiverScheduleDTO.getScheduleDate());
            timeSlot.setScheduleType(1);  //可以根据需求设置类型
            timeSlot.setScheduleStartTime(eightAM);  // 使用 LocalTime 类型
            timeSlot.setScheduleEndTime(now);  // 使用 LocalTime 类型
            // 将 LocalDateTime 转换为 Date
            Date nowDate = Date.from(LocalDateTime.now().atZone(ZoneId.systemDefault()).toInstant());
            timeSlot.setCreateTime(nowDate);  // 设置为 Date 类型
            timeSlot.setUpdateTime(nowDate);  // 设置为 Date 类型

            // 将这条虚拟数据添加到查询结果中
            list.add(timeSlot);
        }

        // 返回查询结果的 List<CaregiverSchedule>
        return list;
    }

    /**
     * 删除日程
     */
    @Override
    public Long deleteCaregiverSchedule(CaregiverScheduleDTO caregiverScheduleDTO) {
        QueryWrapper<CaregiverSchedule> wrapper = new QueryWrapper<>();
        wrapper.eq("caregiver_id", caregiverScheduleDTO.getCaregiverId());
        caregiverScheduleMapper.delete(wrapper);
        return caregiverScheduleDTO.getCaregiverId();
    }

    /**
     * 更新日程
     */
    @Override
    public int updateCaregiverSchedule(CaregiverScheduleDTO caregiverScheduleDTO) {
        CaregiverSchedule caregiverSchedule = new CaregiverSchedule();
        BeanUtils.copyProperties(caregiverScheduleDTO, caregiverSchedule);
        return caregiverScheduleMapper.updateById(caregiverSchedule);
    }

    /**
     * 根据日程日期查询
     */
    @Override
    public ResultDTO<List<CaregiverSchedule>> getScheduleDate(CaregiverScheduleDTO dto) {
        try {
            QueryWrapper<CaregiverSchedule> wrapper = new QueryWrapper<>();
            wrapper.eq("caregiver_id", dto.getCaregiverId())
                    .apply("DATE(schedule_date) = {0}", new java.sql.Date(dto.getScheduleDate().getTime()));
            List<CaregiverSchedule> list = caregiverScheduleMapper.selectList(wrapper);
            if (list == null || list.size() == 0) {
                return ResultDTO.error("无排班记录");
            }
            return ResultDTO.success("查询成功，共 " + list.size() + " 条", list);
        } catch (Exception e) {
            e.printStackTrace();
            return ResultDTO.error("根据日期查询失败" + e.getMessage());
        }
    }

    /**
     * 设置拒绝接单时间
     */
    @Override
    public ResultDTO<?> blockSchedule(CaregiverScheduleDTO dto) {
        try {
            String loginId = StpUtil.getLoginIdAsString();
            LoginUserDTO loginInfo = (LoginUserDTO) StpUtil.getSession().get(String.valueOf(loginId));
            Long userId = loginInfo.getUserId();

            if (userId == null || dto.getScheduleDate() == null || dto.getHourList() == null) {
                return ResultDTO.error("参数不能为空");
            }

            // 删除该天旧的不可接单记录
            QueryWrapper<CaregiverSchedule> deleteWrapper = new QueryWrapper<>();
            deleteWrapper.eq("caregiver_id", userId)
                    .eq("schedule_type", 0)
                    .apply("DATE(schedule_date) = {0}", new java.sql.Date(dto.getScheduleDate().getTime()));
            caregiverScheduleMapper.delete(deleteWrapper);

            // 遍历手动 hourList
            for (Integer hour : dto.getHourList()) {
                Calendar cal = Calendar.getInstance();
                cal.setTime(dto.getScheduleDate());
                cal.set(Calendar.HOUR_OF_DAY, hour);
                cal.set(Calendar.MINUTE, 0);
                cal.set(Calendar.SECOND, 0);

                // 正确写法：先转 Instant，再转 LocalTime
                LocalTime startTime = cal.toInstant()
                        .atZone(ZoneId.systemDefault())
                        .toLocalTime();

                cal.set(Calendar.HOUR_OF_DAY, hour + 1);
                LocalTime endTime = cal.toInstant()
                        .atZone(ZoneId.systemDefault())
                        .toLocalTime();

                boolean hasConflict = this.hasConflictSchedule(
                        userId,
                        dto.getScheduleDate(),
                        startTime,
                        endTime
                );

                if (hasConflict) {
                    return ResultDTO.error(String.format("%02d:00~%02d:00 已被安排，不能设为休息", hour, hour + 1));
                }

                CaregiverSchedule schedule = new CaregiverSchedule();
                schedule.setCaregiverId(userId);
                schedule.setScheduleDate(dto.getScheduleDate());
                schedule.setScheduleStartTime(startTime);
                schedule.setScheduleEndTime(endTime);
                schedule.setScheduleType(0); // 不可接单
                schedule.setCreateTime(new Date());
                schedule.setUpdateTime(new Date());

                caregiverScheduleMapper.insert(schedule);
            }

            return ResultDTO.success("不可接单时间设置成功（状态将在下班时间自动生效）", null);
        } catch (Exception e) {
            e.printStackTrace();
            return ResultDTO.error("设置失败：" + e.getMessage());
        }
    }

    /**
     * 查询阿姨不可接单时间
     */
    @Override
    public ResultDTO<List<Map<String, Object>>> getBlockedHourList(CaregiverScheduleDTO dto) {
        try {
            // 参数校验
            if (dto.getCaregiverId() == null || dto.getScheduleDate() == null) {
                return ResultDTO.error("参数不能为空");
            }

            // 查询该阿姨这一天的不可接单记录
            QueryWrapper<CaregiverSchedule> query = new QueryWrapper<>();
            query.eq("caregiver_id", dto.getCaregiverId()) // 阿姨ID
                    .eq("schedule_type", 0)                   // 类型为不可接单
                    .apply("DATE(schedule_date) = {0}", new java.sql.Date(dto.getScheduleDate().getTime())); // 日期相同
            List<CaregiverSchedule> list = caregiverScheduleMapper.selectList(query); // 查询列表

            // 从数据库中查询该阿姨的下班时间
            Caregiver caregiver = caregiverMapper.selectById(dto.getCaregiverId());  // 获取阿姨对象
            LocalTime offDutyTime = caregiver != null ? caregiver.getStopOrderTime() : null;  // 获取下班时间

            // 把每条记录的“开始时间”转换为小时（例如：09:00 → 9）
            List<Map<String, Object>> hourList = list.stream()
                    .map(s -> {
                        Map<String, Object> timeMap = new HashMap<>();
                        timeMap.put("startTime", s.getScheduleStartTime().getHour()); // 只获取小时
                        return timeMap;
                    })
                    .collect(Collectors.toList());

            // 如果存在下班时间，添加到返回结果
            if (offDutyTime != null) {
                Map<String, Object> offDutyTimeMap = new HashMap<>();
                offDutyTimeMap.put("offDutyTime", offDutyTime.toString().substring(0, 5));  // 格式化为 "HH:mm"
                hourList.add(offDutyTimeMap);  // 将下班时间添加到结果列表中
            }

            return ResultDTO.success("查询成功，阿姨设置的不可接单时间和下班时间为：", hourList); // 返回小时段和下班时间
        } catch (Exception e) {
            e.printStackTrace();
            return ResultDTO.error("查询失败：" + e.getMessage());
        }
    }


    /**
     * 设置手动接单功能
     */
    @Override
    public ResultDTO<?> addOrderSchedule(CaregiverScheduleDTO dto) {
        try {
            String loginId = StpUtil.getLoginIdAsString();
            LoginUserDTO loginInfo = (LoginUserDTO) StpUtil.getSession().get(String.valueOf(loginId));
            Long userId = loginInfo.getUserId();

            // 根据 orderId 查询订单信息
            Order order = orderMapper.selectById(dto.getOrderId());
            if (order == null) {
                return ResultDTO.error("订单不存在");
            }

            // 获取订单的预约时间 appointment_time
            Date appointmentTime = order.getAppointmentTime(); // 订单的预约时间
            // 获取订单的预估时间（单位：分钟）
            int estimatedTime = order.getEstimatedTime(); // 订单的预估时间（分钟）

            // 校验：阿姨ID、预约时间、结束时间必须都有
            if (userId == null || appointmentTime == null || estimatedTime <= 0) {
                return ResultDTO.error("参数不能为空");
            }

            // 获取预约时间的年月日部分
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(appointmentTime);

            // 将时分秒清零，只保留年月日部分
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            calendar.set(Calendar.MILLISECOND, 0);

            // 重新构建 Date 只包含年月日部分
            Date orderStartTime = calendar.getTime();  // 获取精确到年月日的开始时间

            // 将原始的预约时间转为 LocalTime（保留时分秒）
            calendar.setTime(appointmentTime);
            LocalTime localStartTime = LocalTime.of(calendar.get(Calendar.HOUR_OF_DAY), calendar.get(Calendar.MINUTE), calendar.get(Calendar.SECOND));

            // 复制 calendar 对象来计算结束时间，确保不影响开始时间
            Calendar endCalendar = (Calendar) calendar.clone();
            endCalendar.add(Calendar.MINUTE, estimatedTime); // 计算结束时间
            Date orderEndTime = endCalendar.getTime();  // 获取结束时间

            // 将结束时间转换为 LocalTime（从结束时间获取时分秒）
            endCalendar.setTime(orderEndTime);
            LocalTime localEndTime = LocalTime.of(endCalendar.get(Calendar.HOUR_OF_DAY), endCalendar.get(Calendar.MINUTE), endCalendar.get(Calendar.SECOND));

            // 检查是否存在同一天且 schedule_type = 1 且时间段冲突的记录
            boolean hasConflict = checkScheduleConflict(userId, orderStartTime, localStartTime, localEndTime);

            if (hasConflict) {
                return ResultDTO.error("该时间段已被安排，不能重复接单");
            }

            // 创建新的接单日程
            CaregiverSchedule schedule = new CaregiverSchedule();
            schedule.setCaregiverId(userId);

            // 设置开始日期，只包含年月日，不包含时分秒
            schedule.setScheduleDate(orderStartTime);  // 设置订单的开始日期（只包含年月日）

            // 设置开始时间和结束时间，只包含时分秒
            schedule.setScheduleStartTime(localStartTime);  // 设置开始时间（LocalTime）
            schedule.setScheduleEndTime(localEndTime);  // 设置结束时间（LocalTime）

            schedule.setScheduleType(1); // 1=正常接单
            schedule.setCreateTime(new Date());
            schedule.setUpdateTime(new Date());


            // 插入新接单日程
            caregiverScheduleMapper.insert(schedule);

            // 在 Order 表中更新 caregiver_id 和 status
            order.setCaregiversId(userId); // 设置阿姨ID
            order.setStatus(1); // 设置订单状态为 1
            orderMapper.updateById(order); // 更新订单信息

            return ResultDTO.success("接单日程添加成功", null);
        } catch (Exception e) {
            e.printStackTrace();
            return ResultDTO.error("添加接单时间失败: " + e.getMessage());
        }
    }

    /**
     * 检查是否存在同一天且 schedule_type = 1 且时间段冲突的接单记录
     */
    private boolean checkScheduleConflict(Long caregiverId, Date scheduleDate, LocalTime startTime, LocalTime endTime) {
        // 查询数据库中是否存在相同日期且 schedule_type = 1 的记录
        QueryWrapper<CaregiverSchedule> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("caregiver_id", caregiverId)
                .eq("schedule_type", 1)
                .eq("schedule_date", scheduleDate);

        List<CaregiverSchedule> existingSchedules = caregiverScheduleMapper.selectList(queryWrapper);

        // 遍历数据库中已存在的接单日程，判断时间是否冲突
        for (CaregiverSchedule existingSchedule : existingSchedules) {
            // 获取现有日程的开始和结束时间
            LocalTime existingStartTime = existingSchedule.getScheduleStartTime();
            LocalTime existingEndTime = existingSchedule.getScheduleEndTime();

            // 判断时间是否冲突：检查是否有重叠的时间段
            if ((startTime.isBefore(existingEndTime) && startTime.isAfter(existingStartTime)) ||
                    (endTime.isBefore(existingEndTime) && endTime.isAfter(existingStartTime)) ||
                    (startTime.equals(existingStartTime) || endTime.equals(existingEndTime))) {
                return true; // 存在冲突
            }
        }

        return false; // 没有冲突
    }


    /**
     * 设置日程类型
     */
    @Override
    public ResultDTO<?> toggleScheduleStatus(CaregiverScheduleDTO caregiverScheduleDTO) {

        String loginId = StpUtil.getLoginIdAsString();
        LoginUserDTO loginInfo = (LoginUserDTO) StpUtil.getSession().get(String.valueOf(loginId));
        Long userId = loginInfo.getUserId();

        if (userId == null || caregiverScheduleDTO.getScheduleType() == null) {
            return ResultDTO.error("参数不能为空");
        }
        try {
            Integer type = caregiverScheduleDTO.getScheduleType();

            // 如果状态设置为 0，删除该阿姨的所有日程类型为 0 的记录
            if (type.equals(0)) {
                // 删除该阿姨的所有日程类型为 0 的记录
                QueryWrapper<CaregiverSchedule> deleteWrapper = new QueryWrapper<>();
                deleteWrapper.eq("caregiver_id", userId)
                        .eq("schedule_type", 0);  // 删除所有类型为 0 的日程记录
                caregiverScheduleMapper.delete(deleteWrapper);
            }

            // 更新阿姨的状态
            UpdateWrapper<Caregiver> updateWrapper = new UpdateWrapper<>();
            updateWrapper.eq("caregiver_id", userId)
                    .set("status", type.equals(1) ? 1 : 0);
            caregiverMapper.update(null, updateWrapper);

            String msg = type.equals(1) ? "开启接单状态" : "已停止接单";
            return ResultDTO.success(msg, null);
        } catch (Exception e) {
            e.printStackTrace();
            return ResultDTO.error("切换状态失败" + e.getMessage());
        }
    }

    /**
     * 冲突检测（status 为 0 或 1 都认为冲突）
     */
    @Override
    public boolean hasConflictSchedule(Long caregiverId, Date scheduleDate, LocalTime startTime, LocalTime endTime) {
        try {
            // 构造条件：找出与该时间段冲突的记录（仅考虑状态为1的接单记录）
            QueryWrapper<CaregiverSchedule> wrapper = new QueryWrapper<>();
            // 注意：我们仍然保留原来使用的日期部分
            wrapper.eq("caregiver_id", caregiverId)
                    .eq("schedule_type", 1) // 只检查接单时间（schedule_type = 1）
                    .apply("DATE(schedule_date) = {0}", new java.sql.Date(scheduleDate.getTime())) // 同一天
                    .and(w -> w.lt("schedule_end_time", endTime.toString()) // 如果结束时间在某个记录的开始时间之前，算冲突
                            .gt("schedule_start_time", startTime.toString())); // 如果开始时间在某个记录的结束时间之后，算冲突

            return caregiverScheduleMapper.selectCount(wrapper) > 0; // 存在冲突则返回 true
        } catch (Exception e) {
            e.printStackTrace();
            return true; // 出现异常时认为有冲突
        }
    }

    @Override
    @Transactional
    public void setOffDutyTime(CaregiverScheduleDTO caregiverScheduleDTO) {

        // 获取阿姨的下班时间
        Long caregiverId = caregiverScheduleDTO.getCaregiverId();
        LocalTime offDutyTime = caregiverScheduleDTO.getStopOrderTime();  // 确保这里是 LocalTime

        // 获取当前日期和时间
        LocalDateTime now = LocalDateTime.now();

        // 将阿姨的下班时间保存到数据库
        Caregiver caregiver = caregiverMapper.selectById(caregiverId);  // 根据阿姨ID查询
        if (caregiver != null) {
            caregiver.setStopOrderTime(offDutyTime);  // 直接传递 LocalTime
            caregiverMapper.updateById(caregiver);    // 更新数据库中的下班时间
            log.info("阿姨 {} 的下班时间 {} 已保存到数据库", caregiverId, offDutyTime);
        } else {
            log.error("未找到阿姨 ID: {}", caregiverId);
        }

        // 输出日志，确认获取的时间
        log.info("当前时间: {}", now);
        log.info("下班时间: {}", offDutyTime);
        log.info("当前时间的 LocalTime 部分: {}", now.toLocalTime());
    }

    // 定时任务每分钟执行一次
    @Scheduled(fixedRate = 60000) // 每60秒执行一次
    public void checkIfCaregiverCanOffDuty() {
        LocalDateTime now = LocalDateTime.now();
        // 查询所有阿姨
        List<Caregiver> allCaregivers = caregiverMapper.selectList(null);
        for (Caregiver caregiver : allCaregivers) {
            try {
                // 从数据库中获取阿姨的下班时间
                LocalTime offDutyTime = caregiver.getStopOrderTime();  // 假设数据库中已经有 stop_order_time 字段
                // 如果下班时间为空，跳过这个阿姨
                if (offDutyTime == null) {
//                        log.info("阿姨 {} 的下班时间未设置，跳过检查", caregiver.getCaregiverId());
                    continue;  // 跳过当前阿姨，继续检查其他阿姨
                }

                // 输出日志，确认获取的时间
                log.info("当前时间: {}", now);
                log.info("阿姨 {} 的下班时间: {}", caregiver.getCaregiverId(), offDutyTime);

                // 判断当前时间是否已经超过下班时间
                if (now.toLocalTime().isAfter(offDutyTime)) {
                    log.info("阿姨 {} 的下班时间已到，开始检查排班记录", caregiver.getCaregiverId());

                    // 查询是否有今天的排班（scheduleType = 1）且未执行完的订单
                    List<CaregiverSchedule> ongoingSchedules = caregiverScheduleMapper.selectList(
                            new QueryWrapper<CaregiverSchedule>()
                                    .eq("caregiver_id", caregiver.getCaregiverId())
                                    .eq("schedule_type", 1) // 只查询阿姨手动接的订单
                                    .apply("DATE(schedule_date) = CURDATE()") // 只查询今天的排班
                                    .ge("schedule_end_time", now) // 订单的结束时间未到当前时间
                    );


                    // 输出查询结果
                    log.info("查询到的未完成排班: {}", ongoingSchedules);

                    // 如果没有未完成的排班，阿姨可以下班
                    if (ongoingSchedules.isEmpty()) {
                        // 更新阿姨的状态为不可接单并删除所有排班记录
                        updateCaregiverStatusToOffline(caregiver.getCaregiverId());
                    } else {
                        log.info("阿姨 {} 有未完成的订单，无法下班", caregiver.getCaregiverId());
                    }
                }
            } catch (Exception e) {
                log.error("处理阿姨 {} 状态异常", caregiver.getCaregiverId(), e);
            }
        }
    }

    private void updateCaregiverStatusToOffline(Long caregiverId) {
        // 更新阿姨的状态为“停止接单”
        caregiverMapper.update(null,
                new UpdateWrapper<Caregiver>()
                        .eq("caregiver_id", caregiverId)
                        .set("status", 0));  // 设置阿姨状态为“停止接单”

        log.info("阿姨 {} 状态变更为不可接单", caregiverId);

        // 删除接单表中该阿姨的所有今天的排班记录
        caregiverScheduleMapper.delete(new QueryWrapper<CaregiverSchedule>()
                .eq("caregiver_id", caregiverId)
                .apply("DATE(schedule_date) = CURDATE()"));  // 只删除今天的记录

        log.info("阿姨 {} 的所有今天的接单记录已删除", caregiverId);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultDTO<Map<String, Object>> appointmentCaregiver(CaregiverScheduleDTO caregiverScheduleDTO) {
        String loginIdAsString = StpUtil.getLoginIdAsString();
        LoginUserDTO loginInfo = (LoginUserDTO) StpUtil.getSession().get(loginIdAsString);
        String phone = loginInfo.getPhone();
        Long userId = loginInfo.getUserId();
        Integer userType = loginInfo.getUserType();
        LambdaQueryWrapper<Wallet> walletLambdaQueryWrapper = new LambdaQueryWrapper<>();
        walletLambdaQueryWrapper.eq(Wallet::getUserId, userId)
                .eq(Wallet::getUserType, userType);
        Wallet wallet = walletMapper.selectOne(walletLambdaQueryWrapper);
        Long walletId = wallet.getWalletId();

        try {
            // 1. 检查该时间段是否已有安排
            LambdaQueryWrapper<CaregiverSchedule> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(CaregiverSchedule::getCaregiverId, caregiverScheduleDTO.getCaregiverId())
                    .eq(CaregiverSchedule::getScheduleDate, caregiverScheduleDTO.getScheduleDate())
                    .and(qw -> qw
                            .lt(CaregiverSchedule::getScheduleStartTime, caregiverScheduleDTO.getScheduleEndTime())
                            .gt(CaregiverSchedule::getScheduleEndTime, caregiverScheduleDTO.getScheduleStartTime()));
            List<CaregiverSchedule> conflictList = caregiverScheduleMapper.selectList(wrapper);
            if (!conflictList.isEmpty()) {
                throw new BizException("该时间段阿姨已有安排，请选择其他时间");
            }

            // 2. 插入新的日程
            CaregiverSchedule caregiverSchedule = new CaregiverSchedule();
            BeanUtils.copyProperties(caregiverScheduleDTO, caregiverSchedule);
            int insertCount = caregiverScheduleMapper.insert(caregiverSchedule);
            if (insertCount < 1) {
                throw new BizException("预约失败，日程保存异常");
            }

            // 3. 获取前端传来的订单信息
            OrderDTO orderDTO = caregiverScheduleDTO.getOrder();
            orderDTO.setCaregiversId(caregiverScheduleDTO.getCaregiverId());
            orderDTO.setOrderType(3);

            // 4. 优惠券校验和标记使用（新增逻辑）
            if (orderDTO.getCouponId() != null) {
                LambdaQueryWrapper<UserCoupon> userCouponLambdaQueryWrapper = new LambdaQueryWrapper<>();
                userCouponLambdaQueryWrapper
                        .select(UserCoupon::getUserCouponId, UserCoupon::getCouponId, UserCoupon::getExpirationTime, UserCoupon::getStatus)
                        .eq(UserCoupon::getCouponId, orderDTO.getCouponId())
                        .eq(UserCoupon::getUserId, userId);
                UserCoupon userCoupon = userCouponMapper.selectOne(userCouponLambdaQueryWrapper);
                if (userCoupon == null || userCoupon.getStatus() == 2) {
                    throw new BizException("优惠券已使用或已过期");
                }
                if (userCoupon.getExpirationTime().before(new Date())) {
                    throw new BizException("优惠券已过期");
                }

                Coupon coupon = couponMapper.selectById(orderDTO.getCouponId());
                Integer couponType = coupon.getCouponType();
                int serviceTypeId = orderDTO.getServiceTypeId().intValue();
                if (couponType != 0 && couponType != serviceTypeId) {
                    return ResultDTO.error("优惠券不可用于此服务类型");
                }

                // 标记优惠券为已使用
                UserCoupon updateUserCoupon = new UserCoupon();
                updateUserCoupon.setUserCouponId(userCoupon.getUserCouponId());
                updateUserCoupon.setStatus(2);
                updateUserCoupon.setUsedTime(new Date());
                userCouponMapper.updateById(updateUserCoupon);
            }

            Integer payMethods = orderDTO.getPayMethods();
            if (payMethods == 1 || payMethods == 6) {
                orderDTO.setPayMethods4DB(1);
            } else {
                orderDTO.setPayMethods4DB(payMethods);
            }
            int payMethods4DB = orderDTO.getPayMethods4DB();

            // 5. 创建订单
            Order order = orderService.createAndInsertOrder(orderDTO, userId, phone, 7);
            List<OrderAttribute> list = orderDTO.getAttributes().stream().map(attr -> {
                OrderAttribute entity = new OrderAttribute();
                entity.setOrderId(order.getOrderId());
                entity.setAttributeKey(attr.getAttributeKey());
                entity.setAttributeValue(attr.getValue());
                return entity;
            }).collect(Collectors.toList());
            orderAttributeMapper.insertBatch(list);

            // 6. 预估时间
            int estimatedTime = orderService.calculateEstimatedTime(list, orderDTO.getServiceTypeId());
            Order updateOrder = new Order();
            updateOrder.setOrderId(order.getOrderId());
            updateOrder.setEstimatedTime(estimatedTime);
            orderMapper.updateById(updateOrder);

            // 7. 插入交易流水
            WalletTransaction walletTransaction = new WalletTransaction();
            walletTransaction.setWalletId(walletId);
            walletTransaction.setTransactionType(2);
            walletTransaction.setAmount(orderDTO.getActualPaymentPrice());
            walletTransaction.setTransactionTime(new Date());
            walletTransaction.setOutTradeNo(order.getOrderNumber());
            walletTransaction.setStatus(0);
            walletTransaction.setPaymentChannel(payMethods4DB);
            walletTransactionMapper.insert(walletTransaction);

            // 8. 执行支付
            PayStrategy payStrategy = payContext.executePay(payMethods);
            HashMap<String, Object> map = new HashMap<>();
            map.put("userId", userId);
            map.put("userType", userType);
            map.put("payPassword", orderDTO.getPayPassword());
            map.put("orderId", order.getOrderId());
            map.put("orderNumber", order.getOrderNumber());
            map.put("transactionType", 2); // 消费
            map.put("actualPaymentPrice", orderDTO.getActualPaymentPrice());
            map.put("description", "下单");
            map.put("payMethods", orderDTO.getPayMethods());
            map.put("code", caregiverScheduleDTO.getCode());
            Map<String, Object> pay = payStrategy.pay(map);

            // 9. 发放优惠券（仅限微信/支付宝支付）
            if (orderDTO.getPayMethods() == 3) {
//                Map<String, Object> grantedCoupon = orderService.grantRandomCoupon(userId);
//                if (grantedCoupon != null) {
//                    pay.put("grantedCoupon", grantedCoupon);
//                }
//                HashMap<String, Object> payload = new HashMap<>();
//                payload.put("orderId", order.getOrderId());
//                webSocketServer.sendMessageToTarget(
//                        "caregiver:" + caregiverScheduleDTO.getCaregiverId().toString(),
//                        payload,
//                        WebSocketMessageType.WORKER_ORDER_RESPONSE.getType()
//                );
            }

            return ResultDTO.success("预约成功", pay);
        } catch (BizException e) {
            throw e;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 已分配到阿姨，更新订单及阿姨信息
     *
     * @param order
     * @param caregiverId
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateAssignCaregiver(Order order, Long caregiverId) {
        try {
            // 1. 更新订单信息
            order.setCaregiversId(caregiverId);
            order.setStatus(1); // 状态改为待接单
            orderMapper.updateById(order);

            // 2. 写入阿姨排班信息
            CaregiverSchedule schedule = new CaregiverSchedule();
            schedule.setCaregiverId(caregiverId);
            schedule.setScheduleType(1);

            // 提取日期和开始结束时间
            Date appointmentDate = order.getAppointmentTime();
            LocalDate localDate = appointmentDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
            LocalTime startTime = appointmentDate.toInstant().atZone(ZoneId.systemDefault()).toLocalTime();
            LocalTime endTime = startTime.plusMinutes(order.getEstimatedTime());

            schedule.setScheduleDate(Date.from(localDate.atStartOfDay(ZoneId.systemDefault()).toInstant()));
            schedule.setScheduleStartTime(startTime);
            schedule.setScheduleEndTime(endTime);

            caregiverScheduleMapper.insert(schedule);
        } catch (Exception e) {
            log.error("自动分配阿姨失败", e);
            throw new RuntimeException(e);
        }
    }


    /**
     * 获取自动分配
     *
     * @param order
     * @return
     */
    public Long autoAssignCaregiver(Order order) {
        Address userAddress = addressMapper.selectById(order.getAddressId());
        double userLat = userAddress.getLatitude();
        double userLng = userAddress.getLongitude();

        int radiusKm = 5; // 初始搜索半径
        int maxRadiusKm = 50; // 最大搜索半径限制
        int stepKm = 1; // 每次递增1公里

        Date appointmentDate = order.getAppointmentTime();

        // 1. 提取日期（去除时分秒）
        LocalDate scheduleDate = appointmentDate.toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDate();
        Date scheduleDateOnly = Date.from(scheduleDate.atStartOfDay(ZoneId.systemDefault()).toInstant());

        // 2. 提取开始时间
        LocalTime appointmentStart = appointmentDate.toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalTime();

        // 3. 计算结束时间
        LocalTime appointmentEnd = appointmentStart.plusMinutes(order.getEstimatedTime());


        while (radiusKm <= maxRadiusKm) {
            Long serviceTypeId = order.getServiceTypeId();
            // 1. 查询 radiusKm 公里以内的阿姨位置信息
            List<Location> nearbyLocations = locationMapper.findWithinDistance(userLat, userLng, radiusKm, serviceTypeId);

            if (CollectionUtils.isEmpty(nearbyLocations)) {
                radiusKm += stepKm;
                continue;
            }

            List<Long> caregiverIds = nearbyLocations.stream()
                    .map(Location::getCaregiverId)
                    .distinct()
                    .collect(Collectors.toList());

            // 2. 过滤今天无订单的阿姨 且不在休息时间内（更高优先级）
            List<Long> caregiversWithNoSchedule = caregiverScheduleMapper.findCaregiversWithNoScheduleOnDate(caregiverIds, scheduleDateOnly);

            if (!caregiversWithNoSchedule.isEmpty()) {
                return getBestCaregiverByLeastOrders(caregiversWithNoSchedule, scheduleDate);
//                return getBestCaregiver(caregiversWithNoSchedule); // 自定义选择策略
            }

            // 3. 过滤与当前订单时间不冲突的阿姨
            List<Long> caregiversTimeOK = caregiverScheduleMapper.findCaregiversWithNoTimeConflict(caregiverIds, scheduleDate, appointmentStart, appointmentEnd);

            if (!caregiversTimeOK.isEmpty()) {
                return getBestCaregiverByLeastOrders(caregiversTimeOK, scheduleDate);
            }

            // 4. 增加半径，继续找
            radiusKm += stepKm;
        }

        // 5. 如果都找不到
        return null;
    }

    /**
     * 自定义选择策略（取出第一个阿姨）
     *
     * @param caregiversWithNoSchedule 阿姨id列表
     * @return
     */
    private Long getBestCaregiver(List<Long> caregiversWithNoSchedule) {
        Long caregiverId = caregiversWithNoSchedule.get(0);
        return caregiverId;
    }

    /**
     * 获取今天订单最少的阿姨
     *
     * @param caregiverIds
     * @param scheduleDate
     * @return
     */
    public Long getBestCaregiverByLeastOrders(List<Long> caregiverIds, LocalDate scheduleDate) {
        if (caregiverIds == null || caregiverIds.isEmpty()) {
            return null;
        }

        // 获取当天起止时间
        Date todayStart = Date.from(scheduleDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
        Date todayEnd = Date.from(scheduleDate.plusDays(1).atStartOfDay(ZoneId.systemDefault()).toInstant());

        // 查询每个阿姨的订单数
        List<Map<String, Object>> rawResult = orderMapper.countTodayOrdersForCaregivers(caregiverIds, todayStart, todayEnd);
        // 手动组装 caregiverId -> orderCount 的 Map
        Map<Long, Integer> orderCountMap = new HashMap<>();
        for (Map<String, Object> row : rawResult) {
            Long caregiverId = ((Number) row.get("caregiverId")).longValue();
            Integer count = ((Number) row.get("orderCount")).intValue();
            orderCountMap.put(caregiverId, count);
        }
        // 找出订单最少的阿姨（默认选第一个）
        return caregiverIds.stream()
                .min(Comparator.comparing(id -> orderCountMap.getOrDefault(id, 0)))
                .orElse(null);
    }
}




