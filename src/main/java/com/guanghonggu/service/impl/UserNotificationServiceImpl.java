package com.guanghonggu.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.guanghonggu.dto.UserNotificationDTO;
import com.guanghonggu.entity.UserNotification;
import com.guanghonggu.mapper.UserNotificationMapper;
import com.guanghonggu.service.UserNotificationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class UserNotificationServiceImpl extends ServiceImpl<UserNotificationMapper, UserNotification>
        implements UserNotificationService {
    @Autowired
    private UserNotificationMapper userNotificationMapper;

    @Override
    public List<UserNotification> getUserNotification(UserNotificationDTO userNotificationDTO){
        LambdaQueryWrapper<UserNotification> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserNotification::getOrderId, userNotificationDTO.getOrderId());
        return userNotificationMapper.selectList(queryWrapper);
    }
}
