package com.guanghonggu.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.guanghonggu.entity.CouponServiceType;
import com.guanghonggu.service.CouponServiceTypeService;
import com.guanghonggu.mapper.CouponServiceTypeMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【coupon_service_type】的数据库操作Service实现
* @createDate 2025-04-29 16:18:24
*/
@Service
public class CouponServiceTypeServiceImpl extends ServiceImpl<CouponServiceTypeMapper, CouponServiceType>
    implements CouponServiceTypeService{

}




