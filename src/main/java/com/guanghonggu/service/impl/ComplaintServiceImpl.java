package com.guanghonggu.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.guanghonggu.dto.ComplaintDTO;
import com.guanghonggu.dto.LoginUserDTO;
import com.guanghonggu.dto.ResultDTO;
import com.guanghonggu.entity.Complaint;
import com.guanghonggu.entity.ComplaintTypeRelation;
import com.guanghonggu.mapper.ComplaintTypeRelationMapper;
import com.guanghonggu.service.ComplaintService;
import com.guanghonggu.mapper.ComplaintMapper;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【complaint(投诉表)】的数据库操作Service实现
 * @createDate 2025-04-27 10:56:56
 */
@Service
@Transactional
public class ComplaintServiceImpl extends ServiceImpl<ComplaintMapper, Complaint>
        implements ComplaintService {

    @Autowired
    private ComplaintMapper complaintMapper;

    @Autowired
    private ComplaintTypeRelationMapper complaintTypeRelationMapper;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultDTO<String> addComplaint(ComplaintDTO complaintDTO) {
        String loginIdAsString = StpUtil.getLoginIdAsString();
        LoginUserDTO loginUser = (LoginUserDTO) StpUtil.getSession().get(loginIdAsString);
        Long userId = loginUser.getUserId();

        Complaint complaint = new Complaint();
        BeanUtils.copyProperties(complaintDTO, complaint);
        complaint.setStatus(0);
        complaint.setUserId(userId);
        complaintMapper.insert(complaint);

        List<Long> complaintTypeList = complaintDTO.getComplaintTypeList();
        for (Long id : complaintTypeList) {
            ComplaintTypeRelation complaintTypeRelation = new ComplaintTypeRelation();
            complaintTypeRelation.setComplaintId(complaint.getComplaintId());
            complaintTypeRelation.setTypeId(id);
            complaintTypeRelationMapper.insert(complaintTypeRelation);
        }
        return ResultDTO.notDataSuccess("投诉成功");
    }
}




