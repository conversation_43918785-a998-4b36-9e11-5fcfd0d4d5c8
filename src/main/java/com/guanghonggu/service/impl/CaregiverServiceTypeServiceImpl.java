package com.guanghonggu.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.guanghonggu.dto.LoginUserDTO;
import com.guanghonggu.dto.ResultDTO;
import com.guanghonggu.entity.Caregiver;
import com.guanghonggu.entity.CaregiverServiceType;
import com.guanghonggu.mapper.CaregiverMapper;
import com.guanghonggu.service.CaregiverServiceTypeService;
import com.guanghonggu.mapper.CaregiverServiceTypeMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【caregiver_service_type】的数据库操作Service实现
 * @createDate 2025-04-29 16:18:24
 */
@Service
public class CaregiverServiceTypeServiceImpl extends ServiceImpl<CaregiverServiceTypeMapper, CaregiverServiceType>
        implements CaregiverServiceTypeService {

    @Autowired
    private CaregiverServiceTypeMapper caregiverServiceTypeMapper;


    @Override
    public ResultDTO<List<CaregiverServiceType>> getCaregiverServiceType() {
        String loginIdAsString = StpUtil.getLoginIdAsString();
        LoginUserDTO loginInfo = (LoginUserDTO) StpUtil.getSession().get(loginIdAsString);
        Long caregiverId = loginInfo.getUserId();
        LambdaQueryWrapper<CaregiverServiceType> caregiverServiceTypeLambdaQueryWrapper = new LambdaQueryWrapper<>();
        caregiverServiceTypeLambdaQueryWrapper
                .select(CaregiverServiceType::getId, CaregiverServiceType::getServiceTypeId)
                .eq(CaregiverServiceType::getCaregiverId, caregiverId);
        List<CaregiverServiceType> caregiverList = caregiverServiceTypeMapper.selectList(caregiverServiceTypeLambdaQueryWrapper);
        return ResultDTO.success("获取成功", caregiverList);
    }
}




