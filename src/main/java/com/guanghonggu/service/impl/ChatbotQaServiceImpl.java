package com.guanghonggu.service.impl;

import com.alibaba.dashscope.embeddings.TextEmbedding;
import com.alibaba.dashscope.embeddings.TextEmbeddingParam;
import com.alibaba.dashscope.embeddings.TextEmbeddingResult;
import com.alibaba.dashscope.embeddings.TextEmbeddingResultItem;
import com.alibaba.dashscope.exception.NoApiKeyException;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.guanghonggu.dto.ResultDTO;
import com.guanghonggu.entity.ChatbotConversation;
import com.guanghonggu.entity.ChatbotQa;
import com.guanghonggu.mapper.ChatbotConversationMapper;
import com.guanghonggu.service.ChatbotQaService;
import com.guanghonggu.mapper.ChatbotQaMapper;
import io.milvus.v2.client.ConnectConfig;
import io.milvus.v2.client.MilvusClientV2;
import io.milvus.v2.service.vector.request.SearchReq;
import io.milvus.v2.service.vector.request.data.FloatVec;
import io.milvus.v2.service.vector.response.SearchResp;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【chatbot_qa】的数据库操作Service实现
 * @createDate 2025-08-27 15:25:18
 */
@Service
public class ChatbotQaServiceImpl extends ServiceImpl<ChatbotQaMapper, ChatbotQa>
        implements ChatbotQaService {

    @Autowired
    private ChatbotQaMapper chatbotQaMapper;

    @Autowired
    private ChatbotConversationMapper conversationMapper;

    private MilvusClientV2 client;

    @PostConstruct
    public void init() {
        client = new MilvusClientV2(ConnectConfig.builder()
                .uri("http://************:19530")
                .token("root:Milvus")
                .build());
    }
    @Override
    public ResultDTO<String> searchAnswer(String query) {
        try {
            // 1. 生成 query embedding
            List<Float> queryEmbedding = getEmbeddingFromDashScope(query);

            // 2. 搜索 Milvus 找到最相似的 QA id
            Long qaId = searchSimilarQA(queryEmbedding);
            if (qaId == null) return null;

            // 3. 根据 qaId 查询 MySQL
            ChatbotQa qa = chatbotQaMapper.selectById(qaId);
            if (qa == null) return null;
            ChatbotConversation record = new ChatbotConversation();
            record.setUserId(1L);
            record.setQuestion(query);
            record.setAnswer(qa.getAnswer());
            conversationMapper.insert(record);
            return ResultDTO.success("OK", qa.getAnswer());
        } catch (Exception e) {
            log.error("回答异常");
            throw new RuntimeException(e);
        }
    }

    /**
     * 调用 DashScope embedding API
     */
    private List<Float> getEmbeddingFromDashScope(String text) throws NoApiKeyException {
        TextEmbeddingParam param = TextEmbeddingParam
                .builder()
                .model("text-embedding-v4")  // 使用text-embedding-v4模型
                .apiKey("sk-0fcc39a7c92c4fa59b0b2303c35f9ce7")
                .texts(Arrays.asList(text))  // 输入文本
                .parameter("dimension", 1024)  // 指定向量维度（仅 text-embedding-v3及 text-embedding-v4支持该参数）
                .build();

        // 创建模型实例并调用
        TextEmbedding textEmbedding = new TextEmbedding();
        TextEmbeddingResult result = textEmbedding.call(param);
        List<TextEmbeddingResultItem> embeddings = result.getOutput().getEmbeddings();
        List<Double> embeddingDouble = embeddings.get(0).getEmbedding();

        // 转换成 List<Float>
        List<Float> embeddingFloat = new ArrayList<>(embeddingDouble.size());
        for (Double d : embeddingDouble) {
            embeddingFloat.add(d.floatValue());
        }
        return embeddingFloat;
    }

    public Long searchSimilarQA(List<Float> embedding) {
        FloatVec floatVec = new FloatVec(embedding);
        SearchReq searchReq = SearchReq.builder()
                .collectionName("chatbot_vector")
                .data(Collections.singletonList(floatVec))
                .topK(3)
                .outputFields(Collections.singletonList("qa_id"))
                .build();

        SearchResp searchResp = client.search(searchReq);
        List<SearchResp.SearchResult> searchResults = searchResp.getSearchResults().get(0);
        // 取出 score 最低的那个
        SearchResp.SearchResult minResult = Collections.min(
                searchResults,
                Comparator.comparing(SearchResp.SearchResult::getScore) // 按 score 排序
        );
        Long qaId = (Long) minResult.getEntity().get("qa_id");

        System.out.println("最低分结果: " + minResult.getId() + " , score=" + minResult.getScore());
        return qaId;
    }
}




