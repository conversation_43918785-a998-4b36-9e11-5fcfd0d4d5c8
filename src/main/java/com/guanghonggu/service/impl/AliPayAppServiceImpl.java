package com.guanghonggu.service.impl;


import cn.dev33.satoken.stp.StpUtil;
import com.alipay.api.AlipayApiException;
import com.alipay.api.internal.util.AlipaySignature;
import com.alipay.api.response.AlipayFundTransUniTransferResponse;
import com.alipay.api.response.AlipayTradeQueryResponse;
import com.guanghonggu.dto.LoginUserDTO;
import com.guanghonggu.dto.ResultDTO;
import com.guanghonggu.dto.WalletTransactionDTO;
import com.guanghonggu.entity.Order;
import com.guanghonggu.entity.Wallet;
import com.guanghonggu.entity.WalletTransaction;
import com.guanghonggu.mapper.WalletMapper;
import com.guanghonggu.mapper.WalletTransactionMapper;
import com.guanghonggu.service.AlipayAppService;
import com.guanghonggu.strategy.pay.strategy.PayQueryHandler;
import com.guanghonggu.strategy.pay.alipay.AlipayUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * @author：zzn
 * @description：
 * @createData：2025/6/19 13:44
 */
@Slf4j
@Service
public class AliPayAppServiceImpl implements AlipayAppService {
    @Autowired
    private AlipayUtil alipayUtil;

    @Autowired
    private PayQueryHandler payQueryHandler;

    @Autowired
    private WalletMapper walletMapper;

    @Autowired
    private WalletTransactionMapper walletTransactionMapper;

    @Autowired
    private WalletServiceImpl walletService;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultDTO<Map<String, Object>> searchAlipayByOrderId(String orderNumber) {
        try {
            AlipayTradeQueryResponse response = alipayUtil.queryOrder(orderNumber);
            if (!response.isSuccess()) {
                return ResultDTO.error("支付宝查单失败：" + response.getSubMsg());
            }

            String tradeStatus = response.getTradeStatus();
            String outTradeNo = response.getOutTradeNo();
            Date sendPayDate = response.getSendPayDate();
            BigDecimal amountYuan = new BigDecimal(response.getTotalAmount()).setScale(2, RoundingMode.HALF_UP);

            Order order = payQueryHandler.getOrderInfo(orderNumber);
            if (order == null) {
                return ResultDTO.error("订单不存在");
            }

            switch (tradeStatus) {
                case "TRADE_SUCCESS":
                    return payQueryHandler.handlePaySuccess(order, outTradeNo, response.getTradeNo(), sendPayDate.toString(), amountYuan);
                case "TRADE_CLOSED":
                    return payQueryHandler.handlePayClosed(order, outTradeNo);
                default:
                    return payQueryHandler.handleNotPay();
            }

        } catch (AlipayApiException e) {
            log.error("支付宝查单异常", e);
            return ResultDTO.error("支付宝查单异常：" + e.getErrMsg());
        }
    }

    @Override
    public String handleRechargeNotify(HttpServletRequest request) {
        try {
            Map<String, String[]> requestParams = request.getParameterMap();
            Map<String, String> params = new HashMap<>();
            for (String name : requestParams.keySet()) {
                params.put(name, request.getParameter(name));
            }

            boolean signVerified = AlipaySignature.rsaCheckV1(
                    params,
                    alipayUtil.getAlipayPublicKey(),
                    alipayUtil.getCharset(),
                    alipayUtil.getSignType()
            );

            String outTradeNo = params.get("out_trade_no");
            WalletTransaction transaction = payQueryHandler.getPendingRechargeTransaction(outTradeNo);
            if (transaction == null) {
                log.warn("【支付宝充值回调】未找到待处理交易，订单号：{}", outTradeNo);
                return "fail";
            }

            if (signVerified && "TRADE_SUCCESS".equals(params.get("trade_status"))) {
                BigDecimal amount = new BigDecimal(params.get("total_amount"));
                String tradeNo = params.get("trade_no");
                String payDateStr = params.get("send_pay_date");

                Date payTime = StringUtils.hasText(payDateStr) ?
                        Date.from(LocalDateTime.parse(payDateStr, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))
                                .atZone(ZoneId.systemDefault()).toInstant())
                        : new Date();

                payQueryHandler.handleRechargeSuccess(transaction, tradeNo, payTime, amount, 2); // 2 = 支付宝
                return "success";
            } else {
                payQueryHandler.handleRechargeClosed(transaction);
                return "fail";
            }

        } catch (Exception e) {
            log.error("【支付宝充值回调】处理异常", e);
            return "fail";
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class) // 确保加余额与更新交易状态是原子操作
    public void handleSuccessfulRecharge(WalletTransaction tx) {
        // 幂等判断：如果已处理，就跳过
        if (tx.getStatus() == 1) {
            return;
        }
        Wallet wallet = walletMapper.selectWalletByIdForUpdate(tx.getWalletId());
        if (wallet == null) {
            throw new RuntimeException("找不到对应的钱包，walletId=" + tx.getWalletId());
        }
        if (wallet.getBalance() == null) {
            wallet.setBalance(BigDecimal.ZERO);
        }
        if (wallet.getTotalIncome() == null) {
            wallet.setTotalIncome(BigDecimal.ZERO);
        }

        wallet.setBalance(wallet.getBalance().add(tx.getAmount()));
        wallet.setTotalIncome(wallet.getTotalIncome().add(tx.getAmount()));
        walletMapper.updateById(wallet);


        // 修改交易状态为已完成
        tx.setStatus(1);
        walletTransactionMapper.updateById(tx);

        log.info("成功补偿订单：{}，充值金额：{} 元", tx.getOutTradeNo(), tx.getAmount());
    }

    @Override
    public ResultDTO<?> withdraw(WalletTransactionDTO dto) {
        String loginIdAsString = StpUtil.getLoginIdAsString();
        LoginUserDTO loginUser = (LoginUserDTO) StpUtil.getSession().get(loginIdAsString);
        Long userId = loginUser.getUserId();
        // 查询钱包
        Wallet wallet = walletMapper.selectById(dto.getWalletId());
        if (wallet == null) {
            return ResultDTO.error("钱包不存在");
        }

        BigDecimal amount = dto.getAmount();
        if (wallet.getBalance().compareTo(amount) < 0) {
            return ResultDTO.error("余额不足，无法提现");
        }

        // 生成提现单号
//        String outTradeNo = "WITHDRAW_" + System.currentTimeMillis();
        String outTradeNo = walletService.generateRechargeOrderNo(userId, 3);

        // 插入提现记录（状态=0 待处理）
        WalletTransaction tx = new WalletTransaction();
        tx.setWalletId(dto.getWalletId());
        tx.setTransactionType(3); // 提现
        tx.setAmount(amount);
        tx.setStatus(0);
        tx.setOutTradeNo(outTradeNo);
        tx.setCreateTime(new Date());
        walletTransactionMapper.insert(tx);

        // 调用支付宝转账接口
        AlipayFundTransUniTransferResponse response = alipayUtil.transferToAlipay(
                dto.getAlipayAccount(),
                dto.getAmount(),
                outTradeNo,
                dto.getRealName()
        );

        if (response == null || !response.isSuccess()) {
            tx.setStatus(2);
            walletTransactionMapper.updateById(tx);
            return ResultDTO.error("支付宝转账失败");
        }

        String tradeNo = response.getOrderId(); // 获取 external_trade_no

        // 更新钱包余额和总支出
        BigDecimal oldBalance = wallet.getBalance() != null
                ? wallet.getBalance()
                : BigDecimal.ZERO;
        BigDecimal oldTotalSpent = wallet.getTotalSpent() != null
                ? wallet.getTotalSpent()
                : BigDecimal.ZERO;

        wallet.setBalance(oldBalance.subtract(amount));
        wallet.setTotalSpent(oldTotalSpent.add(amount));

        walletMapper.updateById(wallet);

        // 更新交易记录为成功
        tx.setStatus(1);
        tx.setPaymentChannel(2);
        tx.setExternalTradeNo(tradeNo);
        tx.setTransactionTime(new Date());
        tx.setUpdateTime(new Date());
        walletTransactionMapper.updateById(tx);

        return ResultDTO.success("提现成功", null);
    }
}
