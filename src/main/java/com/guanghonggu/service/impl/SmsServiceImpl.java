package com.guanghonggu.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import com.alibaba.fastjson.JSON;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.dysmsapi.model.v20170525.SendSmsRequest;
import com.aliyuncs.dysmsapi.model.v20170525.SendSmsResponse;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.profile.DefaultProfile;
import com.guanghonggu.dto.LoginUserDTO;
import com.guanghonggu.dto.UserDTO;
import com.guanghonggu.handler.exception.BizException;
import com.guanghonggu.service.CaregiverService;
import com.guanghonggu.service.SmsService;
import com.guanghonggu.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.Iterator;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;


@Slf4j
@Service
public class SmsServiceImpl implements SmsService {

    @Autowired
    private UserService userService;
    @Autowired
    private CaregiverService caregiverService;

    @Value("${aliyun.sms.accessKeyId}")
    private String accessKeyId;  // 阿里云的 AccessKeyId

    @Value("${aliyun.sms.accessKeySecret}")
    private String accessKeySecret;  // 阿里云的 AccessKeySecret

    @Value("${aliyun.sms.signName}")
    private String signName;  // 阿里云的短信签名

    @Value("${aliyun.sms.changePayPasswordTemplateCode}")
    private String changePayPasswordTemplateCode;

    @Value("${aliyun.sms.LoginTemplateCode}")
    private  String LoginTemplateCode;

    private static final Map<String, Object[]> verificationCodeMap = new ConcurrentHashMap<>();

    private static final Map<String, Object[]> loginCodeMap = new ConcurrentHashMap<>();
    //验证码保存时间
    private static final long EXPIRATION_TIME = 5 * 60 * 1000;
    // 定义请求频率限制时间（单位：毫秒，1分钟）
    private static final long REQUEST_INTERVAL = 60 * 1000;

    private static final Map<String, Long> requestTimestampMap = new ConcurrentHashMap<>(); // 存储请求时间戳


    /**
     * 发送短信验证码
     *
     * @param phoneNumber      手机号
     * @param verificationCode 生成的验证码
     * @return 发送结果，true表示发送成功，false表示失败
     */
    @Override
    public boolean sendSms(String phoneNumber, String verificationCode) {
        // 校验手机号格式是否正确
        if (!isValidPhoneNumber(phoneNumber)) {
            throw new BizException("手机号格式不正确");
        }

        // 校验是否超过请求频率
        if (!isRequestAllowed(phoneNumber)) {
            throw new BizException("请一分钟后再试");
        }

        sendPhoneVerification(phoneNumber, verificationCode,changePayPasswordTemplateCode);
        verificationCodeMap.put(phoneNumber + "_CHANGE", new Object[]{verificationCode, System.currentTimeMillis()});

        return true;
    }

    /**
     * 发送手机验证码
     * @param phoneNumber
     * @param verificationCode
     */
    public void sendPhoneVerification(String phoneNumber, String verificationCode,String templateCode) {
        DefaultProfile profile = DefaultProfile.getProfile("cn-hangzhou", accessKeyId, accessKeySecret);
        IAcsClient client = new DefaultAcsClient(profile);

        // 创建API请求并设置参数
        SendSmsRequest request = new SendSmsRequest();

        request.setPhoneNumbers(phoneNumber); // 该参数值为假设值，请您根据实际情况进行填写

        request.setSignName(signName); // 该参数值为假设值，请您根据实际情况进行填写
        request.setTemplateCode(templateCode);
        request.setTemplateParam("{\"code\":\"" + verificationCode + "\"}");

        try {
            SendSmsResponse response = client.getAcsResponse(request);
            log.info("response:{}", JSON.toJSONString(response));
        } catch (ClientException e) {
            log.error("发送失败", e);
            throw new RuntimeException(e);
        }
    }

    /**
     * 验证短信验证码
     */
    @Override
    public boolean verifySmsCode(UserDTO userDTO) {
        String loginIdAsString = StpUtil.getLoginIdAsString();
        LoginUserDTO loginUser = (LoginUserDTO) StpUtil.getSession().get(loginIdAsString);
        userDTO.setUserId(loginUser.getUserId());
        Integer userType = loginUser.getUserType();
        String phoneNumber = userDTO.getPhoneNumber();
        String verificationCode = userDTO.getVerificationCode();
        // 从 Map 中获取存储的验证码及时间戳
        Object[] storedData = verificationCodeMap.get(phoneNumber + "_CHANGE");

        // 判断验证码是否存在
        if (storedData == null) {
            return false;  // 没有找到验证码
        }

        // 获取存储的验证码和时间戳
        String storedCode = (String) storedData[0];
        long storedTimestamp = (long) storedData[1];

        // 判断验证码是否过期
        long currentTime = System.currentTimeMillis();
        if (currentTime - storedTimestamp > EXPIRATION_TIME) {
            verificationCodeMap.remove(phoneNumber + "_CHANGE");  // 超时后移除验证码
            return false;  // 验证码已过期
        }

        // 判断验证码是否匹配
        if (!storedCode.equals(verificationCode)) {
            return false;  // 验证码不匹配
        }

        verificationCodeMap.remove(phoneNumber + "_CHANGE");
        // 如果验证码验证成功，删除数据库中的支付密码
        try {
            if (userType == 1) {
                // 调用 UserService 删除用户的支付密码
                userService.deleteUserPayPassword(phoneNumber);
            } else if (userType == 2) {
                caregiverService.deleteCaregiverPayPassword(phoneNumber);
            }
        } catch (Exception e) {
            // 如果删除失败，可以根据需要处理异常
            e.printStackTrace();
            return false;  // 删除失败返回 false
        }

        return true;  // 验证码验证成功并删除密码
    }

    private boolean isValidPhoneNumber(String phoneNumber) {
        return phoneNumber != null && phoneNumber.matches("^1[3-9]\\d{9}$");
    }

    // 限流：1分钟内只能请求一次验证码
    private boolean isRequestAllowed(String phoneNumber) {
        long currentTime = System.currentTimeMillis();
        Long lastRequestTime = requestTimestampMap.get(phoneNumber);

        if (lastRequestTime == null) {
            requestTimestampMap.put(phoneNumber, currentTime);
            return true;
        } else {
            if (currentTime - lastRequestTime < REQUEST_INTERVAL) {
                return false;
            } else {
                requestTimestampMap.put(phoneNumber, currentTime);
                return true;
            }
        }
    }

    // 定期清理过期验证码
    @Scheduled(fixedRate = 60 * 1000)
    public void cleanExpiredCodes() {
        long currentTime = System.currentTimeMillis();

        cleanMap(verificationCodeMap,currentTime);
        cleanMap(loginCodeMap,currentTime);
    }

    private void cleanMap(Map<String, Object[]> map, long currentTime) {
        Iterator<Map.Entry<String, Object[]>> iterator = map.entrySet().iterator();

        while (iterator.hasNext()) {
            Map.Entry<String, Object[]> entry = iterator.next();
            Object[] value = entry.getValue();
            long storedTimestamp = (long) value[1];

            if (currentTime - storedTimestamp > EXPIRATION_TIME) {
                iterator.remove();
            }
        }
    }

    @Override
    public boolean sendLoginSms(String phoneNumber, String verificationCode){
        //校验手机号格式
        if (!isValidPhoneNumber(phoneNumber)){
            throw new BizException("手机号格式不正确");
        }
        //校验是否超出频率
        if(!isRequestAllowed(phoneNumber)){
            throw new BizException("请一分钟之后再试");
        }

        sendPhoneVerification(phoneNumber,verificationCode,LoginTemplateCode);
        loginCodeMap.put(phoneNumber + "_LOGIN", new Object[]{verificationCode, System.currentTimeMillis()});

        return true;
    }

    @Override
    public boolean verifySmsCodeForLogin(String phoneNumber, String verificationCode){
       Object[] storedData = loginCodeMap.get(phoneNumber + "_LOGIN");

       if (storedData == null) {
           return  false;
       }

       String storedCode = (String)  storedData[0];
       long storedTimestamp = (Long) storedData[1];

       //判断验证码是否过期
        long currentTime = System.currentTimeMillis();
        if (currentTime - storedTimestamp > EXPIRATION_TIME) {
            loginCodeMap.remove(phoneNumber + "_LOGIN");
            return false;
        }

        //判断验证码是否匹配
        if (!storedCode.equals(verificationCode)) {
            return false;
        }

        //验证成功后移除验证码
        loginCodeMap.remove(phoneNumber + "_LOGIN");
        return  true;
    }
}