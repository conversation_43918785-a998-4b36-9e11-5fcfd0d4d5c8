package com.guanghonggu.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import com.aliyuncs.CommonRequest;
import com.aliyuncs.CommonResponse;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.profile.DefaultProfile;
import com.aliyuncs.profile.IClientProfile;
import com.guanghonggu.dto.LoginUserDTO;
import com.guanghonggu.dto.UserDTO;
import com.guanghonggu.service.CaregiverService;
import com.guanghonggu.service.SmsService;
import com.guanghonggu.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.Iterator;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;


@Service
public class SmsServiceImpl implements SmsService {

    @Autowired
    private UserService userService;
    @Autowired
    private CaregiverService caregiverService;

    @Value("${aliyun.sms.accessKeyId}")
    private String accessKeyId;  // 阿里云的 AccessKeyId

    @Value("${aliyun.sms.accessKeySecret}")
    private String accessKeySecret;  // 阿里云的 AccessKeySecret

    @Value("${aliyun.sms.signName}")
    private String signName;  // 阿里云的短信签名

    @Value("${aliyun.sms.templateCode}")
    private String templateCode;

    private static final Map<String , Object[]> verificationCodeMap = new ConcurrentHashMap<>();
    //验证码保存时间
    private static final long EXPIRATION_TIME = 5 * 60 * 1000;
    // 定义请求频率限制时间（单位：毫秒，1分钟）
    private static final long REQUEST_INTERVAL = 60 * 1000;

    private static final Map<String, Long> requestTimestampMap = new ConcurrentHashMap<>(); // 存储请求时间戳


    /**
     * 发送短信验证码
     * @param phoneNumber 手机号
     * @param verificationCode 生成的验证码
     * @return 发送结果，true表示发送成功，false表示失败
     */
    @Override
    public boolean sendSms(String phoneNumber, String verificationCode) {
        // 校验手机号格式是否正确
        if (phoneNumber == null || !isValidPhoneNumber(phoneNumber)) {
            throw new IllegalArgumentException("Invalid phone number format.");
        }

        // 校验是否超过请求频率
        if (!isRequestAllowed(phoneNumber)) {
            throw new IllegalArgumentException("You are requesting too frequently, please try again after 1 minute.");
        }

        try {
            // 设置阿里云短信服务的认证信息
            IClientProfile profile = DefaultProfile.getProfile("cn-hangzhou", accessKeyId, accessKeySecret);
            DefaultAcsClient client = new DefaultAcsClient(profile);  // 使用 DefaultAcsClient 来发送请求

            // 创建短信请求对象
            CommonRequest request = new CommonRequest();
            request.setMethod(com.aliyuncs.http.MethodType.POST);  // 设置请求方法为 POST
            request.setDomain("dysmsapi.aliyuncs.com");
            request.setVersion("2017-05-25");
            request.setAction("SendSms");

            // 设置请求参数
            request.putQueryParameter("PhoneNumbers", phoneNumber);
            request.putQueryParameter("SignName", signName);  // 添加短信签名
            request.putQueryParameter("TemplateCode", templateCode);  // 确保使用正确的模板 ID
            request.putQueryParameter("TemplateParam", "{\"code\":\"" + verificationCode + "\"}");  // 确保传递正确的验证码参数

            // 发送请求并接收响应
            CommonResponse response = client.getCommonResponse(request);  // 使用 getCommonResponse 发送请求并获取响应
            System.out.println(response.getData());

            // 将验证码存入 Map 中
            verificationCodeMap.put(phoneNumber, new Object[]{verificationCode,System.currentTimeMillis()});

            // 判断短信是否发送成功
            return response.getData().contains("\"Code\":\"OK\"");
        } catch (com.aliyuncs.exceptions.ServerException e) {
            // 服务器端异常处理
            System.out.println("Server Exception: " + e.getMessage());
            throw new RuntimeException("Server Exception: " + e.getMessage(), e);
        } catch (com.aliyuncs.exceptions.ClientException e) {
            // 客户端异常处理
            System.out.println("Client Exception: " + e.getMessage());
            throw new RuntimeException("Client Exception: " + e.getMessage(), e);
        } catch (Exception e) {
            // 其他未知异常处理
            System.out.println("Unexpected Exception: " + e.getMessage());
            throw new RuntimeException("Unexpected Exception: " + e.getMessage(), e);
        }
    }

    /**
     * 验证短信验证码
     */
    @Override
    public boolean verifySmsCode(UserDTO userDTO) {
        String loginIdAsString = StpUtil.getLoginIdAsString();
        LoginUserDTO loginUser = (LoginUserDTO) StpUtil.getSession().get(loginIdAsString);
        userDTO.setUserId(loginUser.getUserId());
        Integer userType = loginUser.getUserType();
        String phoneNumber = userDTO.getPhoneNumber();
        String verificationCode = userDTO.getVerificationCode();
        // 从 Map 中获取存储的验证码及时间戳
        Object[] storedData = verificationCodeMap.get(phoneNumber);

        // 判断验证码是否存在
        if (storedData == null) {
            return false;  // 没有找到验证码
        }

        // 获取存储的验证码和时间戳
        String storedCode = (String) storedData[0];
        long storedTimestamp = (long) storedData[1];

        // 判断验证码是否过期
        long currentTime = System.currentTimeMillis();
        if (currentTime - storedTimestamp > EXPIRATION_TIME) {
            verificationCodeMap.remove(phoneNumber);  // 超时后移除验证码
            return false;  // 验证码已过期
        }

        // 判断验证码是否匹配
        if (!storedCode.equals(verificationCode)) {
            return false;  // 验证码不匹配
        }

        // 如果验证码验证成功，删除数据库中的支付密码
        try {
            if (userType == 1) {
                // 调用 UserService 删除用户的支付密码
                userService.deleteUserPayPassword(phoneNumber);
            } else if (userType == 2) {
                caregiverService.deleteCaregiverPayPassword(phoneNumber);
            }
        } catch (Exception e) {
            // 如果删除失败，可以根据需要处理异常
            e.printStackTrace();
            return false;  // 删除失败返回 false
        }

        return true;  // 验证码验证成功并删除密码
    }

    private boolean isValidPhoneNumber(String phoneNumber) {
        return phoneNumber != null && phoneNumber.matches("^1[3-9]\\d{9}$");
    }

    // 限流：1分钟内只能请求一次验证码
    private boolean isRequestAllowed(String phoneNumber) {
        long currentTime = System.currentTimeMillis();
        Long lastRequestTime = requestTimestampMap.get(phoneNumber);

        if (lastRequestTime == null) {
            requestTimestampMap.put(phoneNumber, currentTime);
            return true;
        } else {
            if (currentTime - lastRequestTime < REQUEST_INTERVAL) {
                return false;
            } else {
                requestTimestampMap.put(phoneNumber, currentTime);
                return true;
            }
        }
    }

    // 定期清理过期验证码
    @Scheduled(fixedRate = 60 * 1000)
    public void cleanExpiredCodes() {
        long currentTime = System.currentTimeMillis();
        Iterator<Map.Entry<String, Object[]>> iterator = verificationCodeMap.entrySet().iterator();

        while (iterator.hasNext()) {
            Map.Entry<String, Object[]> entry = iterator.next();
            Object[] value = entry.getValue();
            long storedTimestamp = (long) value[1];

            if (currentTime - storedTimestamp > EXPIRATION_TIME) {
                iterator.remove();
            }
        }
    }
}