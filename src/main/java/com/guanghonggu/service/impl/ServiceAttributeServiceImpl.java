package com.guanghonggu.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.guanghonggu.dto.ServiceAttributeDTO;
import com.guanghonggu.entity.AttributeOptionColor;
import com.guanghonggu.entity.ServiceAttribute;
import com.guanghonggu.entity.ServiceAttributeOption;
import com.guanghonggu.mapper.AttributeOptionColorMapper;
import com.guanghonggu.mapper.ServiceAttributeOptionMapper;
import com.guanghonggu.service.ServiceAttributeService;
import com.guanghonggu.mapper.ServiceAttributeMapper;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
* <AUTHOR>
* @description 针对表【service_attribute】的数据库操作Service实现
* @createDate 2025-05-23 14:42:23
*/
@Service
public class ServiceAttributeServiceImpl extends ServiceImpl<ServiceAttributeMapper, ServiceAttribute>
    implements ServiceAttributeService{

    @Autowired
    private AttributeOptionColorMapper colorMapper;

    static List<Map<String, String>> COLOR_PALETTE = new ArrayList<>();

    static {
        Map<String, String> color1 = new HashMap<>();
        color1.put("textColor", "rgba(253, 145, 76, 1)");
        color1.put("areaColor", "rgba(252, 241, 234, 1)");
        COLOR_PALETTE.add(color1);

        Map<String, String> color2 = new HashMap<>();
        color2.put("textColor", "rgba(79, 191, 103, 1)");
        color2.put("areaColor", "rgba(240, 250, 242, 1)");
        COLOR_PALETTE.add(color2);

        Map<String, String> color3 = new HashMap<>();
        color3.put("textColor", "rgba(155, 81, 224, 1)");
        color3.put("areaColor", "rgba(245, 238, 252, 1)");
        COLOR_PALETTE.add(color3);

        Map<String, String> color4 = new HashMap<>();
        color4.put("textColor", "rgba(255, 102, 40, 1)");
        color4.put("areaColor", "rgba(255, 240, 234, 1)");
        COLOR_PALETTE.add(color4);

        Map<String, String> color5 = new HashMap<>();
        color5.put("textColor", "rgba(255, 207, 25, 1)");
        color5.put("areaColor", "rgba(255, 251, 232, 1)");
        COLOR_PALETTE.add(color5);

        Map<String, String> color6 = new HashMap<>();
        color6.put("textColor", "rgba(78, 216, 255, 1)");
        color6.put("areaColor", "rgba(232, 247, 252, 1)");
        COLOR_PALETTE.add(color6);
    }



    @Autowired
    private ServiceAttributeMapper attributeMapper;

    @Autowired
    private ServiceAttributeOptionMapper optionMapper;

    @Override
    public List<ServiceAttributeDTO> getAttributesByServiceType(String serviceTypeCode) {
        // 查询该服务类型下的所有属性
        List<ServiceAttribute> attributes = attributeMapper.selectByServiceType(serviceTypeCode);
        List<ServiceAttributeDTO> result = new ArrayList<>();

        for (ServiceAttribute attr : attributes) {
            ServiceAttributeDTO dto = new ServiceAttributeDTO();
            BeanUtils.copyProperties(attr, dto);

            List<ServiceAttributeOption> options = optionMapper.selectByAttributeKey(attr.getAttributeKey(), serviceTypeCode);

            List<ServiceAttributeDTO.OptionDTO> optionDTOs = getOptionDTOS(options);

            dto.setOption(optionDTOs);
            result.add(dto);
        }

        return result;
    }

    /**
     * 封装下单属性信息
     * @param options
     * @return
     */
    public List<ServiceAttributeDTO.OptionDTO> getOptionDTOS(List<ServiceAttributeOption> options) {
        List<ServiceAttributeDTO.OptionDTO> optionDTOs = new ArrayList<>();

        // 从数据库获取颜色配置
        List<AttributeOptionColor> colorList = colorMapper.selectByAttributeKey();
        if (colorList == null || colorList.isEmpty()) {
            colorList = colorMapper.selectDefaultColors(); // 默认颜色
        }

        for (int i = 0; i < options.size(); i++) {
            ServiceAttributeOption opt = options.get(i);
            AttributeOptionColor color = colorList.get(i % colorList.size());

            ServiceAttributeDTO.OptionDTO optionDTO = new ServiceAttributeDTO.OptionDTO();
            optionDTO.setId(opt.getId());
            optionDTO.setAttributeKey(opt.getAttributeKey());
            optionDTO.setValue(opt.getValue());
            optionDTO.setLabel(opt.getLabel());
            optionDTO.setPrice(opt.getPrice());
            optionDTO.setIsStartingPrice(opt.getIsStartingPrice());
            optionDTO.setTextColor(color.getTextColor());
            optionDTO.setAreaColor(color.getAreaColor());

            optionDTOs.add(optionDTO);
        }

        return optionDTOs;
    }

}




