package com.guanghonggu.service.impl;

import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.guanghonggu.dto.AddressDTO;
import com.guanghonggu.entity.Address;
import com.guanghonggu.mapper.AddressMapper;
import com.guanghonggu.service.AddressService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 地址业务逻辑实现类
 * 实现了用户地址的增删改查和一些扩展功能
 */
@Slf4j
@Service
public class AddressServiceImpl extends ServiceImpl<AddressMapper, Address> implements AddressService {

    @Autowired
    private AddressMapper addressMapper;

    // ========== 查询类 ==========

    /**
     * 根据地址id查询地址信息
     */
    @Override
    public Address getAddressId(AddressDTO addressDTO){
        LambdaQueryWrapper<Address> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Address::getAddressId, addressDTO.getAddressId());
        return addressMapper.selectOne(queryWrapper);
    }

    @Override
    public Page<Address> getAddressesByUserId(Long userId,Integer page,Integer size,Integer userType) { // 根据用户ID查询地址
        Page<Address> addressPage = new Page<>(page, size);
        LambdaQueryWrapper<Address> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Address::getUserId, userId)
                .eq(Address::getUserType, userType)
                .eq(Address::getStatus, 0)
                .orderByDesc(Address::getIsDefault)
                .orderByDesc(Address::getUpdateTime);
        return addressMapper.selectPage(addressPage, wrapper);
    }

    // ========== 新增与修改类 ==========

    @Override
    public int updateAddress(AddressDTO addressDTO) { // 更新地址
        if (addressDTO == null || addressDTO.getAddressId() == null) {
            return 0;
        }
        // 获取当前地址的原始数据
        Address detailedAddress = addressMapper.selectById(addressDTO.getAddressId());
        if (detailedAddress == null) {
            return 0;  // 如果地址不存在，则返回 0
        }

        // 将 addressDTO 中的数据复制到 Address 实体中
        Address address = new Address();
        BeanUtils.copyProperties(addressDTO, address);

        // 2. 判断是否处理默认地址逻辑
        if (addressDTO.getIsDefault() != null && addressDTO.getIsDefault() == 1) {
            Address dbAddress = addressMapper.selectById(addressDTO.getAddressId());
            if (dbAddress != null && dbAddress.getIsDefault() == 0) {
                LambdaUpdateWrapper<Address> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.eq(Address::getUserId, dbAddress.getUserId())  // 通过 userId 找到所有地址
                        .set(Address::getIsDefault, 0);  // 将其他地址设置为非默认
                addressMapper.update(null, updateWrapper);
                // 更新当前地址的 is_default 字段为 1
                dbAddress.setIsDefault(1);
                addressMapper.updateById(dbAddress);
            }
        }
        // 最后更新数据库中的地址
        return addressMapper.updateById(address);
    }

    @Override
    public int addAddress(AddressDTO addressDTO) {
        // 第一步：获取当前这个地址属于哪个用户
        Long userId = addressDTO.getUserId();
        if (userId == null) {
            userId = IdUtil.getSnowflake().nextId();  // 生成一个新的用户ID
            addressDTO.setUserId(userId);
        }

        // 第二步：查数据库，看看这个用户之前有没有添加过地址
        LambdaQueryWrapper<Address> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Address::getUserId, userId);
        List<Address> existingAddresses = addressMapper.selectList(queryWrapper);
        // 第三步：判断是否需要把新地址设为默认
        boolean noActiveAddress =
                existingAddresses == null
                        || existingAddresses.isEmpty()
                        || existingAddresses.stream().allMatch(a -> Integer.valueOf(1).equals(a.getStatus()));
        // 第三步：判断这个用户有没有地址
        if (noActiveAddress) {
            addressDTO.setIsDefault(1);  // 如果是第一次添加地址，设置为默认地址
        } else {
            if (addressDTO.getIsDefault() == null) {
                addressDTO.setIsDefault(0);  // 如果未设置默认地址，默认设置为非默认地址
            } else if (addressDTO.getIsDefault() == 1) {
                // 更新之前的默认地址为非默认
                LambdaUpdateWrapper<Address> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.eq(Address::getUserId, userId)
                        .eq(Address::getIsDefault, 1);
                Address resetDefault = new Address();
                resetDefault.setIsDefault(0);
                addressMapper.update(resetDefault, updateWrapper);
            }
        }

        // 第六步：将地址插入到数据库
        Address address = new Address();
        address.setStatus(0);
        BeanUtils.copyProperties(addressDTO, address);
        return addressMapper.insert(address);  // 返回插入成功的条数
    }


    @Override
    public boolean setDefaultAddress(AddressDTO addressDTO) { // 设置默认地址的方法，传入的参数是地址ID
        // 根据地址ID从数据库中查找这条地址记录
        Address address = addressMapper.selectById(addressDTO.getAddressId());
        // 如果查不到这个地址，说明地址不存在，返回false表示设置失败
        if (address == null) {
            return false;
        }
        // 获取这个地址所属的用户ID，方便后面把这个用户的所有默认地址取消掉
        Long userId = address.getUserId();
        // 创建一个更新条件构造器，用来指定我们要更新哪些数据
        LambdaUpdateWrapper<Address> resetWrapper = new LambdaUpdateWrapper<>();
        // 设置条件：user_id等于这个用户的ID，并且设置is_default为0（取消默认）
        resetWrapper.eq(Address::getUserId, userId)
                .set(Address::getIsDefault, 0);
        // 执行更新，把这个用户所有地址的is_default字段都设置为0（即全部取消默认）
        addressMapper.update(null, resetWrapper);
        // 创建一个新的Address对象，用来设置新的默认地址
        Address updateAddress = new Address();
        // 设置这个对象的地址ID，就是我们传进来的那个地址ID
        updateAddress.setAddressId(addressDTO.getAddressId());
        // 设置这个地址为默认地址（is_default = 1）
        updateAddress.setIsDefault(1);
        // 更新数据库中这条地址，把is_default字段改为1
        int result = addressMapper.updateById(updateAddress);
        // 所有操作都成功后，返回true，表示设置默认地址成功
        return result > 0;
    }

    // ========== 删除类 ==========

    @Override
    public int deleteAddress(AddressDTO addressDTO) { // 删除指定 ID 的地址
        // 第一步：根据地址ID去数据库查询出需要删除的地址对象
        Address deletedAddress = addressMapper.selectById(addressDTO.getAddressId());

        if (deletedAddress == null) return 0; // 如果地址不存在，则返回0
        //第二步执行删除操作
        LambdaUpdateWrapper<Address> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(Address::getAddressId, addressDTO.getAddressId())
                .set(Address::getStatus, 1)
                .set(Address::getIsDefault, 0);
        int result = addressMapper.update(null, updateWrapper);

        // 第三步：判断该删除的地址是否是默认地址
        if (deletedAddress.getIsDefault() != null && deletedAddress.getIsDefault() == 1) {
            // 第四步：说明用户删除了默认地址
            // 获取该地址所属用户ID
            Long userId = deletedAddress.getUserId();

            // 查询最近使用过的地址
           LambdaQueryWrapper<Address> wrapper = new LambdaQueryWrapper<>();
           wrapper.eq(Address::getUserId, userId)
                   .orderByDesc(Address::getRecentOrderTime)
                   .last("limit 1");
            Address recentAddress = addressMapper.selectOne(wrapper); // 执行查询

            // 第五步：如果找到了最近使用的地址，就把它设为新的默认地址
            if (recentAddress != null) {
                Address update = new Address(); // 新建一个空对象（只填我们要更新的字段）
                update.setAddressId(recentAddress.getAddressId()); // 设置要修改的地址ID
                update.setIsDefault(1); // 设置为默认地址（1代表是默认）
                addressMapper.updateById(update); // 执行更新，把该地址设为默认
            }
        }

        // 返回更新的记录数
        return result;
    }
}
