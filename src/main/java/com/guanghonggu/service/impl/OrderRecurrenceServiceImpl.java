package com.guanghonggu.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.guanghonggu.dto.OrderRecurrenceDTO;
import com.guanghonggu.dto.ResultDTO;
import com.guanghonggu.entity.Order;
import com.guanghonggu.entity.OrderRecurrence;
import com.guanghonggu.entity.Wallet;
import com.guanghonggu.entity.WalletTransaction;
import com.guanghonggu.mapper.OrderMapper;
import com.guanghonggu.mapper.WalletMapper;
import com.guanghonggu.mapper.WalletTransactionMapper;
import com.guanghonggu.service.OrderRecurrenceService;
import com.guanghonggu.mapper.OrderRecurrenceMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @description 针对表【order_recurrence(周期订单规则表)】的数据库操作Service实现
* @createDate 2025-08-18 16:13:16
*/
@Service
public class OrderRecurrenceServiceImpl extends ServiceImpl<OrderRecurrenceMapper, OrderRecurrence>
    implements OrderRecurrenceService{

    @Autowired
    private OrderRecurrenceMapper orderRecurrenceMapper;

    @Autowired
    private OrderMapper orderMapper;

    @Autowired
    private WalletMapper walletMapper;

    @Autowired
    private WalletTransactionMapper walletTransactionMapper;
    @Autowired
    private OrderServiceImpl orderService;


    @Override
    public ResultDTO<String> updateOrderRecurrence(OrderRecurrenceDTO orderRecurrenceDTO) {
        Integer editType = orderRecurrenceDTO.getEditType();
        Long orderId = orderRecurrenceDTO.getOrderId();
        LambdaQueryWrapper<OrderRecurrence> eq = new LambdaQueryWrapper<OrderRecurrence>()
                .eq(OrderRecurrence::getOrderId, orderId);
        // 查询周期规则
        OrderRecurrence recurrence = orderRecurrenceMapper.selectOne(eq);
        if (recurrence == null) {
            return ResultDTO.error("未找到周期规则");
        }

        OrderRecurrence orderRecurrence = new OrderRecurrence();
        orderRecurrence.setOrderId(orderRecurrenceDTO.getOrderId());

        // 获取next_service_time
        recurrence.setServiceTime(orderRecurrenceDTO.getServiceTime());
        LocalDateTime localDateTime = calculateNextServiceTime(LocalDateTime.now(), recurrence);
        orderRecurrence.setNextServiceTime(localDateTime);
        if (editType != 1) {
            // 修改service_time
            orderRecurrence.setServiceTime(orderRecurrenceDTO.getServiceTime());
        }
        orderRecurrenceMapper.update(orderRecurrence, eq);

        return ResultDTO.notDataSuccess("修改成功");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultDTO<String> cancelOrderRecurrence(OrderRecurrenceDTO orderRecurrenceDTO) {
        Integer editType = orderRecurrenceDTO.getEditType();
        Long orderId = orderRecurrenceDTO.getOrderId();
        Order order = orderMapper.selectById(orderId);
        // 查询周期规则
        OrderRecurrence recurrence = orderRecurrenceMapper.selectOne(
                new LambdaQueryWrapper<OrderRecurrence>().eq(OrderRecurrence::getOrderId, orderId)
        );
        if (recurrence == null) {
            return ResultDTO.error("未找到周期规则");
        }

        // 取消下一次：修改next_service_time，修改recurrence_count
        if (editType == 1) {
            // 取消下一次预约
            LocalDateTime nextServiceTime = recurrence.getNextServiceTime();
            if (nextServiceTime == null) {
                return ResultDTO.error("没有下一次可取消的服务");
            }

            // 减少周期次数
            int newCount = (recurrence.getRecurrenceCount() != null && recurrence.getRecurrenceCount() > 0)
                    ? recurrence.getRecurrenceCount() - 1
                    : 0;
            recurrence.setRecurrenceCount(newCount);

            // 计算下下次的时间（根据规则生成）
            LocalDateTime nextNextTime = calculateNextServiceTime(LocalDateTime.now(), recurrence);
            recurrence.setNextServiceTime(nextNextTime);
            orderRecurrenceMapper.updateById(recurrence);
            int newStatus = 2;
            if (newCount <= 0) {
                // 没有周期次数了
                newStatus = 6;
            }
            orderService.doRefund(order, recurrence.getSingleServiceAmount(), order.getUserId(), newStatus, "用户取消周期预约", 1);

            return ResultDTO.notDataSuccess("已取消下一次预约");
        } else if (editType == 2) {
            BigDecimal refundAmount = BigDecimal.ZERO;
            if (recurrence.getTotalServiceCount() != null
                    && recurrence.getUsedCount() != null
                    && recurrence.getSingleServiceAmount() != null) {

                int remainingCount = recurrence.getTotalServiceCount() - recurrence.getUsedCount();

                // 如果剩余次数 > 0，才计算退款
                if (remainingCount > 0) {
                    refundAmount = recurrence.getSingleServiceAmount()
                            .multiply(BigDecimal.valueOf(remainingCount));
                }
            }
            orderService.doRefund(order, refundAmount , order.getUserId(), 6, "用户取消周期预约", 1);

            return ResultDTO.notDataSuccess("已取消所有预约");
        } else {
            return ResultDTO.error("未知的编辑类型");
        }
    }
    /**
     * 计算下一次服务时间
     * @param current 当前时间
     * @param recurrence 周期规则
     * @return 下一次服务时间
     */
    public LocalDateTime calculateNextServiceTime(LocalDateTime current, OrderRecurrence recurrence) {
        // 当前星期几（1=周一，7=周日）
        int today = current.getDayOfWeek().getValue();

        // 解析规则
        String recurrenceRule = recurrence.getRecurrenceRule();
        List<Integer> ruleDays = Arrays.stream(recurrenceRule.split(","))
                .map(String::trim)
                .map(Integer::parseInt)
                .sorted()
                .collect(Collectors.toList());

        // 找到下一个服务日
        Integer nextDay = null;
        for (Integer day : ruleDays) {
            if (day > today) {
                nextDay = day;
                break;
            }
        }
        // 如果没找到，说明本周没有了，下周第一个
        if (nextDay == null) {
            nextDay = ruleDays.get(0);
        }

        // 计算需要加的天数
        int daysToAdd = nextDay - today;
        if (daysToAdd <= 0) {
            daysToAdd += 7;
        }

        // 拼接成新的 LocalDateTime（日期 + 服务时间）
        LocalTime serviceTime = recurrence.getServiceTime(); // 你表里存的 time 类型，对应 java LocalTime
        return LocalDateTime.of(current.toLocalDate().plusDays(daysToAdd), serviceTime);
    }


    /**
     * 给阿姨结算单次金额
     */
    @Transactional(rollbackFor = Exception.class)
    public void settleOrder(OrderRecurrence recurrence) {
        BigDecimal amount = recurrence.getSingleServiceAmount();

        // 调用钱包交易服务，给阿姨加钱
        addIncome(recurrence.getOrderId(), amount);

        // 更新已结算次数
        recurrence.setUsedCount(recurrence.getUsedCount() + 1);
        orderRecurrenceMapper.updateById(recurrence);
    }

    public void addIncome(Long orderId, BigDecimal amount) {
        Order order = orderMapper.selectById(orderId);
        Long caregiversId = order.getCaregiversId();
        Wallet wallet = walletMapper.selectWalletByUserForUpdate(caregiversId, 2);
        Long walletId = wallet.getWalletId();
        WalletTransaction walletTransaction = new WalletTransaction();
        walletTransaction.setWalletId(walletId);
        walletTransaction.setTransactionType(5);
        walletTransaction.setAmount(amount);
        walletTransaction.setTransactionTime(new Date());
        walletTransaction.setOutTradeNo(order.getOrderNumber());
        walletTransaction.setStatus(1);
        walletTransaction.setPaymentChannel(3);
        walletTransactionMapper.insert(walletTransaction);

        // TODO: 同时更新阿姨钱包余额
        wallet.setBalance(wallet.getBalance().add(amount));
        walletMapper.updateById(wallet);
    }
}




