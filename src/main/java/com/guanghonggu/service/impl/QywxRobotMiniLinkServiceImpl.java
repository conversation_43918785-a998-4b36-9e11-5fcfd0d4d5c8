//package com.guanghonggu.service.impl;
//
//import com.fasterxml.jackson.databind.JsonNode;
//import com.fasterxml.jackson.databind.ObjectMapper;
//import com.guanghonggu.service.QywxRobotMiniLinkService;
//import okhttp3.*;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.stereotype.Service;
//
//import java.io.UnsupportedEncodingException;
//import java.net.URLEncoder;
//import java.nio.charset.StandardCharsets;
//
//
//@Service
//public class QywxRobotMiniLinkServiceImpl implements QywxRobotMiniLinkService {
//    @Value("${wx.mini-programs.app-id}")
//    private String appId;
//
//    @Value("${wx.mini-programs.app-secret}")
//    private String appSecret;
//
//    private final OkHttpClient client = new OkHttpClient();
//    private final ObjectMapper mapper = new ObjectMapper();
//
//    @Override
//    public String sendMiniLinkByRoBot(String robotWebhook, String pagepath, String title, Integer expireDays) throws Exception {
//        if (robotWebhook == null || robotWebhook.isEmpty()) {
//            throw new IllegalArgumentException("机器人 webhook 不能为空");
//        }
//        if (pagepath == null || pagepath.isEmpty()) {
//            throw new IllegalArgumentException("小程序页面路径不能为空");
//        }
//        if (title == null || title.isEmpty()) {
//            throw new IllegalArgumentException("标题不能为空");
//        }
//        if (expireDays == null){
//            expireDays = 30;
//        }
//
//        String miniAccessToken = getMiniAccessToken();
//        String urlLink = generateUrlLink(miniAccessToken, pagepath, expireDays);
//        String md = "**" + title + "**\n\n" +
//                "点击打开小程序：<" + urlLink + ">\n\n" +
//                "如无法直接打开，请复制到微信中或扫码";
//        return sendMarkdownMessage(robotWebhook, md);
//    }
//
//    public String getMiniAccessToken() throws Exception {
//        String url = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential" +
//                "&appid=" + urlEncode(appId) + "&secret=" + urlEncode(appSecret);
//
//        Request req = new Request.Builder()
//                .url(url)
//                .get()
//                .build();
//        try (Response resp = client.newCall(req).execute()) {
//            if (!resp.isSuccessful()) throw new RuntimeException("mini_access_token http" + resp.code());
//            JsonNode json = mapper.readTree(resp.body().string());
//            if (json.has("access_token")) return json.get("access_token").asText();
//            throw new RuntimeException("mini_access_token json " + json.toString());
//        }
//    }
//
//    public String generateUrlLink(String miniAccessToken, String pagepath, int expireDays) throws Exception {
//        String url = "https://api.weixin.qq.com/wxa/generate_urllink?access_token=" + miniAccessToken;
//
//        String body = "{\n" +
//                "    \"path\": \"" + quote(pagepath) + "\",\n" +
//                "    \"is_expire\": true,\n" +
//                "    \"expire_type\": 1,\n" +
//                "    \"expire_time\": " + expireDays + ",\n" +
//                "}";
//
//        Request req = new Request.Builder()
//                .url(url)
//                .post(RequestBody.create(body, MediaType.parse("application/json")))
//                .build();
//
//        try (Response resp = client.newCall(req).execute()) {
//            if (!resp.isSuccessful()) throw new RuntimeException("urllink http" + resp.code());
//                JsonNode json = mapper.readTree(resp.body().string());
//            if (json.has("url_link")) return json.get("url_link").asText();
//                throw new RuntimeException("urllink json " + json.toString());
//        }
//    }
//
//    /** 企业微信群机器人：发送 markdown */
//    private String sendMarkdownMessage(String webhookUrl, String markdown) throws Exception {
//        String body = "{ \"msgtype\": \"markdown\", \"markdown\": { \"content\": " + quote(markdown) + " } }";
//        Request req = new Request.Builder()
//                .url(webhookUrl)
//                .post(RequestBody.create(body, MediaType.parse("application/json")))
//                .build();
//        try (Response resp = client.newCall(req).execute()) {
//            if (!resp.isSuccessful()) throw new RuntimeException("robot http " + resp.code());
//            return resp.body().string();
//        }
//    }
//
//    private static String urlEncode(String s) throws UnsupportedEncodingException {
//        return URLEncoder.encode(s, StandardCharsets.UTF_8.name());
//    }
//    private static String quote(String s) {
//        return "\"" + s.replace("\\", "\\\\").replace("\"", "\\\"") + "\"";
//    }
//}
