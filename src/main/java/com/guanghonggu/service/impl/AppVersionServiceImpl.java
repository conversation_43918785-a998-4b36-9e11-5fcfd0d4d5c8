package com.guanghonggu.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.guanghonggu.dto.ResultDTO;
import com.guanghonggu.entity.AppVersion;
import com.guanghonggu.service.AppVersionService;
import com.guanghonggu.mapper.AppVersionMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description 针对表【app_version】的数据库操作Service实现
 * @createDate 2025-08-01 10:52:54
 */
@Service
public class AppVersionServiceImpl extends ServiceImpl<AppVersionMapper, AppVersion>
        implements AppVersionService {

    @Autowired
    private AppVersionMapper appVersionMapper;

    @Override
    public ResultDTO<AppVersion> getLatestVersion(String platform,Integer appType) {
        LambdaQueryWrapper<AppVersion> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AppVersion::getAppType, appType)
                .eq(AppVersion::getPlatform, platform)
                .orderByDesc(AppVersion::getVersionCode)
                .last("LIMIT 1");  // 只取最新一条记录

        AppVersion latestVersion = appVersionMapper.selectOne(queryWrapper);

        if (latestVersion == null) {
            return ResultDTO.error("未找到最新版本信息");
        }

        return ResultDTO.success("查询成功", latestVersion);
    }

}




