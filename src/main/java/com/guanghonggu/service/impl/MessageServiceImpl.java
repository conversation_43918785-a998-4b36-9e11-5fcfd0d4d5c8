package com.guanghonggu.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.guanghonggu.dto.LoginUserDTO;
import com.guanghonggu.dto.MessageDTO;
import com.guanghonggu.dto.ResultDTO;
import com.guanghonggu.entity.*;
import com.guanghonggu.mapper.*;
import com.guanghonggu.service.MessageService;
import org.springframework.beans.factory.annotation.Autowired;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Transactional
public class MessageServiceImpl implements MessageService {

    @Autowired
    private MessageMapper messageMapper;

    @Autowired
    private UserMessageMapper userMessageMapper;


    // 获取系统消息提醒
    @Override
    public ResultDTO<IPage<MessageDTO>> getMessages(Integer currentPage, Integer pageSize) {
        String loginIdAsString = StpUtil.getLoginIdAsString();
        LoginUserDTO loginUser = (LoginUserDTO) StpUtil.getSession().get(loginIdAsString);
        Long userId = loginUser.getUserId();
        Integer userType = loginUser.getUserType();

        Page<MessageDTO> page = new Page<>(currentPage, pageSize);
        IPage<MessageDTO> messagePage = messageMapper.selectMessage(page, userId, userType);

        return ResultDTO.success("查询成功", messagePage);
    }



    @Override
    public ResultDTO<String> updateStatus(Long id) {

        LambdaQueryWrapper<UserMessage> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserMessage::getId, id);
        UserMessage userMessage = new UserMessage();
        userMessage.setId(id);
        userMessage.setIsRead(1);
        userMessageMapper.updateById(userMessage);

        return ResultDTO.notDataSuccess("修改成功");
    }

}
