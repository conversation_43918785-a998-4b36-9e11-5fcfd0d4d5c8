package com.guanghonggu.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.guanghonggu.entity.ChatbotConversation;
import com.guanghonggu.service.ChatbotConversationService;
import com.guanghonggu.mapper.ChatbotConversationMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【chatbot_conversation】的数据库操作Service实现
* @createDate 2025-08-27 15:25:18
*/
@Service
public class ChatbotConversationServiceImpl extends ServiceImpl<ChatbotConversationMapper, ChatbotConversation>
    implements ChatbotConversationService{

}




