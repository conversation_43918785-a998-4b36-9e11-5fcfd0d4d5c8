package com.guanghonggu.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.guanghonggu.entity.GiftCouponProbability;
import com.guanghonggu.service.GiftCouponProbabilityService;
import com.guanghonggu.mapper.GiftCouponProbabilityMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【gift_coupon_probability】的数据库操作Service实现
* @createDate 2025-07-25 16:35:16
*/
@Service
public class GiftCouponProbabilityServiceImpl extends ServiceImpl<GiftCouponProbabilityMapper, GiftCouponProbability>
    implements GiftCouponProbabilityService{

}




