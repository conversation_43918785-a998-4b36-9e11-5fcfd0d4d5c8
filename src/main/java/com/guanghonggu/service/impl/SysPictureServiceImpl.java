package com.guanghonggu.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.guanghonggu.dto.ResultDTO;
import com.guanghonggu.dto.SysPictureDTO;
import com.guanghonggu.entity.SysPicture;
import com.guanghonggu.mapper.dto.SysPictureDTOMapper;
import com.guanghonggu.service.SysPictureService;
import com.guanghonggu.mapper.SysPictureMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【sys_picture】的数据库操作Service实现
* @createDate 2025-05-12 11:01:20
*/
@Service
public class SysPictureServiceImpl extends ServiceImpl<SysPictureMapper, SysPicture>
    implements SysPictureService{

    @Autowired
    private SysPictureMapper sysPictureMapper;

    @Override
    public ResultDTO<List<SysPictureDTO>> listPictures(Integer pictureType) {
        LambdaQueryWrapper<SysPicture> queryWrapper = new LambdaQueryWrapper<>();

        // 可选：按类型筛选
        if (pictureType != null) {
            queryWrapper.eq(SysPicture::getPictureType, pictureType);
        }

        List<SysPicture> pictures = sysPictureMapper.selectList(queryWrapper);
        List<SysPictureDTO> sysPictureDTOList = SysPictureDTOMapper.INSTANCE.toSysPictureDTOList(pictures);

        return ResultDTO.success("查询成功", sysPictureDTOList);
    }
}




