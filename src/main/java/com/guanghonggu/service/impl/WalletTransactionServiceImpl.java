package com.guanghonggu.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.guanghonggu.dto.LoginUserDTO;
import com.guanghonggu.dto.ResultDTO;
import com.guanghonggu.dto.WalletTransactionDTO;
import com.guanghonggu.entity.Wallet;
import com.guanghonggu.entity.WalletTransaction;
import com.guanghonggu.mapper.WalletMapper;
import com.guanghonggu.mapper.dto.WalletTransactionDTOMapper;
import com.guanghonggu.service.WalletTransactionService;
import com.guanghonggu.mapper.WalletTransactionMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【wallet_transaction(钱包交易记录表)】的数据库操作Service实现
 * @createDate 2025-04-27 10:56:56
 */
@Service
public class WalletTransactionServiceImpl extends ServiceImpl<WalletTransactionMapper, WalletTransaction>
        implements WalletTransactionService {

    @Autowired
    private WalletTransactionMapper walletTransactionMapper;

    @Autowired
    private WalletMapper walletMapper;

    @Override
    public ResultDTO<IPage<WalletTransactionDTO>> listWalletTransaction(WalletTransactionDTO walletTransactionDTO) {
        try {
            // 获取当前登录用户信息
            String loginIdAsString = StpUtil.getLoginIdAsString();
            LoginUserDTO loginInfo = (LoginUserDTO) StpUtil.getSession().get(loginIdAsString);
            Long userId = loginInfo.getUserId();
            Integer userType = loginInfo.getUserType();

            // 查询钱包ID
            LambdaQueryWrapper<Wallet> walletQuery = new LambdaQueryWrapper<>();
            walletQuery.select(Wallet::getWalletId)
                    .eq(Wallet::getUserId, userId)
                    .eq(Wallet::getUserType, userType);

            Wallet wallet = walletMapper.selectOne(walletQuery);
            if (wallet == null) {
                return ResultDTO.error("未找到对应的钱包信息");
            }
            Long walletId = wallet.getWalletId();

            // 分页参数设置
            int currentPage = walletTransactionDTO.getCurrentPage() != null ? walletTransactionDTO.getCurrentPage() : 1;
            int pageSize = walletTransactionDTO.getPageSize() != null ? walletTransactionDTO.getPageSize() : 10;

            // 构造查询条件
            LambdaQueryWrapper<WalletTransaction> wrapper = new LambdaQueryWrapper<>();
            wrapper.select(
                            WalletTransaction::getTransactionId,
                            WalletTransaction::getWalletId,
                            WalletTransaction::getTransactionType,
                            WalletTransaction::getAmount,
                            WalletTransaction::getTransactionTime,
                            WalletTransaction::getStatus
                    ).eq(WalletTransaction::getWalletId, walletId)
                    .eq(WalletTransaction::getStatus, 1);

            // 按交易类型过滤（非0才过滤）
            Integer transactionType = walletTransactionDTO.getTransactionType();
            if (transactionType != null && transactionType != 0) {
                wrapper.eq(WalletTransaction::getTransactionType, transactionType);
            }

            // 时间筛选处理（如果传入了时间范围）
            Date startDate = walletTransactionDTO.getStartTime();
            Date endDate = walletTransactionDTO.getEndTime();

            if (startDate != null) {
                LocalDate startLocal = startDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
                LocalDateTime startDateTime = startLocal.atStartOfDay();
                Date finalStartTime = Date.from(startDateTime.atZone(ZoneId.systemDefault()).toInstant());

                LocalDate endLocal = (endDate != null)
                        ? endDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate()
                        : LocalDate.now();
                LocalDateTime endDateTime = endLocal.atTime(LocalTime.MAX);
                Date finalEndTime = Date.from(endDateTime.atZone(ZoneId.systemDefault()).toInstant());

                wrapper.between(WalletTransaction::getCreateTime, finalStartTime, finalEndTime);
            }

            // 按照修改时间倒序排列
            wrapper.orderByDesc(WalletTransaction::getUpdateTime);  // 添加倒序排序

            // 执行分页查询
            Page<WalletTransaction> page = new Page<>(currentPage, pageSize);
            IPage<WalletTransaction> pageResult = walletTransactionMapper.selectPage(page, wrapper);

            // DTO 转换
            List<WalletTransactionDTO> dtoList = WalletTransactionDTOMapper.INSTANCE.toWalletTransactionDTO(pageResult.getRecords());

            // 构建 DTO 分页对象
            Page<WalletTransactionDTO> resultPage = new Page<>();
            resultPage.setCurrent(pageResult.getCurrent());
            resultPage.setSize(pageResult.getSize());
            resultPage.setTotal(pageResult.getTotal());
            resultPage.setPages(pageResult.getPages());
            resultPage.setRecords(dtoList);

            return ResultDTO.success("查询成功", resultPage);
        } catch (Exception e) {
            log.error("获取交易记录列表失败", e);
            return ResultDTO.error("获取交易记录列表失败");
        }
    }

}




