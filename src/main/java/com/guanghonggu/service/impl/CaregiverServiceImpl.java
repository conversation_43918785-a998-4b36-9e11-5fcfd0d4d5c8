package com.guanghonggu.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.guanghonggu.constant.CommonConstants;
import com.guanghonggu.dto.CaregiverDTO;
import com.guanghonggu.dto.LoginDTO;
import com.guanghonggu.dto.LoginUserDTO;
import com.guanghonggu.dto.MobileDTO;
import com.guanghonggu.dto.PhoneLoginDTO;
import com.guanghonggu.dto.ResultDTO;
import com.guanghonggu.dto.WxAccessTokenResponse;
import com.guanghonggu.dto.WxUserInfo;
import com.guanghonggu.entity.Caregiver;
import com.guanghonggu.entity.Location;
import com.guanghonggu.entity.Order;
import com.guanghonggu.entity.SysPicture;
import com.guanghonggu.entity.User;
import com.guanghonggu.entity.Wallet;
import com.guanghonggu.mapper.*;
import com.guanghonggu.mapper.LocationMapper;
import com.guanghonggu.mapper.OrderMapper;
import com.guanghonggu.service.AliyunDypnsService;
import com.guanghonggu.service.CaregiverService;
import com.guanghonggu.service.SmsService;
import com.guanghonggu.strategy.pay.strategy.PayQueryHandler;
import com.guanghonggu.util.AliyunOssUtil;
import com.guanghonggu.util.MD5Utils;
import com.guanghonggu.util.WXPhoneDecryptor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.Date;

import static com.guanghonggu.util.MD5Utils.verify;


/**
 * <AUTHOR>
 * @description 针对表【caregiver(阿姨表)】的数据库操作Service实现
 * @createDate 2025-04-27 10:56:56
 */
@Slf4j
@Service
public class CaregiverServiceImpl extends ServiceImpl<CaregiverMapper, Caregiver>
        implements CaregiverService {

    @Value("${wx.app.appId}")
    private String appid;

    @Value("${wx.app.appSecret}")
    private String appSecret;

    @Autowired
    private CaregiverMapper caregiverMapper;

    @Autowired
    private AliyunOssUtil aliyunOssUtil;

    @Autowired
    private LocationMapper locationMapper;
    @Autowired
    private OrderMapper orderMapper;
    @Autowired
    private UserMapper userMapper;

    @Autowired
    private AliyunDypnsService aliyunDypnsService;

    @Autowired
    private WalletMapper walletMapper;

    @Autowired
    private SysPictureMapper sysPictureMapper;

    @Autowired
    private PayQueryHandler payQueryHandler;

    @Autowired
    private SmsService smsService;

    @Override
    public Caregiver selectByPhone(String phoneNumber) {
        LambdaQueryWrapper<Caregiver> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Caregiver::getPhoneNumber, phoneNumber);

        return caregiverMapper.selectOne(wrapper);
    }

    @Override
    public Caregiver selectByOpenId(String openid) {

        LambdaQueryWrapper<Caregiver> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Caregiver::getWechatOpenid, openid);

        Caregiver caregiver = caregiverMapper.selectOne(wrapper);
        return caregiver;
    }

    /**
     * 根据阿姨id查询阿姨信息
     */
    @Override
    public CaregiverDTO getCaregiver(CaregiverDTO caregiverDTO) {
        LambdaQueryWrapper<Caregiver> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Caregiver::getCaregiverId, caregiverDTO.getCaregiverId());
        Caregiver caregiver = caregiverMapper.selectOne(queryWrapper);


        // 将 Caregiver 的其他字段设置到 CaregiverDTO 中
        caregiverDTO.setCaregiverId(caregiver.getCaregiverId());
        caregiverDTO.setPhoneNumber(caregiver.getPhoneNumber());
        if (StringUtils.isNotBlank(caregiver.getWechatOpenid())) {
            caregiverDTO.setWechatOpenid(caregiver.getWechatOpenid());
        } else {
            caregiverDTO.setWechatOpenid("");
        }
        caregiverDTO.setStatus(caregiver.getStatus());
        caregiverDTO.setName(caregiver.getName());
        caregiverDTO.setAge(caregiver.getAge());
        caregiverDTO.setAvatar(caregiver.getAvatar());
        caregiverDTO.setGender(caregiver.getGender());
        caregiverDTO.setIdCardNumber(caregiver.getIdCardNumber());
        caregiverDTO.setIdCardFront(caregiver.getIdCardFront());
        caregiverDTO.setIdCardBack(caregiver.getIdCardBack());
        caregiverDTO.setHealthCertificate(caregiver.getHealthCertificate());
        caregiverDTO.setExamStatus(caregiver.getExamStatus());
        caregiverDTO.setIdCardVerification(caregiver.getIdCardVerification());
        caregiverDTO.setHealthCertificateVerification(caregiver.getHealthCertificateVerification());
        caregiverDTO.setRegistrationTime(caregiver.getRegistrationTime());
        caregiverDTO.setUpdateTime(caregiver.getUpdateTime());
        caregiverDTO.setPayPassword(caregiver.getPayPassword());
        caregiverDTO.setAvgEvaluationScore(caregiver.getAvgEvaluationScore());

        // 根据 caregiverId 获取对应的用户位置（经纬度）
        QueryWrapper<Location> locationQueryWrapper = new QueryWrapper<>();
        locationQueryWrapper.eq("caregiver_id", caregiver.getCaregiverId());
        Location location = locationMapper.selectOne(locationQueryWrapper);

        // 查询阿姨订单数量
        LambdaQueryWrapper<Order> orderLambdaQueryWrapper = new LambdaQueryWrapper<>();
        orderLambdaQueryWrapper.eq(Order::getCaregiversId, caregiver.getCaregiverId())
                .eq(Order::getStatus, 8);
        Long orderCount = orderMapper.selectCount(orderLambdaQueryWrapper);
        caregiverDTO.setOrderCount(orderCount);

        // 如果找到 location 数据，将经纬度设置到 caregiverDTO 中
        if (location != null) {
            caregiverDTO.setLongitude(location.getLongitude());  // 设置经度
            caregiverDTO.setLatitude(location.getLatitude());  // 设置纬度
        }

        return caregiverDTO;  // 返回包含经纬度的 caregiverDTO
    }


    @Override
    public ResultDTO<String> updateCaregiverExamStatus() {
        String loginIdAsString = StpUtil.getLoginIdAsString();
        LoginUserDTO loginInfo = (LoginUserDTO) StpUtil.getSession().get(loginIdAsString);
        Long userId = loginInfo.getUserId();
        Caregiver caregiver = new Caregiver();
        caregiver.setCaregiverId(userId);
        caregiver.setExamStatus(3); // 已考试
        int res = caregiverMapper.updateById(caregiver);

        if (res < 1) {
            return ResultDTO.notDataSuccess("更新成功");
        }
        return ResultDTO.error("更新失败");
    }

    @Override
    public ResultDTO<String> uploadCaregiverInformation(CaregiverDTO dto) {
        String loginIdAsString = StpUtil.getLoginIdAsString();
        LoginUserDTO loginInfo = (LoginUserDTO) StpUtil.getSession().get(loginIdAsString);
        Long userId = loginInfo.getUserId();
        try {
            // TODO 校验身份信息
            Integer age = dto.getAge();
            Integer gender = dto.getGender();
            String name = dto.getName();

            MultipartFile idCardFrontImage = dto.getIdCardFrontImage();
            if (idCardFrontImage == null || idCardFrontImage.isEmpty()) {
                return ResultDTO.error("请上传身份证正面照片");
            }
            MultipartFile idCardBackImage = dto.getIdCardBackImage();
            if (idCardBackImage == null || idCardBackImage.isEmpty()) {
                return ResultDTO.error("请上传身份证反面照片");
            }
            MultipartFile healthCertificateImage = dto.getHealthCertificateImage();
            if (healthCertificateImage == null || healthCertificateImage.isEmpty()) {
                return ResultDTO.error("请上传健康证照片");
            }
            // 上传OSS
            String idCardFrontUrl = aliyunOssUtil.upload(idCardFrontImage, CommonConstants.PICTURE_URL_CAREGIVER_QUALIFICATION + userId);
            String idCardBackUrl = aliyunOssUtil.upload(idCardBackImage, CommonConstants.PICTURE_URL_CAREGIVER_QUALIFICATION + userId);
            String healthCertificateImageUrl = aliyunOssUtil.upload(healthCertificateImage, CommonConstants.PICTURE_URL_CAREGIVER_QUALIFICATION + userId);
            Caregiver caregiver = new Caregiver();
            caregiver.setCaregiverId(userId);
            caregiver.setName(name);
            caregiver.setGender(gender);
            caregiver.setAge(age);
            caregiver.setIdCardFront(idCardFrontUrl);
            caregiver.setIdCardBack(idCardBackUrl);
            caregiver.setHealthCertificate(healthCertificateImageUrl);
            caregiver.setIdCardVerification(2);
            caregiver.setHealthCertificateVerification(2);
            caregiverMapper.updateById(caregiver);

        } catch (IOException e) {
            log.error("上传身份证失败");
            throw new RuntimeException(e);
        }
        return ResultDTO.notDataSuccess("提交成功");

    }

    @Override
    public ResultDTO<Long> getPassword(CaregiverDTO caregiverDTO) {
        // 从登录会话中获取用户ID和用户类型
        String loginIdAsString = StpUtil.getLoginIdAsString(); // 获取登录ID
        LoginUserDTO loginUser = (LoginUserDTO) StpUtil.getSession().get(loginIdAsString); // 获取登录用户信息

        if (loginUser == null) {
            return ResultDTO.error("未找到登录用户信息");
        }

        Long userId = loginUser.getUserId();  // 获取用户ID
        Integer userType = loginUser.getUserType();  // 获取用户类型（1 = 普通用户, 2 = 阿姨）

        if (userId == null) {
            return ResultDTO.error("用户ID不能为空");
        }

        // 定义返回结果
        User user = null;
        Caregiver caregiver = null;

        // 根据 userType 来选择查询不同的表
        if (userType == 1) {
            // 查询 User 表
            user = userMapper.selectById(userId);
        } else if (userType == 2) {
            // 查询 Caregiver 表
            caregiver = caregiverMapper.selectById(userId);
        } else {
            return ResultDTO.error("无效的用户类型");
        }

        // 检查查询结果是否为空
        if (userType == 1 && user == null) {
            return ResultDTO.error("用户不存在");
        } else if (userType == 2 && caregiver == null) {
            return ResultDTO.error("阿姨不存在");
        }

        // 判断密码是否为空
        if (userType == 1 && user.getPayPassword() != null && !user.getPayPassword().isEmpty()) {
            return ResultDTO.success("查询成功", 1L); // 有密码
        } else if (userType == 2 && caregiver.getPayPassword() != null && !caregiver.getPayPassword().isEmpty()) {
            return ResultDTO.success("查询成功", 1L); // 有密码
        } else {
            return ResultDTO.success("查询成功", 0L); // 无密码
        }
    }

    @Override
    public int setPayPassword(CaregiverDTO caregiverDTO) {
        String loginIdAsString = StpUtil.getLoginIdAsString();
        LoginUserDTO loginUser = (LoginUserDTO) StpUtil.getSession().get(loginIdAsString);
        caregiverDTO.setCaregiverId(loginUser.getUserId());
        Integer userType = loginUser.getUserType();
        // 使用工具类加密密码（不可逆）
        String encrypted = MD5Utils.generate(caregiverDTO.getPayPassword());

        if (userType == 1) {
            // 构造只包含 userId 和加密密码的对象
            User user = new User();
            user.setUserId(caregiverDTO.getCaregiverId());
            user.setPayPassword(encrypted);

            // 更新数据库中该用户的 payPassword 字段
            return userMapper.updateById(user);
        } else if (userType == 2) {
            Caregiver caregiver = new Caregiver();
            caregiver.setCaregiverId(caregiverDTO.getCaregiverId());
            caregiver.setPayPassword(encrypted);
            return caregiverMapper.updateById(caregiver);
        }
        return 1;
    }

    /**
     * 修改支付密码（先验证旧密码是否正确，再设置新密码）
     */
    @Override
    public int updatePayPassword(CaregiverDTO caregiverDTO) {
        try {
            // 获取当前登录用户的 ID 和类型
            String loginIdAsString = StpUtil.getLoginIdAsString();
            LoginUserDTO loginUser = (LoginUserDTO) StpUtil.getSession().get(loginIdAsString);
            caregiverDTO.setCaregiverId(loginUser.getUserId());  // 设置当前用户的 ID
            Integer userType = loginUser.getUserType();  // 获取当前用户的类型

            // 1. 校验新支付密码格式：必须是6位数字
            if (!caregiverDTO.getNewPassword().matches("^\\d{6}$")) {
                throw new RuntimeException("支付密码必须是6位数字");
            }

            // 2. 获取数据库中存储的支付密码
            String oldPassword = caregiverDTO.getOldPassword();

            // 查询用户的支付密码（根据用户ID）
            if (userType == 1) {
                User existingUser = userMapper.selectById(caregiverDTO.getCaregiverId());  // 查询普通用户
                if (existingUser == null) {
                    throw new RuntimeException("用户不存在");
                }

                // 3. 校验旧支付密码（数据库存储的是加密后的密码，直接对比加密后的值）
                if (!verify(oldPassword, existingUser.getPayPassword())) {
                    throw new RuntimeException("旧支付密码不正确");
                }

                // 对新支付密码进行加密
                String encryptedNewPassword = MD5Utils.generate(caregiverDTO.getNewPassword());  // 加密新的支付密码
                existingUser.setPayPassword(encryptedNewPassword);  // 设置新的支付密码

                // 4. 更新数据库中的支付密码
                return userMapper.updateById(existingUser);

            } else if (userType == 2) {
                Caregiver existingCaregiver = caregiverMapper.selectById(caregiverDTO.getCaregiverId());  // 查询阿姨用户
                if (existingCaregiver == null) {
                    throw new RuntimeException("阿姨用户不存在");
                }

                // 3. 校验旧支付密码（数据库存储的是加密后的密码，直接对比加密后的值）
                if (!verify(oldPassword, existingCaregiver.getPayPassword())) {
                    throw new RuntimeException("旧支付密码不正确");
                }

                // 对新支付密码进行加密
                String encryptedNewPassword = MD5Utils.generate(caregiverDTO.getNewPassword());  // 加密新的支付密码
                existingCaregiver.setPayPassword(encryptedNewPassword);  // 设置新的支付密码

                // 4. 更新数据库中的支付密码
                return caregiverMapper.updateById(existingCaregiver);
            }

            return 1;  // 默认返回 1 表示成功

        } catch (Exception e) {
            log.error("修改支付密码异常", e);
            return 0;  // 返回 0 表示失败
        }
    }

    @Override
    public int deleteCaregiverPayPassword(String phoneNumber) {
        UpdateWrapper<Caregiver> wrapper = new UpdateWrapper<>();
        wrapper.eq("phone_number", phoneNumber);
        wrapper.set("pay_password", null);
        return caregiverMapper.update(null, wrapper);
    }

    @Override
    public ResultDTO<String> wxLoginCallback(String code) {
        // 获取access_token
        String tokenUrl = "https://api.weixin.qq.com/sns" +
                "/oauth2/access_token?appid=" + appid + "&secret=" + appSecret + "&code=" + code + "&grant_type=authorization_code";

        // 发送http请求
        RestTemplate restTemplate = new RestTemplate();
        WxAccessTokenResponse tokenResponse = restTemplate
                .getForObject(tokenUrl, WxAccessTokenResponse.class);
        if (tokenResponse == null) {
            return ResultDTO.error("获取用户信息失败");
        }

        // 获取用户信息
        String userInfoUrl = "https://api.weixin.qq.com/sns" +
                "/userinfo?access_token=" + tokenResponse.getAccess_token() + "&openid=" + tokenResponse.getOpenid() + "&lang=zh_CN";

        WxUserInfo userInfo = restTemplate.getForObject(userInfoUrl, WxUserInfo.class);

        if (userInfo == null) {
            return ResultDTO.error("获取用户信息失败");
        }

        Caregiver caregiver = selectByOpenId(userInfo.getOpenid());
        if (caregiver == null) {
            return ResultDTO.error("该微信未绑定账号");
//            caregiver = new Caregiver();
//            caregiver.setWechatOpenid(userInfo.getOpenid());
//            caregiver.setUpdateTime(new Date());
//            caregiver.setRegistrationTime(new Date());
//            caregiverMapper.insert(caregiver);
        }

        Long caregiverId = caregiver.getCaregiverId();
        StpUtil.login("caregiver:" + caregiverId);
        LoginUserDTO loginUserDTO = new LoginUserDTO();
        loginUserDTO.setPhone(caregiver.getPhoneNumber());
        loginUserDTO.setUserId(caregiverId);
        loginUserDTO.setUserType(2);
        StpUtil.getSession().set("caregiver:" + caregiverId, loginUserDTO);

        LoginDTO loginDTOs = new LoginDTO();
        loginDTOs.setCaregiverId(caregiverId);
        loginDTOs.setPhoneNumber(caregiver.getPhoneNumber());
        loginDTOs.setToken(StpUtil.getTokenValue());
        loginDTOs.setAvatar(caregiver.getAvatar());
        return ResultDTO.success("登录成功", StpUtil.getTokenValue());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultDTO<LoginDTO> getMobileLogin(MobileDTO mobileDTO) {
        try {
            log.info("阿姨端手机一键登录开始");
            String token = mobileDTO.getToken();
            String phoneNumber = aliyunDypnsService.getMobileByToken(token);

            return getLoginDTOResultDTO(phoneNumber, mobileDTO.getRegistrationId(), null);

        } catch (Exception e) {
            log.error("一键登录失败", e);
            return ResultDTO.error("一键登录失败");
        }
    }

    @Override
    public ResultDTO<?> loginWithWeChatMiniProgram(PhoneLoginDTO dto) {
        JSONObject json = payQueryHandler.getWechatMiniOpenIdByCode(dto.getCode());
        String sessionKey = json.getString("session_key");
        String openid = json.getString("openid"); // 获取 openid
        // 2. 解密手机号
        String phone = WXPhoneDecryptor.decrypt(dto.getEncryptedData(), sessionKey, dto.getIv());
        if (phone == null) {
            throw new RuntimeException("手机号解密失败");
        }
        return getLoginDTOResultDTO(phone, null, openid);
    }

    @Override
    public ResultDTO<String> updateCaregiverName(CaregiverDTO dto) {
        String loginIdAsString = StpUtil.getLoginIdAsString();
        LoginUserDTO loginUser = (LoginUserDTO) StpUtil.getSession().get(loginIdAsString);
        Long caregiverId = loginUser.getUserId();
        String name = dto.getName();
        Caregiver caregiver = new Caregiver();
        caregiver.setCaregiverId(caregiverId);
        caregiver.setName(name);
        caregiverMapper.updateById(caregiver);
        return ResultDTO.notDataSuccess("修改姓名成功");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultDTO<LoginDTO> loginwiThSms(LoginUserDTO loginUserDTO) {

        if (loginUserDTO.getPhone() == null || loginUserDTO.getVerificationCode() == null) {
            return ResultDTO.error("手机号或验证码不能为空");
        }
        if (!smsService.verifySmsCodeForLogin(loginUserDTO.getPhone(), loginUserDTO.getVerificationCode())) {
            return ResultDTO.error("验证码不能为空");
        }

        String phoneNumber = loginUserDTO.getPhone();
        return getLoginDTOResultDTO(phoneNumber, loginUserDTO.getRegistrationId(), null);

    }

    /**
     * 注册/登录提取方法
     *
     * @param phoneNumber    手机号
     * @param registrationId 极光推送id
     * @param openId         微信openId
     * @return
     */
    public ResultDTO<LoginDTO> getLoginDTOResultDTO(String phoneNumber, String registrationId, String openId) {
        Caregiver caregiver = selectByPhone(phoneNumber);
        if (caregiver == null) {
            LambdaQueryWrapper<SysPicture> sysPictureLambdaQueryWrapper = new LambdaQueryWrapper<>();
            sysPictureLambdaQueryWrapper.eq(SysPicture::getPictureType, 2);
            SysPicture sysPicture = sysPictureMapper.selectOne(sysPictureLambdaQueryWrapper);

            caregiver = new Caregiver();
            caregiver.setPhoneNumber(phoneNumber);
            caregiver.setStatus(0);
            caregiver.setExamStatus(0);
            caregiver.setAvgEvaluationScore(5.0);
            caregiver.setIdCardVerification(0);
            caregiver.setHealthCertificateVerification(0);
//            caregiver.setName("用户" + phoneNumber.substring(phoneNumber.length() - 4));
            caregiver.setAvatar(sysPicture.getDownloadAddress());
            caregiver.setRegistrationTime(new Date());
            caregiver.setRegistrationId(registrationId);
            caregiver.setWechatOpenid(openId);
            caregiverMapper.insert(caregiver);

            Wallet wallet = new Wallet();
            wallet.setUserId(caregiver.getCaregiverId());
            wallet.setUserType(2);
            wallet.setBalance(new BigDecimal(0));
            wallet.setRechargeBalance(new BigDecimal(0));
            wallet.setTotalIncome(new BigDecimal(0));
            wallet.setTotalSpent(new BigDecimal(0));
            walletMapper.insert(wallet);
        } else {
            Caregiver updateCaregiver = new Caregiver();
            updateCaregiver.setCaregiverId(caregiver.getCaregiverId());
            updateCaregiver.setRegistrationId(registrationId);
            caregiverMapper.updateById(updateCaregiver);
        }

        Long caregiverId = caregiver.getCaregiverId();
        StpUtil.login("caregiver:" + caregiverId);
        LoginUserDTO loginUserDTO = new LoginUserDTO();
        loginUserDTO.setPhone(caregiver.getPhoneNumber());
        loginUserDTO.setUserId(caregiverId);
        loginUserDTO.setUserType(2);
        StpUtil.getSession().set("caregiver:" + caregiverId, loginUserDTO);

        boolean isNotSetName = StringUtils.isBlank(caregiver.getName());

        LoginDTO loginDTOs = new LoginDTO();
        loginDTOs.setCaregiverId(caregiverId);
        loginDTOs.setPhoneNumber(caregiver.getPhoneNumber());
        loginDTOs.setToken(StpUtil.getTokenValue());
        loginDTOs.setAvatar(caregiver.getAvatar());
        loginDTOs.setIsNotSetName(isNotSetName);
        return ResultDTO.success("登录成功", loginDTOs);
    }
}




