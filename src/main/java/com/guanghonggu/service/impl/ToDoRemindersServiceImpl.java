package com.guanghonggu.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.guanghonggu.dto.ResultDTO;
import com.guanghonggu.dto.ToDoRemindersDTO;
import com.guanghonggu.entity.ToDoReminders;
import com.guanghonggu.mapper.dto.TodoRemindersDTOMapper;
import com.guanghonggu.service.ToDoRemindersService;
import com.guanghonggu.mapper.ToDoRemindersMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【to_do_reminders(代办提醒表)】的数据库操作Service实现
 * @createDate 2025-04-27 10:56:56
 */
@Service
public class ToDoRemindersServiceImpl extends ServiceImpl<ToDoRemindersMapper, ToDoReminders>
        implements ToDoRemindersService {

    @Autowired
    private ToDoRemindersMapper toDoRemindersMapper;

    @Override
    public ResultDTO<IPage<ToDoRemindersDTO>> listTodoReminders(ToDoRemindersDTO toDoRemindersDTO) {
        Integer status = toDoRemindersDTO.getStatus();
        Integer pageNum = toDoRemindersDTO.getCurrentPage();  // 当前页码
        Integer pageSize = toDoRemindersDTO.getPageSize(); // 每页数量

        if (pageNum == null || pageSize == null) {
            return ResultDTO.error("分页参数不能为空");
        }

        LambdaQueryWrapper<ToDoReminders> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(ToDoReminders::getRemindersId, ToDoReminders::getCaregiverId, ToDoReminders::getOrderId,
                ToDoReminders::getTitle,ToDoReminders::getDescription, ToDoReminders::getReminderTime, ToDoReminders::getStatus);

        // 如果不是查全部（0），才加状态条件
        if (status != null && status != 0) {
            queryWrapper.eq(ToDoReminders::getStatus, status);
        }
        queryWrapper.orderByDesc(ToDoReminders::getCreateTime);

        // 分页查询
        Page<ToDoReminders> page = new Page<>(pageNum, pageSize);
        IPage<ToDoReminders> entityPage = toDoRemindersMapper.selectPage(page, queryWrapper);

        // 转 DTO 列表
        List<ToDoRemindersDTO> dtoList = TodoRemindersDTOMapper.INSTANCE.toDoRemindersDTOList(entityPage.getRecords());

        // 构造返回分页对象（保留分页信息）
        IPage<ToDoRemindersDTO> resultPage = new Page<>();
        resultPage.setCurrent(entityPage.getCurrent());
        resultPage.setSize(entityPage.getSize());
        resultPage.setTotal(entityPage.getTotal());
        resultPage.setPages(entityPage.getPages());
        resultPage.setRecords(dtoList);

        return ResultDTO.success("获取列表成功", resultPage);
    }

    @Override
    public ResultDTO<String> updateTodoRemindersStatus(String remindersId) {

        ToDoReminders update = new ToDoReminders();
        update.setRemindersId(Long.parseLong(remindersId));
        update.setStatus(2);  // 状态：2 已完成

        int rows = toDoRemindersMapper.updateById(update);

        if (rows > 0) {
            return ResultDTO.notDataSuccess("更新成功");
        } else {
            return ResultDTO.error("更新失败，数据不存在");
        }
    }

    @Override
    public ResultDTO<String> deleteTodoReminders(String remindersId) {
        int rows = toDoRemindersMapper.deleteById(remindersId);

        if (rows > 0) {
            return ResultDTO.notDataSuccess("删除成功");
        } else {
            return ResultDTO.error("删除失败，记录不存在");
        }
    }
}




