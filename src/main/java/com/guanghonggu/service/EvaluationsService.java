package com.guanghonggu.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.guanghonggu.dto.EvaluationsDTO;
import com.guanghonggu.dto.ResultDTO;
import com.guanghonggu.entity.Evaluations;
import com.baomidou.mybatisplus.extension.service.IService;

/**
* <AUTHOR>
* @description 针对表【evaluations(评价表)】的数据库操作Service
* @createDate 2025-04-27 10:56:56
*/
public interface EvaluationsService extends IService<Evaluations> {
    //删除评价（包含阿里云 OSS 文件 + resource 表记录）
    long deleteEvaluationsId(EvaluationsDTO evaluationsDTO);
    //查询某个用户的评价记录（分页）
    Page<Evaluations> getEvaluationsPaged(Integer page, Integer size, Long evaluatorsId);
    //根据评价 ID 查询单条评价
    Evaluations getEvaluationsId(Long evaluationsId);
    //新增评价，并返回自增主键 ID（用于绑定资源表）
    Long addEvaluationAndReturnId(EvaluationsDTO dto);


    //查询所有评价（分页）
    Page<Evaluations> getAllEvaluationsPaged(Integer page, Integer size);
    //根据阿姨id查询
    Evaluations getEvaluatedId(EvaluationsDTO evaluationsDTO);
    //根据评价内容模糊查询（分页）
//    Page<Evaluations> getEvaluationsContentPaged(Integer page, Integer size, String evaluationContent);
    //修改一条评价记录
//    long updateEvaluations(EvaluationsDTO evaluationsDTO);

}
