package com.guanghonggu.service;

import com.guanghonggu.entity.SysConfig;
import com.baomidou.mybatisplus.extension.service.IService;

import java.math.BigDecimal;

/**
* <AUTHOR>
* @description 针对表【sys_config(系统配置表)】的数据库操作Service
* @createDate 2025-05-28 14:23:18
*/
public interface SysConfigService extends IService<SysConfig> {

    /**
     * 获取佣金率
     * @return
     */
    BigDecimal getCommissionRate();

    /**
     * 距离服务时间xx分钟内退款扣除的手续费比例
     * @return
     */
    BigDecimal getRefundFeeRateNearService();

    /**
     * 服务开始前多少分钟内退款需收取手续费
     * @return
     */
    int getRefundFeeMinutesNearService();

    /**
     * 获取迟到扣除的比例
     * @return
     */
    BigDecimal getLateDeductedRateService();
}
