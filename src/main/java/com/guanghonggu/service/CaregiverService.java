package com.guanghonggu.service;

import com.guanghonggu.dto.CaregiverDTO;
import com.guanghonggu.dto.LoginDTO;
import com.guanghonggu.dto.LoginUserDTO;
import com.guanghonggu.dto.MobileDTO;
import com.guanghonggu.dto.PhoneLoginDTO;
import com.guanghonggu.dto.ResultDTO;
import com.guanghonggu.dto.UserDTO;
import com.guanghonggu.entity.Caregiver;
import com.baomidou.mybatisplus.extension.service.IService;


/**
* <AUTHOR>
* @description 针对表【caregiver(阿姨表)】的数据库操作Service
* @createDate 2025-04-27 10:56:56
*/
public interface CaregiverService extends IService<Caregiver> {


    Caregiver selectByPhone(String phoneNumber);


    Caregiver selectByOpenId(String openid);

    /**
     * 根据阿姨id查询阿姨信息
     */
    CaregiverDTO getCaregiver(CaregiverDTO caregiverDTO);

    /**
     * 更新阿姨的考试状态
     * @return
     */
    ResultDTO<String> updateCaregiverExamStatus();

    /**
     * 提交审核资料
     * @param dto
     * @return
     */
    ResultDTO<String> uploadCaregiverInformation(CaregiverDTO dto);

    /**
     * 查询阿姨是否有支付密码
     */
    ResultDTO<Long> getPassword(CaregiverDTO caregiverDTO);

    /**
     * 设置支付密码（第一次设置时调用）
     */
    int setPayPassword(CaregiverDTO caregiverDTO);

    /**
     * 修改支付密码
     */
    int updatePayPassword(CaregiverDTO caregiverDTO);

    /**
     * 删除支付密码
     */
    int deleteCaregiverPayPassword(String phoneNumber);

    ResultDTO<String> wxLoginCallback(String code);

    ResultDTO<LoginDTO> getMobileLogin(MobileDTO mobileDTO);

    /**
     * 阿姨微信小程序登录
     * @param dto
     * @return
     */
    ResultDTO<?> loginWithWeChatMiniProgram(PhoneLoginDTO dto);

    /**
     * 修改阿姨姓名
     * @param dto
     * @return
     */
    ResultDTO<String> updateCaregiverName(CaregiverDTO dto);

    /**
     * 验证码登录
     * @param loginUserDTO
     * @return
     */
    ResultDTO<LoginDTO> loginwiThSms(LoginUserDTO loginUserDTO);
}
