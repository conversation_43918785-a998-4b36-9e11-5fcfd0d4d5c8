package com.guanghonggu.service;

import com.guanghonggu.dto.CouponDTO;
import com.guanghonggu.dto.ResultDTO;
import com.guanghonggu.dto.UserCouponDTO;
import com.guanghonggu.entity.UserCoupon;
import com.baomidou.mybatisplus.extension.service.IService;

/**
* <AUTHOR>
* @description 针对表【user_coupon(用户优惠卷表)】的数据库操作Service
* @createDate 2025-04-27 10:56:56
*/
public interface UserCouponService extends IService<UserCoupon> {

    ResultDTO<UserCouponDTO> grantRandomUserCoupon(UserCouponDTO userCouponDTO);

}
