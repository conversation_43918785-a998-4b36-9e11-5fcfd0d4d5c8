package com.guanghonggu.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.guanghonggu.dto.ResultDTO;
import com.guanghonggu.dto.WalletTransactionDTO;
import com.baomidou.mybatisplus.extension.service.IService;
import com.guanghonggu.entity.WalletTransaction;

/**
* <AUTHOR>
* @description 针对表【wallet_transaction(钱包交易记录表)】的数据库操作Service
* @createDate 2025-04-27 10:56:56
*/
public interface WalletTransactionService extends IService<WalletTransaction> {

    /**
     * 获取钱包交易记录
     * @return
     */
    ResultDTO<IPage<WalletTransactionDTO>> listWalletTransaction(WalletTransactionDTO walletTransactionDTO);

}
