package com.guanghonggu.service;

import com.guanghonggu.dto.ResultDTO;
import com.guanghonggu.dto.WalletDTO;
import com.guanghonggu.entity.Wallet;
import com.baomidou.mybatisplus.extension.service.IService;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

/**
* <AUTHOR>
* @description 针对表【wallet(钱包表)】的数据库操作Service
* @createDate 2025-04-27 10:56:56
*/
public interface WalletService extends IService<Wallet> {

    /**
     * 获取钱包余额
     * @return
     */
    ResultDTO<Wallet> getBalance();

    /**
     * 提现
     * @param requestDTO
     * @return
     * @throws Exception
     */
    ResultDTO<Map<String, Object>> withdrawToPlatform(WalletDTO requestDTO);

    /**
     * 充值
     * @param requestDTO
     * @return
     */
    ResultDTO<Map<String, Object>> rechargeWithWechat(WalletDTO requestDTO);

    /**
     * 充值回调
     * @param request
     * @return
     */
    Map<String, Object> handleCallback4Recharge(HttpServletRequest request);

    /**
     * 微信提现回调
     * @param request
     * @return
     */
    Map<String, Object> handleCallback4Withdraw(HttpServletRequest request);

    /**
     * 首次登录插入钱包表
     */
    void insert(Wallet wallet);

    /**
     * 获取钱包信息（总支出、总收入）
     * @return
     */
    Wallet getWalletInfo();
}
