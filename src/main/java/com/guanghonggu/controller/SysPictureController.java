package com.guanghonggu.controller;

import com.guanghonggu.dto.ResultDTO;
import com.guanghonggu.dto.SysPictureDTO;
import com.guanghonggu.service.SysPictureService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @author：zzn
 * @description：
 * @createData：2025/5/12 11:02
 */
@Slf4j
@RestController
@RequestMapping("/sysPicture")
public class SysPictureController {

    @Autowired
    private SysPictureService sysPictureService;

    @GetMapping("/list")
    public ResultDTO<List<SysPictureDTO>> listPictures(Integer pictureType) {
        try {
            ResultDTO<List<SysPictureDTO>> res = sysPictureService.listPictures(pictureType);
            return res;
        } catch (Exception e) {
            log.error("查询列表失败", e);
            return ResultDTO.error("查询列表失败");
        }
    }
}
