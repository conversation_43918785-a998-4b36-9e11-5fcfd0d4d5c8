package com.guanghonggu.controller;

import com.guanghonggu.dto.LocationDTO;
import com.guanghonggu.dto.ResultDTO;
import com.guanghonggu.entity.Location;
import com.guanghonggu.service.LocationService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/location")
public class LocationController {

    private final LocationService locationService;

    public LocationController(LocationService locationService) {
        this.locationService = locationService;
    }

    @PostMapping("/upload")
    public ResultDTO<String> uploadLocation(@RequestBody(required = false) LocationDTO locationDTO) {
        System.out.println("【收到定位】caregiverId=" + locationDTO.getCaregiverId()
                + ", lat=" + locationDTO.getLatitude()
                + ", lng=" + locationDTO.getLongitude());
        locationService.saveLocation(locationDTO);
        return ResultDTO.success("定位上传成功",null);
    }

    @PostMapping("/getByCaregiverId")
    public ResultDTO<Location> getLocationByUserId(@RequestBody(required = false) LocationDTO locationDTO) {
        try{
            if (locationDTO.getCaregiverId() == null){
                return ResultDTO.error("阿姨id不能为空");
            }

            Location location = locationService.getLocationByUserId(locationDTO.getCaregiverId());
            if (location == null){
                return ResultDTO.error("该阿姨暂无定位");
            }
            return ResultDTO.success("查询成功",location);
        }catch (Exception e){
            e.printStackTrace();
            return ResultDTO.error("查询失败" + e.getMessage());
        }
    }
}