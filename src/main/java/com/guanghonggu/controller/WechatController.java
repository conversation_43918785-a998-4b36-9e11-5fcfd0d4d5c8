package com.guanghonggu.controller;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONObject;
import com.guanghonggu.dto.LoginUserDTO;
import com.guanghonggu.dto.ResultDTO;
import com.guanghonggu.strategy.user.UserByTypeStrategyContext;
import com.guanghonggu.strategy.user.UserByTypeStrategyInterface;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * @author：zzn
 * @description：
 * @createData：2025/6/18 8:47
 */
@Slf4j
@RestController
@RequestMapping("/wechat")
public class WechatController {

    @Value("${address.domain}")
    private String domainAddress;

    @Value("${wx.app.appId}")
    private String appid;

    @Value("${wx.app.appSecret}")
    private String secret;

    @Autowired
    private UserByTypeStrategyContext userByTypeStrategyContext;

    /**
     * 绑定微信
     *
     * @return
     */
    @PostMapping("/bind")
    public ResultDTO<String> wechatBind(@RequestBody Map<String, String> map) {
        String code = map.get("code");
        String loginIdAsString = StpUtil.getLoginIdAsString();
        LoginUserDTO loginInfo = (LoginUserDTO) StpUtil.getSession().get(loginIdAsString);
        Long userId = loginInfo.getUserId();
        Integer userType = loginInfo.getUserType();
        // 获取用户openId
        UserByTypeStrategyInterface typeInstance = userByTypeStrategyContext.getTypeInstance(userType);

        // 根据code获取openId
        String url = "https://api.weixin.qq.com/sns/oauth2/access_token?appid="
                + appid + "&secret=" + secret + "&code=" + code
                + "&grant_type=authorization_code";
        HashMap<String, Object> param = new HashMap<>();
        String res = HttpUtil.get(url, param);
        log.info("res:{}", res);
        log.info("code:{}", code);
        String openid = JSONObject.parseObject(res).getString("openid");
        log.info("userId:{},openId:{}", userId, openid);
        typeInstance.wechatBind(openid, userId);

        return ResultDTO.notDataSuccess("绑定成功");
    }
}
