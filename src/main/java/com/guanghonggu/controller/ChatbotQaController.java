package com.guanghonggu.controller;

import com.guanghonggu.dto.ResultDTO;
import com.guanghonggu.service.ChatbotQaService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * @author：zzn
 * @description：
 * @createData：2025/8/27 15:29
 */
@Slf4j
@RestController
@RequestMapping("/chatbot")
public class ChatbotQaController {
    @Autowired
    private ChatbotQaService chatbotService;

    @GetMapping("/ask")
    public ResultDTO<String> ask(@RequestParam String query) {
        return chatbotService.searchAnswer(query);
    }
}
