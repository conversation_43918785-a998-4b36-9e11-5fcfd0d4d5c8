package com.guanghonggu.controller;

import com.guanghonggu.dto.CaregiverServiceVerificationDTO;
import com.guanghonggu.dto.ResultDTO;
import com.guanghonggu.service.CaregiverServiceVerificationService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * @author：zzn
 * @Description：阿姨服务验证
 * @createData：2025/4/27 11:14
 */
@RestController
@RequestMapping("/caregiverServiceVerification")
public class CaregiverServiceVerificationController {


    @Autowired
    private CaregiverServiceVerificationService caregiverServiceVerificationService;

    // 阿姨到达服务地点拍照打卡
    @PostMapping("/photoClock")
    public ResultDTO<String> photoClock(@ModelAttribute CaregiverServiceVerificationDTO caregiverServiceVerificationDTO) {

        ResultDTO<String> res = caregiverServiceVerificationService.photoClock(caregiverServiceVerificationDTO);
        return res;
    }


}
