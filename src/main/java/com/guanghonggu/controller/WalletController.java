package com.guanghonggu.controller;

import com.guanghonggu.dto.ResultDTO;
import com.guanghonggu.dto.WalletDTO;
import com.guanghonggu.entity.Wallet;
import com.guanghonggu.service.WalletService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.HashMap;
import java.util.Map;

/**
 * @author：zzn
 * @Description：钱包
 * @createData：2025/4/27 11:22
 */
@Slf4j
@RestController
@RequestMapping("/wallet")
public class WalletController {

    @Autowired
    private WalletService walletService;

    @GetMapping("/balance")
    public ResultDTO<Wallet> getBalance() {
        try {
            ResultDTO<Wallet> balance = walletService.getBalance();
            return balance;
        } catch (Exception e) {
            log.error("获取余额失败", e);
            return ResultDTO.error("获取余额失败");
        }
    }

    @PostMapping("/recharge")
    public ResultDTO<Map<String, Object>> rechargeWithWechat(@RequestBody WalletDTO requestDTO) {
        try {
            return walletService.rechargeWithWechat(requestDTO);
        } catch (Exception e) {
            log.error("充值失败", e);
            return ResultDTO.error("充值失败：" + e.getMessage());
        }
    }

    @PostMapping("/wechat/recharge/notify")
    public Map<String, Object> rechargeNotify(HttpServletRequest request) {
        try {
            Map<String, Object> result = walletService.handleCallback4Recharge(request);
            return result;
        } catch (Exception e) {
            HashMap<String, Object> resultMap = new HashMap<>();
            resultMap.put("code","FAIL");
            resultMap.put("message",e.getMessage());
            log.error("回调失败", e);
            return resultMap;
        }
    }

    @PostMapping("/withdraw")
    public ResultDTO<Map<String, Object>> withdraw(@RequestBody @Valid WalletDTO requestDTO) {
        try {
            return walletService.withdrawToPlatform(requestDTO);
        } catch (Exception e) {
            log.error("提现失败", e);
            return ResultDTO.error("提现失败：" + e.getMessage());
        }
    }
    @PostMapping("/wechat/withdraw/notify")
    public Map<String, Object> withdrawNotify(HttpServletRequest request) {
        try {
            Map<String, Object> result = walletService.handleCallback4Withdraw(request);
            return result;
        } catch (Exception e) {
            HashMap<String, Object> resultMap = new HashMap<>();
            resultMap.put("code","FAIL");
            resultMap.put("message",e.getMessage());
            log.error("回调失败", e);
            return resultMap;
        }
    }

//    @GetMapping("/search/withdraw")
//    public ResultDTO<Map<String, Object>> searchWithdraw(String outBillNo) {
//        try {
//            Map<String, Object> withdraw = walletService.searchWithdraw(outBillNo);
//            return ResultDTO.success("查询成功", withdraw);
//        } catch (Exception e) {
//            log.error("查询失败", e);
//            return ResultDTO.error("查询失败");
//        }
//    }


    @GetMapping("/info")
    public ResultDTO<Wallet> getWalletInfo() {
        try {
            Wallet info = walletService.getWalletInfo();
            return ResultDTO.success("获取成功", info);
        } catch (Exception e) {
            log.error("获取信息失败", e);
            return ResultDTO.error("获取信息失败");
        }
    }
}
