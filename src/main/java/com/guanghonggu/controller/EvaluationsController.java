package com.guanghonggu.controller;

import cn.dev33.satoken.stp.StpUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.guanghonggu.constant.CommonConstants;
import com.guanghonggu.dto.EvaluationsDTO;
import com.guanghonggu.dto.LoginUserDTO;
import com.guanghonggu.dto.ResultDTO;
import com.guanghonggu.entity.Evaluations;
import com.guanghonggu.service.EvaluationsService;
import com.guanghonggu.service.ResourceService;
import com.guanghonggu.util.AliyunOssUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;


@RestController
@RequestMapping("/evaluations")
public class EvaluationsController {

    @Autowired
    private EvaluationsService evaluationsService;
    @Autowired
    private AliyunOssUtil aliyunOssUtil;
    @Autowired
    private ResourceService resourceService;

    @GetMapping
    public ResultDTO<Page<Evaluations>> getAllEvaluationsPaged(
            @RequestBody(required = false) EvaluationsDTO evaluationsDTO
    ) {
        try{

            if (evaluationsDTO == null) {
                evaluationsDTO = new EvaluationsDTO();
            }
            Integer page = evaluationsDTO.getPage() != null ? evaluationsDTO.getPage() : 1;
            Integer size = evaluationsDTO.getSize() != null ? evaluationsDTO.getSize() : 10;

            Page<Evaluations> evaluationsPage = evaluationsService.getAllEvaluationsPaged(page, size);
            return ResultDTO.success("根据评价内容查询成功",evaluationsPage);
        }catch (Exception e){
            e.printStackTrace();
            return ResultDTO.error("根据评价内容查询失败" + e.getMessage());
        }
    }

        @PostMapping("/evaluatedId")
    public ResultDTO<Evaluations> getEvaluatedId(@RequestBody(required = false) EvaluationsDTO evaluationsDTO){
        try{
         Evaluations evaluatedId = evaluationsService.getEvaluatedId(evaluationsDTO);
         return ResultDTO.success("根据阿姨id查询成功",evaluatedId);
        }catch (Exception e){
            e.printStackTrace();
            return ResultDTO.error("根据阿姨id查询失败" + e.getMessage());
        }
    }

    @PostMapping("/getEvaluatorsId")
    public ResultDTO<Page<Evaluations>> getEvaluationsPaged(
            @RequestBody(required = false) EvaluationsDTO evaluationsDTO
    ) {
        try {

            if (evaluationsDTO == null) {
                evaluationsDTO = new EvaluationsDTO();
            }
            Integer page = evaluationsDTO.getPage() != null ? evaluationsDTO.getPage() : 1;
            Integer size = evaluationsDTO.getSize() != null ? evaluationsDTO.getSize() : 10;

            Page<Evaluations> evaluationsPage = evaluationsService.getEvaluationsPaged(page, size, evaluationsDTO.getEvaluatorsId());
            if (evaluationsPage.getRecords().isEmpty()) {
                return ResultDTO.error("传入的evaluatorsId不存在");
            }
            return ResultDTO.success("根据评价人ID查询成功",evaluationsPage);
        } catch (Exception e) {
            e.printStackTrace();
            return ResultDTO.error("根据评价人ID查询失败" + e.getMessage());
        }
    }

    @PostMapping("/getEvaluationsId")
    public ResultDTO<Evaluations> getEvaluationsId(@RequestBody(required = false) EvaluationsDTO evaluationsDTO) {
        try{

            if (evaluationsDTO == null) {
                evaluationsDTO = new EvaluationsDTO();
            }
            Evaluations evaluations = evaluationsService.getEvaluationsId(evaluationsDTO.getEvaluationsId());
            if (evaluations == null) {
                return ResultDTO.error("传入的evaluationsId不存在");
            }
            return ResultDTO.success("根据投诉Id查询成功",evaluations);
        }catch (Exception e){
            e.printStackTrace();
            return ResultDTO.error("根据投诉Id查询失败" + e.getMessage());
        }
    }


    @PostMapping("/postEvaluation")
    public ResultDTO<String> addEvaluationWithFiles(
            @ModelAttribute EvaluationsDTO evaluationsDTO,
            @RequestPart(value = "files", required = false) List<MultipartFile> files) {
        try {
            // 获取当前登录用户 ID（可能是 Long，也可能是字符串）
            Object loginId = StpUtil.getLoginId();
            Object loginObj = StpUtil.getSession().get(loginId.toString());

            // 判断 loginObj 是否为 LoginUserDTO 类型
            if (loginObj instanceof LoginUserDTO) {
                LoginUserDTO loginUserDTO = (LoginUserDTO) loginObj;
                Long evaluatorId = loginUserDTO.getUserId();
                evaluationsDTO.setEvaluatorsId(evaluatorId); // 设置评价人 ID
            } else {
                return ResultDTO.error("未登录或用户信息失效");
            }

            // 第一步：插入评价表 evaluations，返回自增主键 ID
            Long evaluationId = evaluationsService.addEvaluationAndReturnId(evaluationsDTO);

            // 第二步：处理上传的文件并存入 resource 表
            if (files != null && !files.isEmpty()) {
                for (MultipartFile file : files) {
                    // 打印文件信息方便调试
                    System.out.println("上传文件: " + file.getOriginalFilename() + "，大小: " + file.getSize());

                    // 上传到阿里云 OSS，返回完整访问地址
                    String objectUrl = aliyunOssUtil.upload(file, CommonConstants.PICTURE_URL_USER_EVALUATION + loginId);
                    if (objectUrl == null) {
                        System.out.println("OSS 上传失败，跳过该文件");
                        continue;
                    }

                    // 将上传记录保存到资源表 resource 中
                    resourceService.saveResource(
                            evaluationId,
                            evaluationsDTO.getEvaluatorsId(), // 上传人 ID（当前登录用户）
                            objectUrl
                    );
                }
            }

            return ResultDTO.success("评价及附件上传成功", null);

        } catch (Exception e) {
            e.printStackTrace();
            return ResultDTO.error("上传失败：" + e.getMessage());
        }
    }



//    @PutMapping("/updateEvaluations")
//        public ResultDTO<Long> updateEvaluations(@RequestBody(required = false) EvaluationsDTO evaluationsDTO) {
//            try {
//                evaluationsDTO.setEvaluationsId(evaluationsDTO.getEvaluationsId());
//                long result = evaluationsService.updateEvaluations(evaluationsDTO);
//                if (result > 0) {
//                    return ResultDTO.success("更新评价成功",evaluationsDTO.getEvaluationsId());
//                }else {
//                    return ResultDTO.error("更新失败，未找到地址");
//                }
//            } catch (Exception e) {
//                e.printStackTrace();
//                return ResultDTO.error("更新评价失败" + e.getMessage());
//            }
//    }

    @DeleteMapping("/deleteEvaluations")
    public ResultDTO<Long> deleteEvaluationsId(@RequestBody EvaluationsDTO evaluationsDTO) {
        try {
            // 第1步：调用 Service 层，执行删除操作
            // 会完成 3 件事：
            //   删除 resource 表中绑定该评价的记录（图片路径）
            //   删除阿里云 OSS 上的对应文件
            //   删除 evaluations 表中该条评价数据
            long deletedId = evaluationsService.deleteEvaluationsId(evaluationsDTO);

            // 第2步：返回成功响应（使用统一的 ResultDTO 返回格式）
            return ResultDTO.success("删除评价成功", deletedId);

        } catch (Exception e) {
            // 如果出现异常，打印错误并返回失败信息
            e.printStackTrace();
            return ResultDTO.error("删除评价失败：" + e.getMessage());
        }
    }

}
