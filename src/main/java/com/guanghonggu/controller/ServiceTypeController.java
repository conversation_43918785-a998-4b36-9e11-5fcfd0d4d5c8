package com.guanghonggu.controller;

import com.guanghonggu.dto.ResultDTO;
import com.guanghonggu.dto.ServiceAttributeDTO;
import com.guanghonggu.dto.ServiceTypeDTO;
import com.guanghonggu.service.ServiceTypeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @author：zzn
 * @description：
 * @createData：2025/5/23 15:20
 */
@Slf4j
@RestController
@RequestMapping("/serviceType")
public class ServiceTypeController {

    @Autowired
    private ServiceTypeService serviceTypeService;

    @GetMapping("/list")
    public ResultDTO<List<ServiceTypeDTO>> getServiceTypes() {
        try {
            List<ServiceTypeDTO> list = serviceTypeService.getServiceTypeList();
            return ResultDTO.success("获取服务属性成功", list);
        } catch (Exception e) {
            log.error("获取服务属性失败", e);
            return ResultDTO.error("获取服务属性失败");
        }
    }
}
