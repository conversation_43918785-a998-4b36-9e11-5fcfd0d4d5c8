package com.guanghonggu.controller;

import com.guanghonggu.dto.ComplaintDTO;
import com.guanghonggu.dto.ResultDTO;
import com.guanghonggu.service.ComplaintService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @author：zzn
 * @Description：投诉
 * @createData：2025/4/27 11:15
 */
@RestController
@RequestMapping("/complaint")
public class ComplaintController {

    @Autowired
    private ComplaintService complaintService;


    /*
     *
     * 用户投诉
     * */
    @PostMapping("/addComplaint")
    public ResultDTO<String> addComplaint(@RequestBody ComplaintDTO complaintDTO) {

        ResultDTO<String> res = complaintService.addComplaint(complaintDTO);

        return res;
    }


}
