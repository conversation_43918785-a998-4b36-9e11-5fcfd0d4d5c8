package com.guanghonggu.controller;

import com.guanghonggu.dto.CaregiverScheduleDTO;
import com.guanghonggu.dto.ResultDTO;
import com.guanghonggu.entity.CaregiverSchedule;
import com.guanghonggu.handler.exception.BizException;
import com.guanghonggu.service.CaregiverScheduleService;
import com.guanghonggu.service.CaregiverService;
import com.guanghonggu.validator.group.CaregiverScheduleAppointmentGroup;
import com.guanghonggu.validator.group.OrderPayGroup;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import java.util.Map;

/**
 * @author：zzn
 * @Description：阿姨日程
 * @createData：2025/4/27 11:09
 */
@Slf4j
@RestController
@RequestMapping("/caregiverSchedule")
public class CaregiverScheduleController {
    @Autowired
    private CaregiverScheduleService caregiverScheduleService;

    /**
     * 查询阿姨的日程安排（未来预约）
     */
    @PostMapping("/schedule/list/caregiverId")
    public ResultDTO<List<CaregiverSchedule>> getScheduleList(@RequestBody CaregiverScheduleDTO caregiverScheduleDTO) {
        try {
            List<CaregiverSchedule> caregiverSchedules = caregiverScheduleService.getScheduleListByCaregiverId(caregiverScheduleDTO);
            return ResultDTO.success("查询成功", caregiverSchedules);
        } catch (Exception e) {
            e.printStackTrace();
            return ResultDTO.error("查询失败" + e.getMessage());
        }
    }

    /**
     * 删除日程
     */
    @DeleteMapping("/deleteCaregiverSchedule")
    public ResultDTO<Long> deleteSchedule(@RequestBody(required = false) CaregiverScheduleDTO caregiverScheduleDTO) {
        try {
            Long deleteSchedule = caregiverScheduleService.deleteCaregiverSchedule(caregiverScheduleDTO);
            return ResultDTO.success("删除日程成功", deleteSchedule);
        } catch (Exception e) {
            e.printStackTrace();
            return ResultDTO.error("删除日程失败" + e.getMessage());
        }
    }

    /**
     * 更新日程
     */
    @PutMapping("/updateCaregiverSchedule")
    public ResultDTO<Long> updateSchedule(@RequestBody(required = false) CaregiverScheduleDTO caregiverScheduleDTO) {
        try {
            if (caregiverScheduleDTO == null) {
                caregiverScheduleDTO = new CaregiverScheduleDTO();
            }
            Long caregiverScheduleId = caregiverScheduleDTO.getScheduleId();
            caregiverScheduleDTO.setCaregiverId(caregiverScheduleId);
            int result = caregiverScheduleService.updateCaregiverSchedule(caregiverScheduleDTO);
            if (result > 0) {
                return ResultDTO.success("更新成功", caregiverScheduleId);
            } else {
                return ResultDTO.error("更新失败为找到ID");
            }

        } catch (Exception e) {
            e.printStackTrace();
            return ResultDTO.error("更新日程失败" + e.getMessage());
        }
    }

    /**
     * 根据日程日期查询
     */
    @PostMapping("/scheduleDate")
    public ResultDTO<List<CaregiverSchedule>> getScheduleDate(@RequestBody CaregiverScheduleDTO dto) {
        return caregiverScheduleService.getScheduleDate(dto);
    }

    /**
     * 阿姨接单
     */
    @PostMapping("/schedule/add-order")
    public ResultDTO<?> addOrderSchedule(@RequestBody CaregiverScheduleDTO dto) {
        return caregiverScheduleService.addOrderSchedule(dto);
    }

    /**
     * 设置日程类型
     */
    @PostMapping("/toggleScheduleStatus")
    public ResultDTO<?> toggleScheduleStatus(@RequestBody CaregiverScheduleDTO caregiverScheduleDTO) {
        try {
            return caregiverScheduleService.toggleScheduleStatus(caregiverScheduleDTO);
        } catch (Exception e) {
            e.printStackTrace();
            return ResultDTO.error("设置失败" + e.getMessage());
        }
    }

    /**
     * 设置拒绝接单时间
     */
    @PostMapping("/schedule/block")
    public ResultDTO<?> blockSchedule(@RequestBody CaregiverScheduleDTO dto) {
        return caregiverScheduleService.blockSchedule(dto);
    }

    /**
     * 查询阿姨今天设置不可接单时间
     */
    @PostMapping("/schedule/blocked-hours")
    public ResultDTO<List<Map<String, Object>>> getBlockedHours(@RequestBody CaregiverScheduleDTO dto) {
        return caregiverScheduleService.getBlockedHourList(dto);
    }


    /**
     * 判断阿姨排班是否冲突
     * 设置阿姨下班时间
     */
    @PostMapping("/schedule/check-conflict")
    public ResultDTO<?> checkConflict(@RequestBody CaregiverScheduleDTO dto) {
        boolean hasConflict = caregiverScheduleService.hasConflictSchedule(
                dto.getCaregiverId(),
                dto.getScheduleDate(),
                dto.getScheduleStartTime(),
                dto.getScheduleEndTime()
        );
        return ResultDTO.success("冲突检测结果", hasConflict);
    }

    /**
     * 设置阿姨下班时间
     * @param caregiverScheduleDTO
     * @return
     */
    @PostMapping("/setOffDutyTime")
    public String setOffDutyTime(@RequestBody CaregiverScheduleDTO caregiverScheduleDTO) {
        caregiverScheduleService.setOffDutyTime(caregiverScheduleDTO);
        return "下班时间设置成功";
    }


//    @PostMapping("/appointment")
    public ResultDTO<Map<String, Object>> appointmentCareGiver(@Validated(value = {OrderPayGroup.class, CaregiverScheduleAppointmentGroup.class})
                                                               @RequestBody CaregiverScheduleDTO caregiverScheduleDTO) {
        try {
            ResultDTO<Map<String, Object>> res = caregiverScheduleService.appointmentCaregiver(caregiverScheduleDTO);
            return res;
        } catch (BizException e) {
            log.warn("业务异常：{}", e.getMessage());
            return ResultDTO.error(e.getMessage());
        } catch (Exception e) {
            log.error("预约失败", e);
            return ResultDTO.error("预约失败");
        }
    }

}
