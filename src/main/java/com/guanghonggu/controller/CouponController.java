package com.guanghonggu.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.guanghonggu.dto.CouponDTO;
import com.guanghonggu.dto.ResultDTO;
import com.guanghonggu.service.CouponService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.Pattern;
import java.util.List;

/**
 * @author：zzn
 * @Description：优惠券
 * @createData：2025/4/27 11:17
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/coupon")
public class CouponController {


    @Autowired
    private CouponService couponService;

    @GetMapping("/user")
    public ResultDTO<IPage<CouponDTO>> listUserCoupon(CouponDTO couponDTO) {
        try {
            ResultDTO<IPage<CouponDTO>> couponList = couponService.listUserCoupon(couponDTO);
            return couponList;
        } catch (Exception e) {
            log.error("获取用户优惠卷列表失败", e);
            return ResultDTO.error("获取用户优惠券列表失败");
        }
    }

    @PostMapping("/publish")
    public ResultDTO<String> publishCoupon() {
        try {
            couponService.publishCoupon();
            return ResultDTO.notDataSuccess("发布优惠券成功");
        } catch (Exception e) {
            log.error("发布优惠券失败", e);
            return ResultDTO.error("发布优惠券失败");
        }
    }

    /**
     * 领取优惠卷
     */
    @PostMapping("/receive/{couponId}")
    public ResultDTO<String> receiveCoupon(@PathVariable
                                           @Pattern(regexp = "\\d+", message = "优惠券id只能输入数字") String couponId) {
        try {
            ResultDTO<String> resultDTO = couponService.receiveCoupon(couponId);
            return resultDTO;
        } catch (Exception e) {
            log.error("领取优惠券失败", e);
            return ResultDTO.error("领取优惠券失败");
        }
    }

    @GetMapping("/{couponId}")
    public ResultDTO<CouponDTO> getCoupon(@PathVariable
                                          @Pattern(regexp = "\\d+", message = "优惠券id只能输入数字") String couponId) {
        try {
            ResultDTO<CouponDTO> coupon = couponService.getCoupon(couponId);
            return coupon;
        } catch (Exception e) {
            log.error("获取优惠券信息失败", e);
            return ResultDTO.error("获取优惠券信息失败");
        }
    }

}
