package com.guanghonggu.controller;

import cn.dev33.satoken.stp.StpUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.guanghonggu.constant.CommonConstants;
import com.guanghonggu.dto.CaregiverServiceTypeDTO;
import com.guanghonggu.dto.LoginUserDTO;
import com.guanghonggu.dto.OrderDTO;
import com.guanghonggu.dto.ResultDTO;
import com.guanghonggu.entity.Caregiver;
import com.guanghonggu.entity.Order;

import com.guanghonggu.enums.AppType;
import com.guanghonggu.enums.WebSocketMessageType;
import com.guanghonggu.mapper.CaregiverMapper;
import com.guanghonggu.service.OrderService;


import com.guanghonggu.util.NotificationUtil;
import com.guanghonggu.websocket.WebSocketServer;
import org.springframework.beans.factory.annotation.Autowired;

import org.springframework.web.bind.annotation.*;

import java.util.List;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;


import com.guanghonggu.validator.group.OrderPayGroup;
import lombok.extern.slf4j.Slf4j;

import org.springframework.validation.annotation.Validated;


import java.time.Duration;
import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.HashMap;
import java.util.Map;

/**
 * @author：zzn
 * @Description：订单
 * @createData：2025/4/27 11:18
 */
@Slf4j
@RestController
@RequestMapping("/order")
public class OrderController {

    @Autowired
    private OrderService orderService;
    @Autowired
    private WebSocketServer webSocketServer;

    @Autowired
    private NotificationUtil notificationUtil;
    @Autowired
    private CaregiverMapper caregiverMapper;


    // 再下一单
//    @GetMapping("/again")
    public ResultDTO<OrderDTO> orderAgain(String orderId) {

        ResultDTO<OrderDTO> orderAgain = orderService.orderAgain(orderId);
        return orderAgain;
    }


    // 获取预约大厅列表
    @GetMapping("/caregiver/getOrderList")
    public ResultDTO<IPage<OrderDTO>> getOrderInfo(OrderDTO orderDTO) {
        IPage<OrderDTO> list = orderService.getOrderByList(orderDTO);
        return ResultDTO.success("成功", list);
    }


    // 查看预约大厅订单详细信息
    @GetMapping("/caregiver/{id}")
    public ResultDTO<OrderDTO> getOrderInfo(@PathVariable("id") Long id) {
        ResultDTO<OrderDTO> order = orderService.getByOrderIds(id);
        return order;
    }


    // 阿姨接单
    @PostMapping("/caregiver/accept/{orderId}")
    public ResultDTO<String> acceptOrder(@PathVariable Long orderId) {
        ResultDTO<String> res = orderService.updateOrder(orderId);
        return res;
    }

    // 阿姨完成服务
    @PutMapping("/caregiver/complete/{orderId}")
    public ResultDTO<String> completeOrder(@PathVariable Long orderId) {
        ResultDTO<String> res = orderService.updateOrderById(orderId);
        return res;
    }


    @GetMapping
    public ResultDTO<Page<Order>> getOrdersByPage(
            @RequestBody(required = false) OrderDTO orderDTO) {
        try {

            if (orderDTO == null) {
                orderDTO = new OrderDTO();
            }

            Integer page = orderDTO.getPage() != null ? orderDTO.getPage() : 1;
            Integer size = orderDTO.getSize() != null ? orderDTO.getSize() : 10;

            Page<Order> resultPage = orderService.getOrdersByPage(page, size);
            return ResultDTO.success("分页查询成功", resultPage);
        } catch (Exception e) {
            e.printStackTrace();
            return ResultDTO.error("分页查询失败" + e.getMessage());
        }
    }

    /**
     * 更改订单类型
     */
    @PutMapping("/putStatus")
    public ResultDTO<Long> putStatus(@RequestBody OrderDTO orderDTO) {
        try {

            String loginIdAsString = StpUtil.getLoginIdAsString();
            LoginUserDTO loginUser = (LoginUserDTO) StpUtil.getSession().get(loginIdAsString);
            if (orderDTO.getUserId() == null) {
                orderDTO.setUserId(loginUser.getUserId());
            }

            Long orderId = orderDTO.getOrderId();
            orderDTO.setOrderId(orderId);
            ResultDTO<Long> result = orderService.putStatus(orderDTO);
            if (result.getCode() == 200 && result.getData() != null && result.getData() > 0) {
                return ResultDTO.success("更改订单类型成功", result.getData());
            } else {
                return ResultDTO.error("更改失败或未找到订单");
            }
        } catch (Exception e) {
            e.printStackTrace();
            return ResultDTO.error("更改订单类型失败" + e.getMessage());
        }

    }

    /**
     * 查询各种类型订单数量
     */
    @GetMapping("/statusCount")
    public ResultDTO<Map<String, Integer>> getOrders() {
        try {
            String loginIdAsString = StpUtil.getLoginIdAsString();
            LoginUserDTO loginInfo = (LoginUserDTO) StpUtil.getSession().get(loginIdAsString);
            Long userId = loginInfo.getUserId();


            // 调用 Service 层方法获取订单数量，并传递用户 ID
            Map<String, Integer> result = orderService.getOrdersCountByUserId(userId);
            return ResultDTO.success("查询成功", result);
        } catch (Exception e) {
            e.printStackTrace();
            return ResultDTO.error("查询失败: " + e.getMessage());
        }
    }


    /**
     * 删除订单
     */
    @DeleteMapping("/deleteOrderId")
    public ResultDTO<Long> deleteOrderId(@RequestBody OrderDTO orderDTO) {
        try {
            Long deleteOrderId = orderService.deleteOrderId(orderDTO);
            return ResultDTO.success("删除订单成功", deleteOrderId);
        } catch (Exception e) {
            e.printStackTrace();
            return ResultDTO.error("删除订单成功" + e.getMessage());
        }
    }

    // 获取指定类型订单数量
    @PostMapping("/quantity/serviceType")
    public ResultDTO<Long> getOrderCountByType(@RequestBody(required = false) OrderDTO orderDTO) {
        try {
            String loginIdAsString = StpUtil.getLoginIdAsString();
            LoginUserDTO loginUser = (LoginUserDTO) StpUtil.getSession().get(loginIdAsString);

            if (orderDTO.getUserId() == null) {
                orderDTO.setUserId(loginUser.getUserId());
            }

            long count = orderService.getOrderCountByType(orderDTO.getServiceTypeId());
            return ResultDTO.success("查询订单数量成功", count);
        } catch (Exception e) {
            e.printStackTrace();
            return ResultDTO.error("查询订单数量失败" + e.getMessage());
        }
    }

    // 获取指定类型订单列表
    @PostMapping("/list/serviceType")
    public ResultDTO<Page<Order>> getOrdersByType(@RequestBody(required = false) OrderDTO orderDTO) {
        try {
            if (orderDTO == null) {
                orderDTO = new OrderDTO();
            }
            int page = (orderDTO.getPage() != null) ? orderDTO.getPage() : 1;
            int size = (orderDTO.getSize() != null) ? orderDTO.getSize() : 10;

            String loginIdAsString = StpUtil.getLoginIdAsString();
            LoginUserDTO loginUser = (LoginUserDTO) StpUtil.getSession().get(loginIdAsString);
            orderDTO.setUserId(loginUser.getUserId());

            if (orderDTO.getUserId() == null) {
                orderDTO.setUserId(loginUser.getUserId());
            }
            Page<Order> pageResult = orderService.getOrdersByType(orderDTO, page, size);
            return ResultDTO.success("查询订单列表成功", pageResult);
        } catch (Exception e) {
            e.printStackTrace();
            return ResultDTO.error("查询订单列表失败" + e.getMessage());
        }
    }

    @PostMapping("/orderId")
    public ResultDTO<OrderDTO> getOrderById(@RequestBody(required = false) OrderDTO orderDTO) {
        try {
            OrderDTO order = orderService.getOrderById(orderDTO.getOrderId());
            return ResultDTO.success("查询成功", order);
        } catch (Exception e) {
            e.printStackTrace();
            return ResultDTO.error("查询成功" + e.getMessage());
        }
    }

    // 分页查询：根据用户 ID 获取订单
    @PostMapping("/userId")
    public ResultDTO<Page<OrderDTO>> getOrdersByUserIdPaged(@RequestBody(required = false) OrderDTO orderDTO) {
        try {
            if (orderDTO == null) orderDTO = new OrderDTO();

            String loginIdAsString = StpUtil.getLoginIdAsString();
            LoginUserDTO loginUser = (LoginUserDTO) StpUtil.getSession().get(loginIdAsString);
            orderDTO.setUserId(loginUser.getUserId());

            Integer page = orderDTO.getPage() != null ? orderDTO.getPage() : 1;
            Integer size = orderDTO.getSize() != null ? orderDTO.getSize() : 10;

            Page<OrderDTO> userIdPage = orderService.getOrdersByUserIdPaged(page, size, orderDTO.getUserId());
            return ResultDTO.success("根据用户ID查询成功", userIdPage);

        } catch (Exception e) {
            e.printStackTrace();
            return ResultDTO.error("根据用户ID查询失败：" + e.getMessage());
        }
    }

    @GetMapping("/getCaregiverIdOrder")
    public ResultDTO<IPage<Order>> getOrdersByCaregiverId(OrderDTO orderDTO) {
        try{
            Page<Order> getCaregiver = orderService.getOrderCaregiverId(orderDTO);
            return ResultDTO.success("根据阿姨id查询成功",  getCaregiver);
        }catch (Exception e){
            log.error("【根据阿姨id查询】出现异常，原因：", e);
            return ResultDTO.error("根据阿姨id查询订单失败" + e.getMessage());
        }
    }

    /**
     * 根据阿姨id，订单日期，查询订单信息
     */
    @PostMapping("/caregiversId")
    public ResultDTO<Page<OrderDTO>> getOrdersByCaregiverPaged(
            @RequestBody(required = false) @NotNull(message = "阿姨ID不能为空") OrderDTO orderDTO
    ) {
        try {
            if (orderDTO == null) orderDTO = new OrderDTO();

            Integer page = orderDTO.getPage() != null ? orderDTO.getPage() : 1;
            Integer size = orderDTO.getSize() != null ? orderDTO.getSize() : 10;

            Page<OrderDTO> orderPage = orderService.getOrdersByCaregiverPaged(
                    page,size,orderDTO.getCaregiversId(),orderDTO.getOrderDate()
            );
            if (orderPage.getRecords().isEmpty()) {
                return ResultDTO.notDataSuccess("当前阿姨没有订单！");
            }
            return ResultDTO.success("根据阿姨ID查询成功", orderPage);
        } catch (Exception e) {
            e.printStackTrace();
            return ResultDTO.error("根据阿姨ID查询失败：" + e.getMessage());
        }
    }

    // 查询今日订单：根据用户 ID
    @GetMapping("/list/todayCaregiversId")
    public ResultDTO<List<Order>> getTodayOrdersByUser(
            @RequestBody(required = false) @NotNull(message = "用户ID不能为空") OrderDTO orderDTO) {
        try {
            List<Order> orders = orderService.getTodayOrdersByUserId(orderDTO.getUserId());
            if (orders == null || orders.isEmpty()) {
                return ResultDTO.notDataSuccess("今天没有该用户信息，请确认用户Id！");
            }
            return ResultDTO.success("根据阿姨今天订单查询成功", orders);
        } catch (Exception e) {
            e.printStackTrace();
            return ResultDTO.error("根据用户今天订单查询失败" + e.getMessage());
        }
    }

    /**
     * 查询预约单
     */
    @GetMapping("/order/pending")
    public ResultDTO<?> listPendingOrders(@RequestParam Long caregiverId) {
        return orderService.listPendingOrders(caregiverId);
    }

    /**
     * 同意或拒绝预约单
     */
    @PostMapping("/order/decision")
    public ResultDTO<?> handleOrderDecision(@RequestBody OrderDTO dto) {
        return orderService.handleOrderDecision(dto);
    }

    @PostMapping("/released")
    public ResultDTO<Map<String, Object>> orderReleased(@Validated(value = OrderPayGroup.class) @RequestBody OrderDTO orderDTO) {

        ResultDTO<Map<String, Object>> releasedRes = orderService.orderReleased(orderDTO);
        return releasedRes;
    }

    @PutMapping("/user/refund/{orderId}")
    public ResultDTO<String> userRefundOrder(@PathVariable("orderId")
                                             @Pattern(regexp = "\\d+", message = "只能输入数字") String orderId) {
        try {
            ResultDTO<String> refundRes = orderService.refundOrder(orderId);
            return refundRes;
        } catch (Exception e) {
            log.error("退款失败", e);
            return ResultDTO.error("退款失败");
        }
    }

    @GetMapping("/refund/info")
    public ResultDTO<Map<String, Object>> getRefundInfo(@RequestParam String orderId) {
        try {
            ResultDTO<Map<String, Object>> refundInfo = orderService.getRefundInfo(orderId);
            return refundInfo;
        } catch (Exception e) {
            log.error("查询退款信息失败", e);
            return ResultDTO.error("查询退款信息失败");
        }
    }

    @PutMapping("/confirm/{orderId}")
    public ResultDTO<String> confirmOrder(@PathVariable("orderId")
                                          @Pattern(regexp = "\\d+", message = "只能输入数字") String orderId) {
        try {
            ResultDTO<String> confirmOrder = orderService.confirmOrder(orderId);
            return confirmOrder;
        } catch (Exception e) {
            log.error("确认订单失败", e);
            return ResultDTO.error(e.getMessage());
        }
    }

    @PostMapping("/wechat/pay/notify")
    public Map<String, Object> payNotify(HttpServletRequest request) {
        try {
            Map<String, Object> result = orderService.handleCallback(request);
            return result;
        } catch (Exception e) {
            HashMap<String, Object> resultMap = new HashMap<>();
            resultMap.put("code", "FAIL");
            resultMap.put("message", e.getMessage());
            log.error("回调失败", e);
            return resultMap;
        }
    }

    @PostMapping("/wechat/search/{orderNumber}")
    public ResultDTO<Map<String, Object>> searchWechatByOrderNumber(@PathVariable String orderNumber) {
        try {
            ResultDTO<Map<String, Object>> searchOrder = orderService.searchWechatByOrderNumber(orderNumber);
            return searchOrder;
        } catch (Exception e) {
            log.error("微信查单失败", e);
            return ResultDTO.error(e.getMessage());
        }
    }

    /**
     * 用户修改未被接单or预约单
     */
    @PutMapping("/updateOrderInfo")
    public ResultDTO<Map<String,Object>> updateOrderInfo(@RequestBody OrderDTO orderDTO) {
        try {
            // 0. 获取原订单
            OrderDTO order = orderService.getOrderById(orderDTO.getOrderId());
            if (order == null) {
                return ResultDTO.error("订单不存在");
            }

            // 1. 判断是否为未接单或已预约
            if (order.getStatus() != 0 && order.getStatus() != 1) {
                return ResultDTO.error("仅允许修改未接单或已预约的订单");
            }

            // 2. 判断是否为“原来的预约时间”临近15分钟
            if (order.getAppointmentTime() != null) {
                LocalDateTime now = LocalDateTime.now();
                LocalDateTime oldTime = order.getAppointmentTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
                Duration duration = Duration.between(now, oldTime);

                if (!duration.isNegative() && duration.toMinutes() <= 15) {
                    if (orderDTO.getConfirmModify() == null || !orderDTO.getConfirmModify()) {
                        return ResultDTO.error("订单开始前15分钟内修改将收取实付金额5%作为服务费，请勾选确认后再提交");
                    }
                }
            }

            // 5. 执行修改
            ResultDTO<Map<String,Object>> success = orderService.updateOrderInfo(orderDTO);
            if (success != null) {
                // 获取阿姨ID并发送提醒
                String caregiverId = order.getCaregiversId().toString(); // 获取阿姨ID
                HashMap<String, Object> payload = new HashMap<>();
                payload.put("orderId", order.getOrderId());
                Caregiver caregiver = caregiverMapper.selectById(caregiverId);
                // 向阿姨端发送消息
                webSocketServer.sendMessageToTarget(
                        "caregiver:" + caregiverId,  // 目标是阿姨
                        payload,  // 消息内容
                        WebSocketMessageType.EDIT_ORDER_RESPONSE.getType() // 消息类型
                );
                notificationUtil.sendNotificationToJPush(
                        CommonConstants.PUSH_UPDATE_ORDER_TITLE,
                        CommonConstants.PUSH_UPDATE_ORDER_ALERT,
                        AppType.CAREGIVER,
                        caregiver.getRegistrationId());

                return ResultDTO.success("修改成功", payload);
            } else {
                return ResultDTO.error("修改失败");
            }

        } catch (Exception e) {
            log.error("修改订单异常：", e);
            return ResultDTO.error("系统异常，请稍后重试");
        }
    }

    /**
     * 阿姨同意或拒绝用户修改订单
     */
    @PutMapping("/updateOrderStatus")
    public ResultDTO<Boolean> updateOrderStatus(@RequestBody OrderDTO orderDTO) {
        try {
            Boolean updateStatus = orderService.updateOrderStatus(orderDTO); // 调用服务层更新
            return ResultDTO.success("更新成功", updateStatus);
        } catch (Exception e) {
            log.error("更新失败，失败原因：", e);
            return ResultDTO.error("更新失败");
        }
    }

    /**
     * 阿姨取消订单
     */
    @PostMapping("/cancel")
    public ResultDTO<?> cancelOrderByCaregiver(@RequestBody OrderDTO dto) {
        return orderService.cancelOrderByCaregiver(dto);
    }

    // 缴纳押金和购买保险
    @PostMapping("/caregiver/buyInsurance")
    public ResultDTO<Boolean> buyInsurance(@RequestBody CaregiverServiceTypeDTO caregiverServiceTypeDTO) {
        System.out.println(caregiverServiceTypeDTO.toString());
        String loginIdAsString = StpUtil.getLoginIdAsString();
        LoginUserDTO loginUser = (LoginUserDTO) StpUtil.getSession().get(loginIdAsString);
        Long caregiverId = loginUser.getUserId();
        caregiverServiceTypeDTO.setCaregiverId(caregiverId);
        Boolean flag = orderService.buyInsurance(caregiverServiceTypeDTO);

        return ResultDTO.success("成功", flag);
    }

    @GetMapping("/caregiver/appointment")
    public ResultDTO<List<OrderDTO>> getAppointmentOrdersByCaregiver() {
        try {
            List<OrderDTO> orders = orderService.getAppointmentOrdersByCaregiver();
            return ResultDTO.success("查询成功", orders);
        } catch (Exception e) {
            log.error("获取预约订单失败", e);
            return ResultDTO.error("获取预约订单失败");
        }
    }

    @GetMapping("/caregiver/updateOrder")
    public ResultDTO<List<OrderDTO>> getUpdateOrderByCaregiver() {
        try{
            List<OrderDTO> orders = orderService.getUpdateOrderByCaregiver();
            return ResultDTO.success("查询成功", orders);
        }catch (Exception e){
            log.error("查询修改订单失败", e);
            return ResultDTO.error("查询修改订单失败");
        }
    }

    @GetMapping("/caregiver/cancelOrder")
    public ResultDTO<List<OrderDTO>> getCancelOrderByCaregiver() {
        try{
            List<OrderDTO> orders = orderService.getCancelOrderByCaregiver();
            return ResultDTO.success("查询成功", orders);
        }catch (Exception e){
            log.error("查询取消订单失败", e);
            return ResultDTO.error("查询取消订单失败");
        }
    }

    @GetMapping("/user/rejectAppointment")
    public ResultDTO<List<OrderDTO>> rejectAppointment() {
        try{
            List<OrderDTO> orders = orderService.getRejectAppointment();
            return ResultDTO.success("查询成功",orders);
        }catch (Exception e){
            log.error("查询拒绝预约单失败", e);
            return ResultDTO.error("查询拒绝预约单失败");
        }
    }

    @PutMapping("/confirmUserCancellation")
    public ResultDTO<Boolean> confirmUserCancellation(@RequestBody OrderDTO orderDTO) {
        try{
        Boolean booleans = orderService.UpConfirmUserCancellation(orderDTO);
        return ResultDTO.success("更新成功", booleans);
    } catch (Exception e) {
        log.error("更新失败，失败原因：", e);
        return ResultDTO.error("更新失败");
    }
}
}





