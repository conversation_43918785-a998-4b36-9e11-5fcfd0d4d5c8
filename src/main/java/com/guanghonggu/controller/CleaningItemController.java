package com.guanghonggu.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.guanghonggu.dto.CleaningItemDTO;
import com.guanghonggu.dto.ResultDTO;
import com.guanghonggu.service.CleaningItemService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @author：zzn
 * @description：
 * @createData：2025/7/3 9:34
 */
@Slf4j
@RestController
@RequestMapping("/cleaningItem")
public class CleaningItemController {

    @Autowired
    private CleaningItemService cleaningItemService;

    @GetMapping("/byCategoryId")
    public ResultDTO<IPage<CleaningItemDTO>> listByCategoryId(CleaningItemDTO cleaningItemDTO) {
        try {
            ResultDTO<IPage<CleaningItemDTO>> listResultDTO = cleaningItemService.listByCategoryId(cleaningItemDTO);
            return listResultDTO;
        } catch (Exception e) {
            log.error("获取失败", e);
            return ResultDTO.error("获取失败");
        }
    }

    @GetMapping("/byItemId")
    public ResultDTO<CleaningItemDTO> getCleaningItemById(String itemId) {
        try {
            ResultDTO<CleaningItemDTO> resultDTO = cleaningItemService.getCleaningItemById(itemId);
            return resultDTO;
        } catch (Exception e) {
            log.error("获取失败", e);
            return ResultDTO.error("获取失败");
        }
    }

}
