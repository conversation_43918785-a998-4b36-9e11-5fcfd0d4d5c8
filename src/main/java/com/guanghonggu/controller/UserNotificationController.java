package com.guanghonggu.controller;

import com.guanghonggu.dto.ResultDTO;
import com.guanghonggu.dto.UserNotificationDTO;
import com.guanghonggu.entity.UserNotification;
import com.guanghonggu.service.UserNotificationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/userNotification")
public class UserNotificationController {
    @Autowired
    private UserNotificationService userNotificationService;

    @GetMapping("/getUserNotification")
    public ResultDTO<List<UserNotification>> getUserNotification(@RequestBody UserNotificationDTO userNotificationDTO) {
       try{
           List<UserNotification> userNotification = userNotificationService.getUserNotification(userNotificationDTO);
           return ResultDTO.success("查询成功", userNotification);
       }catch (Exception e){
           e.printStackTrace();
           return ResultDTO.error("查询失败" + e.getMessage());
       }
    }
}
