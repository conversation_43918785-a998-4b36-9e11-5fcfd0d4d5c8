package com.guanghonggu.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.guanghonggu.chatbot.ChatProxyClient;
import com.guanghonggu.dto.MessageDTO;
import com.guanghonggu.dto.ResultDTO;
import com.guanghonggu.service.MessageService;

import org.springframework.beans.factory.annotation.Autowired;

import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;


@RestController
@RequestMapping("/message")
public class MessageController {

    @Autowired
    private MessageService messageService;

    @Autowired
    private ChatProxyClient chatProxyClient;


    // 获取系统消息提醒
    @GetMapping("/list")
    public ResultDTO<IPage<MessageDTO>> getMessages(Integer currentPage, Integer pageSize) {

        ResultDTO<IPage<MessageDTO>> messageList = messageService.getMessages(currentPage, pageSize);

        return messageList;
    }

    // 修改消息状态为已读
    @PutMapping("/{id}")
    public ResultDTO<String> updateStatus(@PathVariable Long id) {
        ResultDTO<String> resultDTO = messageService.updateStatus(id);

        return resultDTO;
    }


    // 联系客服机器人
    @PostMapping("/contact")
    public SseEmitter contact(@RequestBody MessageDTO messageDTO) {
//        System.out.println("query:" + messageDTO.getQuery());
        SseEmitter emitter = new SseEmitter(6000_000L);
        String sessionId = "1";
        chatProxyClient.chat(sessionId, messageDTO.getQuery(), emitter);

//        String contact = messageService.contact(messageDTO);

        return emitter;
    }


}
