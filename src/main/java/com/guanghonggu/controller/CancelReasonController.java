package com.guanghonggu.controller;

import com.guanghonggu.dto.CancelReasonDTO;
import com.guanghonggu.dto.ResultDTO;
import com.guanghonggu.service.CancelReasonService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/cancelReason")
public class CancelReasonController {

    @Autowired
    private CancelReasonService cancelReasonService;

    @GetMapping("/getCancelReason")
    public ResultDTO<List<CancelReasonDTO>> getCancelReason(@RequestParam("type") Integer type) {
        try{
            List<CancelReasonDTO> cancelReason = cancelReasonService.getCancelReasonList(type);
            return ResultDTO.success("查询成功", cancelReason);
        }catch (Exception e){
            e.printStackTrace();
            return ResultDTO.error("查询失败"+ e.getMessage());
        }
    }

}
