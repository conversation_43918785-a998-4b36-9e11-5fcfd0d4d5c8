package com.guanghonggu.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.guanghonggu.dto.CleaningItemShopOrderDTO;
import com.guanghonggu.dto.ResultDTO;
import com.guanghonggu.service.CleaningItemShopOrderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

/**
 * @author：zzn
 * @description：
 * @createData：2025/7/23 11:53
 */
@Slf4j
@RestController
@RequestMapping("/shopOrder")
public class CleaningItemShopOrderController {

    @Autowired
    private CleaningItemShopOrderService cleaningItemShopOrderService;


    @PostMapping("/place")
    public ResultDTO<Map<String, Object>> placeShopOrder(@RequestBody CleaningItemShopOrderDTO cleaningItemShopOrderDTO) {
        ResultDTO<Map<String, Object>> releasedRes = cleaningItemShopOrderService.placeShopOrder(cleaningItemShopOrderDTO);
        return releasedRes;
    }

    @PostMapping("/wechat/pay/notify")
    public Map<String, Object> payNotify(HttpServletRequest request) {
        try {
            Map<String, Object> result = cleaningItemShopOrderService.handleCallback(request);
            return result;
        } catch (Exception e) {
            HashMap<String, Object> resultMap = new HashMap<>();
            resultMap.put("code", "FAIL");
            resultMap.put("message", e.getMessage());
            log.error("回调失败", e);
            return resultMap;
        }
    }

    @GetMapping("/byCaregiver")
    public ResultDTO<IPage<CleaningItemShopOrderDTO>> listShopOrderByCaregiver(Integer currentPage, Integer pageSize) {
        return cleaningItemShopOrderService.listShopOrderByCaregiver(currentPage, pageSize);
    }
}
