package com.guanghonggu.controller;

import com.guanghonggu.dto.ResultDTO;
import com.guanghonggu.entity.CaregiverServiceType;
import com.guanghonggu.service.CaregiverServiceTypeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @author：zzn
 * @description：
 * @createData：2025/7/10 16:46
 */
@Slf4j
@RestController
@RequestMapping("/caregiverServiceType")
public class CaregiverServiceTypeController {

    @Autowired
    private CaregiverServiceTypeService caregiverServiceTypeService;

    @RequestMapping
    public ResultDTO<List<CaregiverServiceType>> getCaregiverServiceType() {
        try {
            ResultDTO<List<CaregiverServiceType>> listResultDTO = caregiverServiceTypeService.getCaregiverServiceType();
            return listResultDTO;
        } catch (Exception e) {
            log.error("获取失败", e);
            return ResultDTO.error("获取失败");
        }
    }

}
