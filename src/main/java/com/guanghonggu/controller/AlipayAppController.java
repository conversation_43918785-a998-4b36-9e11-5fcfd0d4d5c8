package com.guanghonggu.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.guanghonggu.dto.ResultDTO;
import com.guanghonggu.dto.WalletTransactionDTO;
import com.guanghonggu.entity.Order;
import com.guanghonggu.mapper.OrderMapper;
import com.guanghonggu.service.AlipayAppService;
import com.guanghonggu.service.impl.OrderServiceImpl;
import com.guanghonggu.strategy.pay.strategy.PayQueryHandler;
import com.guanghonggu.strategy.pay.alipay.AlipayUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * APP 端支付宝支付 & 退款 控制器
 */
@RestController
@RequestMapping("/apppay")
public class AlipayAppController {

    private static final Logger log = LoggerFactory.getLogger(AlipayAppController.class);

    @Autowired
    private OrderMapper orderMapper;

    @Autowired
    private OrderServiceImpl orderServiceImpl;

    @Autowired
    private PayQueryHandler payQueryHandler;

    @Autowired
    private AlipayAppService alipayAppService;

    private final AlipayUtil alipayUtil;

    public AlipayAppController(AlipayUtil alipayUtil) {
        this.alipayUtil = alipayUtil;
    }

    /**
     * 1. 发起 APP 支付
     */
//    @PostMapping("/app")
//    public ResultDTO<String> appPay(
//            @Validated(TradeDTO.PayGroup.class) @RequestBody TradeDTO dto
//    ) {
//        try {
//            String orderStr = alipayUtil.buildAppOrderStr(
//                    dto.getOrderNo(),
//                    dto.getAmount().toString(),
//                    dto.getSubject(),"2"
//            );
//            return ResultDTO.success("获取 orderStr 成功", orderStr);
//        } catch (AlipayApiException e) {
//            log.error("APP 支付失败", e);
//            return ResultDTO.error("APP 支付失败：" + e.getErrMsg());
//        }
//    }

    /**
     * 2. 支付异步通知（支付宝回调）
     */
    @PostMapping("/appnotify")
    public String notifyUrl(HttpServletRequest request) {
        try {
            Map<String, String> params = new HashMap<>();
            request.getParameterMap().forEach((k, v) ->
                    params.put(k, String.join(",", v))
            );

            // 统一使用工具类验签
            boolean valid = alipayUtil.checkSignature(params);
            if (!valid) {
                log.warn("支付异步通知验签失败");
                return "fail";
            }

            String outTradeNo = params.get("out_trade_no");
            String tradeStatus = params.get("trade_status");
            String totalAmount = params.get("total_amount");
            LambdaQueryWrapper<Order> orderLambdaQueryWrapper = new LambdaQueryWrapper<>();
            orderLambdaQueryWrapper.eq(Order::getOrderNumber, outTradeNo);
            Order order = orderMapper.selectOne(orderLambdaQueryWrapper);
//            Order order = orderService.getOrderByOrderNo(outTradeNo);
            if (order == null) {
                log.error("订单不存在，订单号: {}", outTradeNo);
                return "failure";
            }

            // 幂等校验：避免重复更新
            if (order.getStatus() != null && order.getStatus() == 1) {
                log.info("订单已处理过，无需重复处理，订单号: {}", outTradeNo);
                return "success";
            }

            if ("TRADE_SUCCESS".equals(tradeStatus)) {

//                order.setStatus(1); // 1 = 已支付
//                order.setUpdateTime(new Date());
//                orderMapper.updateById(order);
//                log.info("订单状态已更新为已支付，订单号: {}", outTradeNo);
            } else if ("TRADE_FINISHED".equals(tradeStatus)) {
                orderServiceImpl.updateOrderPayment(outTradeNo, null, null, 4);
                orderServiceImpl.updateWalletTransaction(outTradeNo, null, new BigDecimal(totalAmount), 2);
            }

            return "success";
        } catch (Exception e) {
            log.error("支付通知处理异常", e);
            return "fail";
        }
    }

    /**
     * 3. 查询订单支付状态
     */
    @PostMapping("/query")
    public ResultDTO<Map<String, Object>> searchAlipayByOrderId(String orderNumber) {
        try {
            ResultDTO<Map<String, Object>> mapResultDTO = alipayAppService.searchAlipayByOrderId(orderNumber);
            return mapResultDTO;
        } catch (Exception e) {
            log.error("支付宝查单失败", e);
            return ResultDTO.error("支付宝查单失败");
        }
    }

    /**
     * 支付宝异步通知（充值成功后回调）
     */
    @PostMapping("/rechargeNotify")
    public String handleRechargeNotify(HttpServletRequest request) {
        System.out.println("【收到支付宝回调】");
        return alipayAppService.handleRechargeNotify(request);
    }

    @PostMapping("/withdraw")
    public ResultDTO<?> withdraw(@RequestBody WalletTransactionDTO dto) {
        return alipayAppService.withdraw(dto);
    }
}