package com.guanghonggu.controller;

import com.guanghonggu.dto.ResultDTO;
import com.guanghonggu.entity.AppVersion;
import com.guanghonggu.service.AppVersionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @author：zzn
 * @description：
 * @createData：2025/8/1 10:53
 */
@RestController
@RequestMapping("/appVersion")
public class AppVersionController {

    @Autowired
    private AppVersionService appVersionService;

    @GetMapping("/latest")
    public ResultDTO<AppVersion> getLatestVersion(String platform,Integer appType) {
        return appVersionService.getLatestVersion(platform, appType);
    }
}
