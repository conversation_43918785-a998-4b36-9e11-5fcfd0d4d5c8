package com.guanghonggu.controller;

import cn.dev33.satoken.stp.StpUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.guanghonggu.dto.*;
import com.guanghonggu.entity.Caregiver;
import com.guanghonggu.entity.SysPicture;
import com.guanghonggu.entity.Wallet;
import com.guanghonggu.mapper.CaregiverMapper;
import com.guanghonggu.mapper.SysPictureMapper;
import com.guanghonggu.mapper.WalletMapper;
import com.guanghonggu.service.AliyunDypnsService;
import com.guanghonggu.service.CaregiverService;
import com.guanghonggu.service.RefundDecisionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.Date;

import com.guanghonggu.dto.CaregiverDTO;
import com.guanghonggu.dto.ResultDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * @author：zzn
 * @Description：阿姨
 * @createData：2025/4/27 11:11
 */
@Slf4j
@RestController
@RequestMapping("/caregiver")
public class CaregiverController {
    @Autowired
    private CaregiverService caregiverService;

    @Autowired
    private RefundDecisionService refundDecisionService;


    @Autowired
    private AliyunDypnsService aliyunDypnsService;

    @Value("${wx.app.appId}")
    private String appid;

    @Value("${wx.app.appSecret}")
    private String appSecret;

    @Value("${wx.redirectUri}")
    private String redirectUri;

    @Autowired
    private WalletMapper walletMapper;

    @Autowired
    private SysPictureMapper sysPictureMapper;
    @Autowired
    private CaregiverMapper caregiverMapper;


    // 阿姨端登录
    @PostMapping("/login")
    public ResultDTO<String> login(@RequestBody LoginDTO loginDTO) {

        System.out.println("LoginDTO" + loginDTO.getPhoneNumber());
        Caregiver caregiver = caregiverService.selectByPhone(loginDTO.getPhoneNumber());
        System.out.println("caregiver" + caregiver);
        System.out.println("caregiver" + caregiver);

        if (caregiver != null) {
            Long userId = caregiver.getCaregiverId();
            StpUtil.login(caregiver.getPhoneNumber());
            LoginUserDTO loginUserDTO = new LoginUserDTO();
            loginUserDTO.setPhone(caregiver.getPhoneNumber());
            loginUserDTO.setUserId(userId);
            loginUserDTO.setUserType(2);
            StpUtil.getSession().set(loginUserDTO.getPhone(), loginUserDTO);

            return ResultDTO.success("登录成功", StpUtil.getTokenValue());
        } else {
            return ResultDTO.error("登录失败");
        }
    }


    // 阿姨端一键登录 - APP
    @PostMapping("/get-mobile")
    public ResultDTO<LoginDTO> getMobile(@RequestBody MobileDTO mobileDTO) {
        try {
            String token = mobileDTO.getToken();
            String phoneNumber = aliyunDypnsService.getMobileByToken(token);

            Caregiver caregiver = caregiverService.selectByPhone(phoneNumber);

            if (caregiver == null) {
                LambdaQueryWrapper<SysPicture> sysPictureLambdaQueryWrapper = new LambdaQueryWrapper<>();
                sysPictureLambdaQueryWrapper.eq(SysPicture::getPictureType, 2);
                SysPicture sysPicture = sysPictureMapper.selectOne(sysPictureLambdaQueryWrapper);

                caregiver = new Caregiver();
                caregiver.setPhoneNumber(phoneNumber);
                caregiver.setStatus(0);
                caregiver.setExamStatus(0);
                caregiver.setAvgEvaluationScore(5.0);
                caregiver.setIdCardVerification(0);
                caregiver.setHealthCertificateVerification(0);
                caregiver.setName("用户" + phoneNumber.substring(phoneNumber.length() - 4));
                caregiver.setAvatar(sysPicture.getDownloadAddress());
                caregiver.setRegistrationTime(new Date());
                caregiver.setRegistrationId(mobileDTO.getRegistrationId());
                caregiverService.insertCaregivers(caregiver);

                Wallet wallet = new Wallet();
                wallet.setUserId(caregiver.getCaregiverId());
                wallet.setUserType(2);
                wallet.setBalance(new BigDecimal(0));
                wallet.setRechargeBalance(new BigDecimal(0));
                wallet.setTotalIncome(new BigDecimal(0));
                wallet.setTotalSpent(new BigDecimal(0));
                walletMapper.insert(wallet);
            } else {
                Caregiver updateCaregiver = new Caregiver();
                updateCaregiver.setCaregiverId(caregiver.getCaregiverId());
                updateCaregiver.setRegistrationId(mobileDTO.getRegistrationId());
                caregiverMapper.updateById(updateCaregiver);
            }

            Long caregiverId = caregiver.getCaregiverId();
            StpUtil.login("caregiver:" + caregiverId);
            LoginUserDTO loginUserDTO = new LoginUserDTO();
            loginUserDTO.setPhone(caregiver.getPhoneNumber());
            loginUserDTO.setUserId(caregiverId);
            loginUserDTO.setUserType(2);
            StpUtil.getSession().set("caregiver:" + caregiverId, loginUserDTO);

            LoginDTO loginDTOs = new LoginDTO();
            loginDTOs.setCaregiverId(caregiverId);
            loginDTOs.setPhoneNumber(caregiver.getPhoneNumber());
            loginDTOs.setToken(StpUtil.getTokenValue());
            loginDTOs.setAvatar(caregiver.getAvatar());
            return ResultDTO.success("登录成功", loginDTOs);

        } catch (Exception e) {
            log.error("一键登录失败", e);
            return ResultDTO.error("一键登录失败");
        }

    }


    // 跳转到微信授权页面
    @GetMapping("/wx-auth")
    public ResultDTO<String> wxAuth() {
        String url = "https://open.weixin.qq.com/connect/oauth2" +
                "/authorize?appid=" + appid + "&redirect_uri=" + redirectUri + "&response_type=code&scope=snsapi_base&state=STATE#wechat_redirect";
        System.out.println(url);
        return ResultDTO.success("登录成功", url);
    }


    // 测试内网穿透
    // 微信登录回调接口
    @GetMapping("/wx-callback")
    public ResultDTO<String> wxCallback(@RequestParam("code") String code) {
        ResultDTO<String> res = caregiverService.wxLoginCallback(code);
        return res;

    }

    /**
     * 根据阿姨id查询阿姨信息
     */
    @PostMapping("/getCaregiver")
    public ResultDTO<CaregiverDTO> getCaregiver(@RequestBody CaregiverDTO caregiverDTO) {
        try {
            CaregiverDTO caregiver = caregiverService.getCaregiver(caregiverDTO);
            return ResultDTO.success("查询阿姨信息成功", caregiver);
        } catch (Exception e) {
            e.printStackTrace();
            return ResultDTO.error("查询阿姨信息失败" + e.getMessage());
        }
    }


//    /**
//     * 阿姨接受/拒绝用户退退款
//     */
//    @PostMapping("/refund/decision")
//    public ResultDTO<?> decideRefund(@RequestBody RefundDecisionDTO dto) {
//        try {
//            return refundDecisionService.decideRefund(dto);
//        } catch (Exception e) {
//            e.printStackTrace();
//            return ResultDTO.error("阿姨处理退款失败: " + e.getMessage());
//        }
//    }

    /**
     * 查询阿姨总数量
     */
    @GetMapping("/total")
    public ResultDTO<Long> getTotal() {
        try {
            long count = caregiverService.count();
            return ResultDTO.success("查询成功", count);
        } catch (Exception e) {
            e.printStackTrace();
            return ResultDTO.error("查询失败" + e.getMessage());
        }
    }

    @PutMapping("/examStatus")
    public ResultDTO<String> updateCaregiverExamStatus() {
        try {
            ResultDTO<String> res = caregiverService.updateCaregiverExamStatus();
            return res;
        } catch (Exception e) {
            log.error("更新状态失败", e);
            return ResultDTO.error("更新状态失败");
        }
    }

    @PostMapping("/information")
    public ResultDTO<String> uploadCaregiverInformation(@Valid @ModelAttribute CaregiverDTO dto) {
        try {

            ResultDTO<String> res = caregiverService.uploadCaregiverInformation(dto);
            return res;
        } catch (Exception e) {
            log.error("上传审核资料失败", e);
            return ResultDTO.error("上传审核资料失败");
        }
    }

    @GetMapping("/getPassword")
    public ResultDTO<Long> getPassword(@RequestBody(required = false) CaregiverDTO caregiverDTO) {
        // 调用服务层的方法获取密码
        return caregiverService.getPassword(caregiverDTO);
    }

    /**
     * 设置支付密码（第一次设置时调用）
     */
    @PostMapping("/setPassword")
    public ResultDTO<?> setPayPassword(@RequestBody(required = false) CaregiverDTO caregiverDTO) {
        try {
            if (!caregiverDTO.getPayPassword().matches("^\\d{6}$")) {
                return ResultDTO.error("密码格式错误，必须是6位数字");
            }
            // 调用 Service 层的设置密码方法
            int result = caregiverService.setPayPassword(caregiverDTO);

            // 如果设置成功，返回成功提示；否则返回失败提示
            return ResultDTO.success("密码设置成功",result);
        } catch (Exception e) {
            log.error("错误", e);
            return ResultDTO.error("设置失败：" + e.getMessage());
        }
    }

    /**
     * 修改支付密码（需要输入旧密码验证）
     */
    @PostMapping("/updatePassword")
    public ResultDTO<?> updatePassword(@RequestBody CaregiverDTO caregiverDTO) {
        try {
            // 调用 Service 层的业务逻辑
            int result = caregiverService.updatePayPassword(caregiverDTO);

            // 根据返回的结果，返回成功或失败的提示信息
            if (result > 0) {
                return ResultDTO.success("支付密码修改成功",result);
            } else {
                return ResultDTO.error("支付密码修改失败");
            }
        } catch (Exception e) {
            return ResultDTO.error("修改支付密码失败: " + e.getMessage());
        }
    }

}
