package com.guanghonggu.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.guanghonggu.dto.ResultDTO;
import com.guanghonggu.dto.WalletTransactionDTO;
import com.guanghonggu.service.WalletTransactionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @author：zzn
 * @Description：钱包交易记录
 * @createData：2025/4/27 11:23
 */
@Slf4j
@RestController
@RequestMapping("/walletTransaction")
public class WalletTransactionController {

    @Autowired
    private WalletTransactionService walletTransactionService;

    @PostMapping
    public ResultDTO<IPage<WalletTransactionDTO>> listWalletTransaction(@RequestBody WalletTransactionDTO walletTransactionDTO) {
        try {
            ResultDTO<IPage<WalletTransactionDTO>> walletTransactionDTOList = walletTransactionService.listWalletTransaction(walletTransactionDTO);
            return walletTransactionDTOList;
        } catch (Exception e) {
            log.error("获取交易记录列表失败", e);
            return ResultDTO.error("获取交易记录列表失败");
        }
    }
}
