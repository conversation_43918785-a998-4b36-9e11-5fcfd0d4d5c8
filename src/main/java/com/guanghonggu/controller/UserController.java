package com.guanghonggu.controller;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.stp.StpUtil;
import com.guanghonggu.dto.*;
import com.guanghonggu.entity.User;
import com.guanghonggu.service.*;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import java.util.Date;
import java.util.Map;
import java.util.Random;


/**
 * @author：zzn
 * @Description：用户
 * @createData：2025/4/27 11:20
 */
@Slf4j
@RestController
@RequestMapping("/user")
public class UserController {

    @Autowired
    private UserService userService;

    @Autowired
    private WxPhoneLoginService wxPhoneLoginService;

    @Autowired
    private SmsService smsService;


    @Value("${wx.app.appId}")
    private String appid;

    @Value("${wx.app.appSecret}")
    private String appSecret;

    @Value("${wx.redirectUri}")
    private String redirectUri;


    @PostMapping("/login")
    public void login(@RequestBody LoginUserDTO loginUserDTO) {
        loginUserDTO.setUserName("test");
        loginUserDTO.setUserType(1);
        loginUserDTO.setUserId(7L);
        loginUserDTO.setPhone(loginUserDTO.getPhone());
        if (loginUserDTO.getUserType() == 1) {
            StpUtil.login("user:" + loginUserDTO.getUserId());
            StpUtil.getSession().set("user:" + loginUserDTO.getUserId(), loginUserDTO);
        }else {
            StpUtil.login("caregiver:" + loginUserDTO.getUserId());
            StpUtil.getSession().set("caregiver:" + loginUserDTO.getUserId(), loginUserDTO);
        }

        String loginId = StpUtil.getLoginIdAsString();
        LoginUserDTO loginInfo = (LoginUserDTO) StpUtil.getSession().get(loginId);
        log.info("loginInfo:{}" , loginInfo);
    }

    //用户端一键登录
    @PostMapping("/get-mobile")
    public ResultDTO<LoginDTO> getMobile(@RequestBody MobileDTO mobileDTO) {
        ResultDTO<LoginDTO> loginDTO = userService.getMobile(mobileDTO);
        return loginDTO;

    }

    // 跳转到微信授权页面
    @GetMapping("/wx-auth")
    public ResultDTO<String> wxAuth() {
        String url = "https://open.weixin.qq.com/connect/oauth2" +
                "/authorize?appid=" + appid + "&redirect_uri=" + redirectUri + "&response_type=code&scope=snsapi_base&state=STATE#wechat_redirect";
        System.out.println(url);
        return ResultDTO.success("登录成功", url);
    }


    // 微信登录回调接口
    @GetMapping("/wx-callback")
    public ResultDTO<String> wxCallback(@RequestParam("code") String code) {

        ResultDTO<String> res = userService.wxLoginCallback(code);
        return res;
    }

    /**
     * 发送验证码
     */
    @PostMapping("/sendVerificationCode")
    public ResultDTO<Void> sendVerificationCode(@RequestBody UserDTO userDTO) {
        String phoneNumber = userDTO.getPhoneNumber();
        String verificationCode = generateVerificationCode();  // 生成随机验证码

        // 发送短信验证码
        boolean success = smsService.sendSms(phoneNumber, verificationCode);

        if (success) {
            return ResultDTO.success("验证码发送成功", null);
        } else {
            return ResultDTO.error("验证码发送失败");
        }
    }

    /**
     * 验证短信验证码
     */
    @PostMapping("/verify")
    public ResultDTO<Void> verifySmsCode(@RequestBody UserDTO userDTO) {

        boolean isValid = smsService.verifySmsCode(userDTO);
        if (isValid) {
            return ResultDTO.success("验证码验证成功",null);
        } else {
            return ResultDTO.error("验证码验证失败");
        }
    }

    private String generateVerificationCode() {
        return String.format("%06d", new Random().nextInt(999999));  // 生成六位数验证码
    }

    /**
     * 修改支付密码，内部使用
     */
    @PutMapping("putPassword")
    public ResultDTO<Long> putPassword(@RequestBody(required = false) UserDTO userDTO) {
        try{
            if (userDTO == null) {
                userDTO = new UserDTO();
            }
            Long userId = userDTO.getUserId();

            userDTO.setUserId(userId);
            int result = userService.putPassword(userDTO);
            if (result > 0) {
                return ResultDTO.success("更新成功", userId);
            }else{
                return ResultDTO.error("修改失败，未找到id");
            }
        }catch (Exception e){
            e.printStackTrace();
            return ResultDTO.error("修改成功" + e.getMessage());
        }
    }


    /**
     * 小程序登录
     */
    @PostMapping("/phoneLogin")
    public ResultDTO<?> phoneLogin(@RequestBody PhoneLoginDTO dto) {
        try {
            return wxPhoneLoginService.loginWithWeChat(dto);
        } catch (Exception e) {
            return ResultDTO.error("登录失败：" + e.getMessage());
        }
    }

    /**
     * 小程序发起微信支付
     */
    @SaCheckLogin
    @PostMapping("/wxpay/createJsapiPay")
    public ResultDTO<?> wxPay(@RequestBody PhoneLoginDTO dto) {
        Object loginId = StpUtil.getLoginIdDefaultNull();
        System.out.println("当前登录用户ID: " + loginId);
        try {
            Map<String, String> payParams = wxPhoneLoginService.createWxPay(dto);
            return ResultDTO.success("统一下单成功", payParams);
        } catch (Exception e) {
            System.out.println("【支付参数打印】");
            System.out.println("code: " + dto.getCode());
            System.out.println("body: " + dto.getBody());
            System.out.println("outTradeNo: " + dto.getOutTradeNo());
            System.out.println("totalFee: " + dto.getTotalFee());
            return ResultDTO.error("支付失败：" + e.getMessage());
        }
    }

    @PutMapping("/registrationId")
    public ResultDTO<String> updateRegistrationId(UserDTO userDTO) {
        ResultDTO<String> res = userService.updateRegistrationId(userDTO);
        return res;
    }

}
