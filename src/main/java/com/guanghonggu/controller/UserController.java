package com.guanghonggu.controller;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.stp.StpUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.guanghonggu.dto.*;
import com.guanghonggu.entity.SysPicture;
import com.guanghonggu.entity.User;
import com.guanghonggu.entity.Wallet;
import com.guanghonggu.mapper.SysPictureMapper;
import com.guanghonggu.mapper.UserMapper;
import com.guanghonggu.mapper.WalletMapper;
import com.guanghonggu.service.*;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Map;
import java.util.Random;


/**
 * @author：zzn
 * @Description：用户
 * @createData：2025/4/27 11:20
 */
@Slf4j
@RestController
@RequestMapping("/user")
public class UserController {

    @Autowired
    private UserService userService;

    @Autowired
    private WxPhoneLoginService wxPhoneLoginService;

    @Autowired
    private SmsService smsService;


    @Value("${wx.app.appId}")
    private String appid;

    @Value("${wx.app.appSecret}")
    private String appSecret;

    @Value("${wx.redirectUri}")
    private String redirectUri;
    @Autowired
    private SysPictureMapper sysPictureMapper;
    @Autowired
    private UserMapper userMapper;
    @Autowired
    private WalletMapper walletMapper;
    @Autowired
    private CouponService couponService;


    @PostMapping("/login")
    public void login(@RequestBody LoginUserDTO loginUserDTO) {
        loginUserDTO.setUserName("test");
        loginUserDTO.setUserType(2);
        loginUserDTO.setUserId(1L);
        loginUserDTO.setPhone(loginUserDTO.getPhone());
        if (loginUserDTO.getUserType() == 1) {
            StpUtil.login("user:" + loginUserDTO.getUserId());
            StpUtil.getSession().set("user:" + loginUserDTO.getUserId(), loginUserDTO);
        }else {
            StpUtil.login("caregiver:" + loginUserDTO.getUserId());
            StpUtil.getSession().set("caregiver:" + loginUserDTO.getUserId(), loginUserDTO);
        }

        String loginId = StpUtil.getLoginIdAsString();
        LoginUserDTO loginInfo = (LoginUserDTO) StpUtil.getSession().get(loginId);
        log.info("loginInfo:{}" , loginInfo);
    }

    //用户端一键登录
    @PostMapping("/get-mobile")
    public ResultDTO<LoginDTO> getMobile(@RequestBody MobileDTO mobileDTO) {
        ResultDTO<LoginDTO> loginDTO = userService.getMobile(mobileDTO);
        return loginDTO;

    }

    /**
     * 用户端短信验证码登录
     */
    @PostMapping("/loginWithSms")
    @Transactional(rollbackFor = Exception.class)
    public ResultDTO<LoginDTO> loginWithSms(@RequestBody LoginUserDTO loginUserDTO) {
        try{
            if (loginUserDTO.getPhone() == null || loginUserDTO.getVerificationCode() == null) {
                return ResultDTO.error("手机号或验证码不能为空");
            }

            if (!smsService.verifySmsCodeForLogin(loginUserDTO.getPhone(), loginUserDTO.getVerificationCode())) {
                return ResultDTO.error("验证码错误或已过期");
            }

            String phoneNumber = loginUserDTO.getPhone();
            User user = userService.selectByPhone(phoneNumber);
            int isNewUser = 0;

            if (user == null) {
                isNewUser = 1;
                user = new User();
                user.setPhoneNumber(phoneNumber);
                user.setRegistrationTime(new Date());
                user.setNickname("用户" + phoneNumber.substring(phoneNumber.length() - 4));

                LambdaQueryWrapper<SysPicture> sysPictureLambdaQueryWrapper = new LambdaQueryWrapper<>();
                sysPictureLambdaQueryWrapper.eq(SysPicture::getPictureType, 1);
                SysPicture sysPicture = sysPictureMapper.selectOne(sysPictureLambdaQueryWrapper);

                user.setAvatar(sysPicture.getDownloadAddress());
                user.setRegistrationId(loginUserDTO.getRegistrationId());
                userMapper.insert(user);

                //创建钱包
                Wallet wallet = new Wallet();
                wallet.setUserId(user.getUserId());
                wallet.setUserType(1);
                wallet.setBalance(new BigDecimal(0));
                wallet.setRechargeBalance(new BigDecimal(0));
                wallet.setTotalIncome(new BigDecimal(0));
                wallet.setTotalSpent(new BigDecimal(0));
                walletMapper.insert(wallet);

                //发放优惠券
                couponService.issueNewUserCoupon(user.getUserId());
            }else {
                //更新注册id
                User updateUser = new User();
                updateUser.setUserId(user.getUserId());
                updateUser.setRegistrationId(loginUserDTO.getRegistrationId());
                userMapper.updateById(updateUser);
            }

            //登录并设置session
            StpUtil.login("user:" + user.getUserId());
            loginUserDTO.setUserId(user.getUserId());
            loginUserDTO.setUserType(1);
            loginUserDTO.setPhone(phoneNumber);
            StpUtil.getSession().set("user:" + user.getUserId(), loginUserDTO);

            //返回登录结果
            LoginDTO loginDTO = new LoginDTO();
            loginDTO.setIsNewUser(isNewUser);
            loginDTO.setUserId(user.getUserId());
            loginDTO.setPhoneNumber(phoneNumber);
            loginDTO.setToken(StpUtil.getTokenValue());
            loginDTO.setAvatar(user.getAvatar());
            return ResultDTO.success("登录成功", loginDTO);
        }catch (Exception e){
            log.error("用户端验证码登录异常",e);
            return  ResultDTO.error("登陆失败");
        }
    }

    // 跳转到微信授权页面
    @GetMapping("/wx-auth")
    public ResultDTO<String> wxAuth() {
        String url = "https://open.weixin.qq.com/connect/oauth2" +
                "/authorize?appid=" + appid + "&redirect_uri=" + redirectUri + "&response_type=code&scope=snsapi_base&state=STATE#wechat_redirect";
        System.out.println(url);
        return ResultDTO.success("登录成功", url);
    }


    // 微信登录回调接口
    @GetMapping("/wx-callback")
    public ResultDTO<String> wxCallback(@RequestParam("code") String code) {

        ResultDTO<String> res = userService.wxLoginCallback(code);
        return res;
    }

    /**
     * 发送验证码
     */
    @PostMapping("/sendVerificationCode")
    public ResultDTO<Void> sendVerificationCode(@RequestBody UserDTO userDTO) {
        String phoneNumber = userDTO.getPhoneNumber();
        String verificationCode = generateVerificationCode();  // 生成随机验证码

        // 发送短信验证码
        boolean success = smsService.sendSms(phoneNumber, verificationCode);

        if (success) {
            return ResultDTO.success("验证码发送成功", null);
        } else {
            return ResultDTO.error("验证码发送失败");
        }
    }

    /**
     * 验证短信验证码
     */
    @PostMapping("/verify")
    public ResultDTO<Void> verifySmsCode(@RequestBody UserDTO userDTO) {

        boolean isValid = smsService.verifySmsCode(userDTO);
        if (isValid) {
            return ResultDTO.success("验证码验证成功",null);
        } else {
            return ResultDTO.error("验证码验证失败");
        }
    }

    private String generateVerificationCode() {
        return String.format("%06d", new Random().nextInt(999999));  // 生成六位数验证码
    }

    /**
     * 修改支付密码，内部使用
     */
    @PutMapping("putPassword")
    public ResultDTO<Long> putPassword(@RequestBody(required = false) UserDTO userDTO) {
        try{
            if (userDTO == null) {
                userDTO = new UserDTO();
            }
            Long userId = userDTO.getUserId();

            userDTO.setUserId(userId);
            int result = userService.putPassword(userDTO);
            if (result > 0) {
                return ResultDTO.success("更新成功", userId);
            }else{
                return ResultDTO.error("修改失败，未找到id");
            }
        }catch (Exception e){
            e.printStackTrace();
            return ResultDTO.error("修改成功" + e.getMessage());
        }
    }

    /**
     * 用户登录发送验证码
     */
    @PostMapping("/userSendLoginSms")
    public ResultDTO<String> userSendLoginSms(@RequestBody LoginUserDTO loginUserDTO) {
        try {
            String phoneNumber = loginUserDTO.getPhone();
            // 生成6位数字验证码
            String verificationCode = generateVerificationCode();

            boolean success = smsService.sendLoginSms(phoneNumber, verificationCode);
            if (success) {
                return ResultDTO.success("验证码发送成功",null);
            } else {
                return ResultDTO.error("验证码发送失败");
            }
        } catch (Exception e) {
            return ResultDTO.error(e.getMessage());
        }
    }

    /**
     * 阿姨端发送验证码登录
     */
    @PostMapping("/caregiverSendLoginSms")
    public ResultDTO<String> caregiverSendLoginSms(@RequestBody LoginUserDTO loginUserDTO) {
        try {
            String phoneNumber = loginUserDTO.getPhone();
            String verificationCode = generateVerificationCode();
            boolean success = smsService.sendLoginSms(phoneNumber, verificationCode);
            if (success) {
                return ResultDTO.success("验证码发送成功",null);
            } else {
                return ResultDTO.error("验证码发送失败");
            }
        }catch (Exception e){
            log.error("发送验证码异常",e);
            return ResultDTO.error("发送验证码失败");
        }
    }


    /**
     * 小程序登录
     */
    @PostMapping("/phoneLogin")
    public ResultDTO<?> phoneLogin(@RequestBody PhoneLoginDTO dto) {
        try {
            return wxPhoneLoginService.loginWithWeChat(dto);
        } catch (Exception e) {
            return ResultDTO.error("登录失败：" + e.getMessage());
        }
    }

    @PutMapping("/registrationId")
    public ResultDTO<String> updateRegistrationId(UserDTO userDTO) {
        ResultDTO<String> res = userService.updateRegistrationId(userDTO);
        return res;
    }

}
