package com.guanghonggu.controller;

import com.guanghonggu.dto.ResultDTO;
import com.guanghonggu.entity.CleaningCategory;
import com.guanghonggu.service.CleaningCategoryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @author：zzn
 * @description：
 * @createData：2025/7/3 9:33
 */
@Slf4j
@RestController
@RequestMapping("/cleaningCategory")
public class CleaningCategoryController {

    @Autowired
    private CleaningCategoryService cleaningCategoryService;

    @GetMapping("/list")
    public ResultDTO<List<CleaningCategory>> listAllCategories() {
        try {
            ResultDTO<List<CleaningCategory>> resultDTO = cleaningCategoryService.listAllCategories();
            return resultDTO;
        } catch (Exception e) {
            log.error("获取分类失败", e);
            return ResultDTO.error("获取分类失败");
        }
    }

}
