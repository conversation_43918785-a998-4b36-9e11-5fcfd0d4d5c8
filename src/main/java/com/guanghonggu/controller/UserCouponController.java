package com.guanghonggu.controller;

import com.guanghonggu.dto.ResultDTO;
import com.guanghonggu.dto.UserCouponDTO;
import com.guanghonggu.service.UserCouponService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @author：zzn
 * @Description：用户优惠券
 * @createData：2025/4/27 11:22
 */
@Slf4j
@RestController
@RequestMapping("/userCoupon")
public class UserCouponController {
    @Autowired
    private UserCouponService userCouponService;

    /**
     * 老用户邀请新用户下发优惠券
     */
    @PostMapping("/referral/reward")
    public ResultDTO<UserCouponDTO> rewardInviterForNewUser(@RequestBody UserCouponDTO userCouponDTO) {
        try{
            ResultDTO<UserCouponDTO> userCouponDTOResultDTO = userCouponService.grantRandomUserCoupon(userCouponDTO);
            return userCouponDTOResultDTO;
        }catch (Exception e){
            log.error("发放优惠券失败", e);
            return ResultDTO.error("发放优惠券失败");
        }
    }
}
