//package com.guanghonggu.controller;
//
//import com.guanghonggu.service.QywxRobotMiniLinkService;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.web.bind.annotation.PostMapping;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RequestParam;
//import org.springframework.web.bind.annotation.RestController;
//
//
//@RestController
//@RequestMapping("/apiQywxRobot")
//public class QywxRobotMiniLinkController {
//    @Autowired
//    private QywxRobotMiniLinkService qywxRobotMiniLinkService;
//
//    @PostMapping("send-mini-link")
//    public String sendMiniLink(@RequestParam String webhook,
//                               @RequestParam String pagepath,
//                               @RequestParam(required = false) String title,
//                                @RequestParam(required = false) Integer expireDays) throws Exception{
//        return qywxRobotMiniLinkService.sendMiniLinkByRoBot(webhook,pagepath,title,expireDays);
//
//    }
//}
