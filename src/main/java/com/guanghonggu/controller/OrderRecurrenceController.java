package com.guanghonggu.controller;

import com.guanghonggu.dto.OrderRecurrenceDTO;
import com.guanghonggu.dto.ResultDTO;
import com.guanghonggu.service.OrderRecurrenceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @author：zzn
 * @description：
 * @createData：2025/8/18 16:14
 */
@Slf4j
@RestController
@RequestMapping("/orderRecurrence")
public class OrderRecurrenceController {

    @Autowired
    private OrderRecurrenceService orderRecurrenceService;


    @PutMapping
    public ResultDTO<String> updateOrderRecurrence(@RequestBody OrderRecurrenceDTO orderRecurrenceDTO) {
        return orderRecurrenceService.updateOrderRecurrence(orderRecurrenceDTO);
    }

    @PutMapping("/cancel")
    public ResultDTO<String> cancelOrderRecurrence(@RequestBody OrderRecurrenceDTO orderRecurrenceDTO) {
        return orderRecurrenceService.cancelOrderRecurrence(orderRecurrenceDTO);
    }

}
