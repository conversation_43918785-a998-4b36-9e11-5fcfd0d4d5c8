package com.guanghonggu.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.guanghonggu.dto.ResultDTO;
import com.guanghonggu.dto.ToDoRemindersDTO;
import com.guanghonggu.service.ToDoRemindersService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @author：zzn
 * @Description：待办提醒
 * @createData：2025/4/27 11:19
 */
@Slf4j
@RestController
@RequestMapping("/toDoReminders")
public class ToDoRemindersController {

    @Autowired
    private ToDoRemindersService toDoRemindersService;

    @GetMapping("/list")
    public ResultDTO<IPage<ToDoRemindersDTO>> listTodoReminders(ToDoRemindersDTO toDoRemindersDTO) {
        try {
            ResultDTO<IPage<ToDoRemindersDTO>> todoReminderList = toDoRemindersService.listTodoReminders(toDoRemindersDTO);
            return todoReminderList;
        } catch (Exception e) {
            log.error("获取待办列表失败", e);
            return ResultDTO.error("获取待办列表失败");
        }
    }

    @PutMapping("/status/{remindersId}")
    public ResultDTO<String> updateTodoRemindersStatus(@PathVariable String remindersId) {
        try {
            ResultDTO<String> updateRes = toDoRemindersService.updateTodoRemindersStatus(remindersId);
            return updateRes;
        } catch (Exception e) {
            log.error("更新待办状态失败", e);
            return ResultDTO.error("更新待办状态失败");
        }
    }

    @DeleteMapping("/{remindersId}")
    public ResultDTO<String> deleteTodoReminders(@PathVariable String remindersId) {
        try {
            ResultDTO<String> deleteRes = toDoRemindersService.deleteTodoReminders(remindersId);
            return deleteRes;
        } catch (Exception e) {
            log.error("删除待办失败", e);
            return ResultDTO.error("删除待办失败");
        }
    }
}
