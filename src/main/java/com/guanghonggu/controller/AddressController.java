package com.guanghonggu.controller;

import cn.dev33.satoken.stp.StpUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.guanghonggu.dto.AddressDTO;
import com.guanghonggu.dto.LoginUserDTO;
import com.guanghonggu.dto.ResultDTO;
import com.guanghonggu.entity.Address;
import com.guanghonggu.service.AddressService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


/**
 * @author：zzn
 * @Description：地址
 * @createData：2025/4/27 11:21
 */
@Slf4j
@RestController
@RequestMapping("/address")
public class AddressController {

    @Autowired
    private AddressService addressService;

    // ========== 查询类 ==========

    /**
     * 根据地址id查询地址信息
     */
    @PostMapping("/getAddressId")
    public ResultDTO<Address> getAddressId(@RequestBody AddressDTO addressDTO) {
        try{
            Address addressId = addressService.getAddressId(addressDTO);
            return ResultDTO.success("根据地址id查询成功",addressId);
        }catch (Exception e){
            e.printStackTrace();
            return ResultDTO.error("根据地址id查询失败");
        }
    }

    @PostMapping("/user")
    public ResultDTO<Page<Address>> getAddressesByUserId(@RequestBody (required = false)AddressDTO addressDTO) {
        try {
            if (addressDTO == null){
                addressDTO = new AddressDTO();
            }
            // 如果 userId 没有传，就从登录用户中获取（Sa-Token session）
            String loginIdAsString = StpUtil.getLoginIdAsString();
            LoginUserDTO loginUser = (LoginUserDTO) StpUtil.getSession().get(loginIdAsString);
            Integer userType = loginUser.getUserType();

            if (addressDTO.getUserId() == null) {
                addressDTO.setUserId(loginUser.getUserId());
            }
            if (addressDTO.getUserType() == null) {
                addressDTO.setUserType(loginUser.getUserType());
            }

            Integer page = addressDTO.getPage() != null ? addressDTO.getPage() : 1;
            Integer size = addressDTO.getSize() != null ? addressDTO.getSize() : 8;


            Page<Address> addressPage = addressService.getAddressesByUserId(
                    addressDTO.getUserId(),
                    page, size, userType);
            return ResultDTO.success("根据用户id查询成功", addressPage);
        } catch (Exception e) {
            log.error("【根据用户id查询】出现异常，原因：", e);
            return ResultDTO.error("根据用户id查询失败" + e.getMessage());
        }
    }


    // ========== 新增与修改类 ==========

    @PostMapping("/postAddressId")
    public ResultDTO<Long> addAddress(@RequestBody(required = false) AddressDTO addressDTO) {
        try {
            String loginIdAsString = StpUtil.getLoginIdAsString();
            LoginUserDTO loginUser = (LoginUserDTO) StpUtil.getSession().get(loginIdAsString);

            if (addressDTO.getUserId() == null) {
                addressDTO.setUserId(loginUser.getUserId());
            }
            if (addressDTO.getUserType() == null) {
                addressDTO.setUserType(loginUser.getUserType());
            }

            long postId = addressService.addAddress(addressDTO);

            return ResultDTO.success("新增成功",postId);
        } catch (Exception e) {
            log.error("【新增】出现异常，原因：", e);
            return ResultDTO.error("新增失败" + e.getMessage());
        }
    }

    @PutMapping("/putAddressId")
    public ResultDTO<Long> updateAddress(@RequestBody(required = false) AddressDTO addressDTO) {
        try {

            if (addressDTO ==null){
                addressDTO = new AddressDTO();
            }
            Long addressId = addressDTO.getAddressId();

            addressDTO.setAddressId(addressId);
            int result = addressService.updateAddress(addressDTO);
            if (result > 0) {
                return ResultDTO.success("更新成功",addressId);
            } else {
                return ResultDTO.error("更新失败，未找到地址");
            }
        } catch (Exception e) {
            log.error("【更新】出现异常，原因：", e);
            return ResultDTO.error("更新失败：" + e.getMessage());
        }
    }

    @PutMapping("/setDefault")
    public ResultDTO<Boolean> setDefaultAddress(@RequestBody(required = false) AddressDTO addressDTO) {
        try {

            if (addressDTO == null){
                addressDTO = new AddressDTO();
            }
            Integer isDefault = addressDTO.getIsDefault() != null ? addressDTO.getIsDefault() : 1;
            Long addressId = addressDTO.getAddressId();
            addressDTO.setIsDefault(isDefault);
            addressDTO.setAddressId(addressId);
            boolean setDefaultAddress = addressService.setDefaultAddress(addressDTO);
            return ResultDTO.success("设置默认地址成功",setDefaultAddress);
        } catch (Exception e) {
            log.error("【设置默认地址】出现异常，原因：", e);
            return ResultDTO.error("设置默认地址失败" + e.getMessage());
        }
    }


    // ========== 删除类 ==========

    @DeleteMapping("/deleteAddress")
    public ResultDTO<Long> deleteAddress(@RequestBody(required = false) AddressDTO addressDTO) {
        try {

            if (addressDTO == null){
                addressDTO = new AddressDTO();
            }
            String loginIdAsString = StpUtil.getLoginIdAsString();
            LoginUserDTO loginUser = (LoginUserDTO) StpUtil.getSession().get(loginIdAsString);

            if (addressDTO.getUserId() == null) {
                addressDTO.setUserId(loginUser.getUserId());
            }
            if (addressDTO.getUserType() == null) {
                addressDTO.setUserType(loginUser.getUserType());
            }

            Long addressId = addressDTO.getAddressId();

            addressDTO.setAddressId(addressId);
            int result = addressService.deleteAddress(addressDTO);
            if (result > 0) {
                return ResultDTO.success("删除成功",addressId);
            } else {
                return ResultDTO.error("删除失败，未找到地址");
            }
        } catch (Exception e) {
            log.error("【删除地址】出现异常，原因：", e);
            return ResultDTO.error("删除地址失败: " + e.getMessage());
        }
    }


}