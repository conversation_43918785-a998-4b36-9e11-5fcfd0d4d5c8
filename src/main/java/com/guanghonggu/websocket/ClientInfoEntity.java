package com.guanghonggu.websocket;


import lombok.Data;

import javax.websocket.Session;
import java.time.LocalDateTime;

/**
 * @author：zzn
 * @description：
 * @createData：2025/7/11 11:33
 */
@Data
public class ClientInfoEntity {

    /**
     * 客户端唯一标识
     */
    private String clientId;

    /**
     * 客户端类型(user caregiver)
     */
    private String clientType;

    /**
     * 客户端连接的session
     */
    private Session session;
    /**
     * 连接存活时间
     */
    private LocalDateTime existTime;

    /**
     * 正在监听的阿姨ID，仅用户端有意义
     */
    private String watchingCaregiverId;
}