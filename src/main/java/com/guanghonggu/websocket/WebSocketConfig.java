package com.guanghonggu.websocket;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.socket.server.standard.ServerEndpointExporter;

/**
 * @author：zzn
 * @description：
 * @createData：2025/7/11 12:00
 */
@Configuration
public class WebSocketConfig {

    /**
     * 该方法用来创建并返回一个ServerEndpointExporter实例。
     * 这个实例的作用是扫描并自动配置所有使用@ServerEndpoint注解标记的WebSocket端点
     *
     * @return ServerEndpointExporter：这是一个用于自动检测和管理WebSocket端点的类。
     *          通过将其实例化并配置为Spring管理的Bean，可以确保所有WebSocket端点在应用程序启动时被自动初始化和注册。
     */
    @Bean
    public ServerEndpointExporter serverEndpointExporter(){
        return new ServerEndpointExporter();
    }
}