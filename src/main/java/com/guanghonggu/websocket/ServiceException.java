package com.guanghonggu.websocket;

import lombok.Data;

/**
 * @author：zzn
 * @description：
 * @createData：2025/7/11 13:31
 */
@Data
public class ServiceException extends RuntimeException {

    private Integer code;

    private String msg;

    public ServiceException(Integer code, String msg) {
        super();
        this.msg = msg;
        this.code = code;
    }

    public ServiceException(String msg) {
        this(1, msg);
    }

    @Override
    public String getMessage() {
        return msg;
    }

}
