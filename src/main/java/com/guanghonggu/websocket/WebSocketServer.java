package com.guanghonggu.websocket;


import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.guanghonggu.entity.AdminUser;
import com.guanghonggu.entity.Location;
import com.guanghonggu.enums.WebSocketMessageType;
import com.guanghonggu.handler.SpringContextHolder;
import com.guanghonggu.mapper.AdminUserMapper;
import com.guanghonggu.service.impl.LocationServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;


import javax.websocket.OnClose;
import javax.websocket.OnError;
import javax.websocket.OnMessage;
import javax.websocket.OnOpen;
import javax.websocket.Session;
import javax.websocket.server.PathParam;
import javax.websocket.server.ServerEndpoint;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * @author：zzn
 * @description：该类负责监听客户端的连接、断开连接、接收消息、发送消息等操作。
 * @createData：2025/7/11 13:34
 */
@Slf4j
@Component
@ServerEndpoint(value = "/webSocket/{clientType}/{id}", configurator = GetHttpSessionConfig.class)
public class WebSocketServer {
    //key：客户端连接唯一标识(token)
    //value：ClientInfoEntity
    private static final Map<String, ClientInfoEntity> uavWebSocketInfoMap = new ConcurrentHashMap<>();

    private static final ObjectMapper objectMapper = new ObjectMapper();


    private static final int EXIST_TIME_HOUR = 6;

    /**
     * 连接建立成功调用的方法
     *
     * @param session 第一个参数必须是session
     * @param id      代表客户端的唯一标识
     */
    @OnOpen
    public void onOpen(Session session, @PathParam("clientType") String clientType, @PathParam("id") String id) {
        String clientId = clientType + ":" + id;
        if (uavWebSocketInfoMap.containsKey(clientId)) {
            log.info("{}已建立连接", clientId);
            return;
        }

        //把成功建立连接的会话在实体类中保存
        ClientInfoEntity entity = new ClientInfoEntity();
        entity.setClientId(clientId);
        entity.setSession(session);
        //默认连接6个小时
        entity.setExistTime(LocalDateTime.now().plusHours(EXIST_TIME_HOUR));
        uavWebSocketInfoMap.put(clientId, entity);
        //之所以获取http session 是为了获取获取httpsession中的数据 (用户名 /账号/信息)
        log.info("WebSocket 连接建立成功: {}", clientId);
    }

    /**
     * 当断开连接时调用该方法
     *
     * @param session
     */
    @OnClose
    public void onClose(Session session, @PathParam("clientType") String clientType, @PathParam("id") String id) {
        // 找到关闭会话对应的用户 ID 并从 uavWebSocketInfoMap 中移除
        String clientId = clientType + ":" + id;
        if (ObjectUtil.isNotEmpty(clientId) && uavWebSocketInfoMap.containsKey(clientId)) {
            uavWebSocketInfoMap.remove(clientId);
            log.info("WebSocket 连接关闭成功: {}", clientId);
        }
    }

    /**
     * 接受消息
     * 这是接收和处理来自用户的消息的地方。我们需要在这里处理消息逻辑，可能包括广播消息给所有连接的用户。
     */
    @OnMessage
    public void onMessage(Session session, @PathParam("clientType") String clientType, @PathParam("id") String id, String message) {
//        log.info("接收到消息：{}", message);
        String clientId = clientType + ":" + id;
        ClientInfoEntity entity = uavWebSocketInfoMap.get(clientId);

        if (entity == null) return;
        entity.setExistTime(LocalDateTime.now().plusHours(EXIST_TIME_HOUR));

        JSONObject msgObj = JSON.parseObject(message);
        String type = msgObj.getString("type");

        // 处理订阅地理位置消息
        if ("watch_location".equals(type)) {
            String caregiverId = msgObj.getString("caregiverId");
            entity.setWatchingCaregiverId(caregiverId); // 加一个字段表示关注谁
//            log.info("用户 [{}] 正在监听阿姨 [{}] 的定位", clientId, caregiverId);
            LocationServiceImpl locationService = SpringContextHolder.getBean(LocationServiceImpl.class);
            Location localization = locationService.getLocationByUserId(Long.parseLong(caregiverId));
            JSONObject data = new JSONObject();
            data.put("latitude", localization.getLatitude());
            data.put("longitude", localization.getLongitude());
            data.put("caregiverId", caregiverId);
//            entity.getSession().getBasicRemote().sendText(data.toJSONString());
            sendMessageToTarget(entity.getClientId(), data, WebSocketMessageType.LOCATION_UPDATE.getType());
        }

        // 回复心跳
//        if (entity.getSession().isOpen()) {
//            try {
//                entity.getSession().getBasicRemote().sendText("{\"msg\": \"ok\", \"code\": 0}");
//            } catch (IOException e) {
//                log.error("web socket报错信息:", e);
//                throw new RuntimeException(e);
//            }
//        }
    }


    /**
     * 处理WebSocket中发生的任何异常。可以记录这些错误或尝试恢复。
     */
    @OnError
    public void onError(Throwable error) {
        log.error("web socket报错信息:", error);
    }

    /**
     * 发生消息定时器
     */
//    @PostConstruct
//    @Scheduled(cron = "0/1 * *  * * ? ")
//    public void refreshDate() {
//        //开启定时任务，1秒一次向前台发送当前时间
//        //当没有客户端连接时阻塞等待
//        if (!uavWebSocketInfoMap.isEmpty()) {
//            //超过存活时间进行删除
//            Iterator<Map.Entry<String, ClientInfoEntity>> iterator = uavWebSocketInfoMap.entrySet().iterator();
//            while (iterator.hasNext()) {
//                Map.Entry<String, ClientInfoEntity> entry = iterator.next();
//                if (!entry.getValue().getExistTime().isAfter(LocalDateTime.now())) {
//                    log.info("WebSocket {} 已到存活时间，自动断开连接", entry.getKey());
//                    try {
//                        entry.getValue().getSession().close();
//                    } catch (IOException e) {
//                        log.error("WebSocket 连接关闭失败: {} - {}", entry.getKey(), e.getMessage());
//                    }
//                    //过期则进行移除
//                    iterator.remove();
//                }
//            }
////            sendMessage(FORMAT.format(new Date()));
//        }
//    }

    /**
     * 群发信息的方法
     *
     * @param message 消息
     */
    public void sendMessage(String message) {
        System.out.println(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date())
                + "发送全体消息:" + message);
        //循环客户端map发送消息
        uavWebSocketInfoMap.values().forEach(item -> {
            //向每个用户发送文本信息。这里getAsyncRemote()解释一下，向用户发送文本信息有两种方式，
            // 一种是getBasicRemote，一种是getAsyncRemote
            //区别：getAsyncRemote是异步的，不会阻塞，而getBasicRemote是同步的，会阻塞，由于同步特性，第二行的消息必须等待第一行的发送完成才能进行。
            // 而第一行的剩余部分消息要等第二行发送完才能继续发送，所以在第二行会抛出IllegalStateException异常。所以如果要使用getBasicRemote()同步发送消息
            // 则避免尽量一次发送全部消息，使用部分消息来发送，可以看到下面sendMessageToTarget方法内就用的getBasicRemote，因为这个方法是根据用户id来私发的，所以不是全部一起发送。
            item.getSession().getAsyncRemote().sendText(message);
        });
    }

    /**
     * 给指定用户发送结构化 WebSocket 消息
     *
     * @param id      客户端 用户/阿姨id
     * @param payload 业务消息内容（Object，自动转 JSON）
     * @param type    消息类型（如 order_update、chat_msg）
     */
    public void sendMessageToTarget(String id, Object payload, String type) {
        ClientInfoEntity entity = uavWebSocketInfoMap.get(id);
        if (entity == null) {
            log.warn("尝试给不存在的连接发送消息，clientId: {}", id);
            return;
        }

        Session session = entity.getSession();
        if (session != null && session.isOpen()) {
            try {
                // 封装消息对象
                SocketMessage<Object> socketMessage = new SocketMessage<>(type, payload);
                String json = objectMapper.writeValueAsString(socketMessage);

                // 同步发送
                session.getBasicRemote().sendText(json);
                log.info("成功发送消息给用户 [{}] ：{}", id, json);
            } catch (IOException e) {
                log.error("发送消息给用户 [{}] 失败：{}", id, e.getMessage());
            }
        } else {
            log.warn("用户 [{}] 的 WebSocket 会话未打开", id);
        }
    }

    /**
     * 推送经纬度（定时任务或坐标上传时调用）
     * @param caregiverId
     * @param lat
     * @param lon
     */
    public void pushLocationToUser(String caregiverId, double lat, double lon) {
        uavWebSocketInfoMap.values().forEach(entity -> {
            if (StringUtils.isNotBlank(entity.getWatchingCaregiverId()) && entity.getWatchingCaregiverId().equals(caregiverId)) {
                if (entity.getSession().isOpen()) {
                    JSONObject data = new JSONObject();
                    data.put("latitude", lat);
                    data.put("longitude", lon);
                    data.put("caregiverId", caregiverId);
//                        entity.getSession().getBasicRemote().sendText(data.toJSONString());
                    sendMessageToTarget(entity.getClientId(), data, WebSocketMessageType.LOCATION_UPDATE.getType());
                }
            }
        });
    }

    /**
     * 用户下单通知管理端
     */
    public void sendMessageToAdminUserByOrder() {
        AdminUserMapper adminUserMapper = SpringContextHolder.getBean(AdminUserMapper.class);
        LambdaQueryWrapper<AdminUser> adminUserLambdaQueryWrapper = new LambdaQueryWrapper<>();
        List<AdminUser> adminUsers = adminUserMapper.selectList(adminUserLambdaQueryWrapper);
        for (AdminUser adminUser : adminUsers) {
            Long id = adminUser.getId();
            sendMessageToTarget("management:" + id, "", WebSocketMessageType.ORDER_RELEASED_MANAGEMENT.getType());
        }
    }
}
