package com.guanghonggu.dto;

import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import java.util.Date;
import java.util.List;

/**
 * 阿姨服务验证
 */
@Data
public class CaregiverServiceVerificationDTO {
    /**
     * 服务验证id
     */
    private Long serviceVerificationId;


    /**
     * 阿姨id
     */
    private Long caregiverId;

    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 到达时间
     */
    private Date arrivalTime;

    /**
     * 拍照验证时间
     */
    private Date verificationTime;

    /**
     * 验证照片
     */
    private String verificationPhoto;

    /**
     * 状态（0：待验证，1：验证通过，2：验证未通过）
     */
    private Integer status;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 拍照验证图片列表
     */
    private List<MultipartFile> verificationPhotoList;

}