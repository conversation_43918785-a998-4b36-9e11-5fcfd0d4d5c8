package com.guanghonggu.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @author：zzn
 * @description：
 * @createData：2025/7/24 15:49
 */
@Data
public class CleaningItemShopOrderDetailDTO {

    /** 明细ID */
    private Long id;

    /** 订单ID */
    private Long shopOrderId;

    /** 商品ID */
    private Long itemId;

    /** 商品名称 */
    private String itemName;

    /** 商品单价 */
    private BigDecimal unitPrice;

    /** 购买数量 */
    private Integer quantity;

    /** 明细总价（单价 × 数量） */
    private BigDecimal totalPrice;


    private String commodityName;
}
