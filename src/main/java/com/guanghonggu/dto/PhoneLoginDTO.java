package com.guanghonggu.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 小程序登录 + 支付请求 DTO
 */
@Data
public class PhoneLoginDTO {
    private String code;         // wx.login() 拿到的 code
    private String encryptedData; // 手机号加密数据
    private Long userId;        //用户id
    private String iv;           // 加密偏移量

    // ↓↓↓ 以下为微信支付新增字段 ↓↓↓
    private String body;         // 商品描述
    private String outTradeNo;   // 商户订单号（你可以随机生成）
    private BigDecimal totalFee;        // 支付金额（单位：分）
}