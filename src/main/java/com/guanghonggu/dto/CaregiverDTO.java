package com.guanghonggu.dto;

import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalTime;
import java.util.Date;

/**
 * 阿姨DTO
 */
@Data
public class CaregiverDTO {
    /**
     * 阿姨id
     */
    private Long caregiverId;

    /**
     * 手机号
     */
    private String phoneNumber;

    /**
     * 微信开放id
     */
    private String wechatOpenid;

    /**
     * 状态 (0: 禁用, 1: 启用)
     */
    private Integer status;

    /**
     * 下班时间
     */
    private LocalTime stopOrderTime;

    /**
     * 姓名
     */
    @NotBlank(message = "姓名不能为空")
    private String name;

    /**
     * 年龄
     */
    @NotNull(message = "年龄不能为空")
    private Integer age;

    /**
     * 头像
     */
    private String avatar;

    /**
     * 性别
     */
    @NotNull(message = "性别不能为空")
    private Integer gender;

    /**
     * 平均评分（来自评价表中三个字段的平均值，保存一位小数）
     */
    private Double avgEvaluationScore;

    /**
     * 地址id
     */
    private Long caregiverAddressId;

    /**
     * 身份证号码
     */
//    @NotBlank(message = "身份证号码不能为空")
    private String idCardNumber;

    /**
     * 身份证正面照片
     */
    private String idCardFront;

    /**
     * 身份证反面照片
     */
    private String idCardBack;

    /**
     * 健康证照片
     */
    private String healthCertificate;

    /**
     * 学习/考试状态（0：未学习，1：已学习，2：未考试，3：已考试）
     */
    private Integer examStatus;

    /**
     * 阿姨评分
     */
    private BigDecimal score;

    /**
     * 身份证审核（1：通过，0：未通过）
     */
    private Integer idCardVerification;

    /**
     * 健康证审核（1：通过，0：未通过）
     */
    private Integer healthCertificateVerification;

    /**
     * 注册时间
     */
    private Date registrationTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 接收前端图片
     */
    private MultipartFile idCardFrontImage;

    /**
     * 接收前端图片
     */
    private MultipartFile idCardBackImage;

    /**
     * 接收前端图片
     */
    private MultipartFile healthCertificateImage;

    /**
     * 支付密码
     */
    private String payPassword;

    /**
     * 经度
     */
    private double longitude;

    /**
     * 纬度
     */
    private double latitude;

    /**
     * 旧的支付密码
     */
    private String oldPassword;

    /**
     * 新密码
     */
    private String newPassword;

    /**
     * 订单数量
     */
    private Long orderCount;

    private Integer type;

    /**
     * 阿姨来源
     */
    private String caregiverSource;

}