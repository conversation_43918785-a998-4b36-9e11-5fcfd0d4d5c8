package com.guanghonggu.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.Date;

/**
 * 地址DTO
 */
@Data
public class AddressDTO {
    /**
     * 地址id
     */
    private Long addressId;

    /**
     * 用户id/阿姨id
     */
    private Long userId;

    /**
     * 用户类型（1：用户，2：阿姨）
     */
    private Integer userType;

    /**
     * 详细街道
     */
    private String locationDetails;

    /**
     * 区县
     */
    private String area;

    /**
     * 建筑物
     */
    private String placeName;

    /**
     * 详细地址
     */
    private String detailedAddress;

    /**
     * 经度
     */
    private Double longitude;

    /**
     * 纬度
     */
    private Double latitude;

    /**
     * 是否默认地址 (0: 否, 1: 是)
     */
    private Integer isDefault;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 分页（开始的页数）
     */
    private Integer page;

    /**
     *分页（一页有多少信息）
     */
    private Integer size;

    /**
     * 姓名
     */
    private String userName;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 经纬度
     */
    private String longitudeAndLatitude;

    /**
     * 状态（0=未删除，1=已删除）
     */
    private Integer status;




}