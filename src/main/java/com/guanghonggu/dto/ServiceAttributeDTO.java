package com.guanghonggu.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;


@Data
public class ServiceAttributeDTO {

    private Long id;

    private String serviceTypeCode;

    private String attributeKey;

    private String label;

    /**
     * 输入类型（number，select，radio，checkbox）
     */
    private String inputType;

    /**
     * 是否必须（0：否，1：是）
     */
    private Integer required;

    /**
     * 排序
     */
    private Integer sort;

    /** 选项列表 */
    private List<OptionDTO> option;

    /**
     * 属性选项 DTO 子类
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class OptionDTO {

        private Long id;
        /** 选项值（如 'heavy'） */
        private String value;

        /** 展示名称（如 '大片油污'） */
        private String label;

        private String attributeKey;

        private String selectValue;

        /** 价格 */
        private BigDecimal price;

        private Integer isStartingPrice;

        private String textColor;

        private String areaColor;
    }
}