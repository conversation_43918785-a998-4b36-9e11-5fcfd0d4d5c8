package com.guanghonggu.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.guanghonggu.validator.group.OrderPayGroup;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 订单表
 */
@Data
public class OrderDTO extends PageDTO {
    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 订单编号
     */
    private String orderNumber;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 阿姨id
     */
    private Long caregiversId;

    /**
     * 地址id
     */
    @NotNull(message = "请选择地址", groups = {OrderPayGroup.class})
    private Long addressId;

    /**
     * 订单状态 (0: 等待接单，1: 已预约, 2: 进行中, 3: 已完成, 4: 取消，5退款中，6已退款，7待支付)
     */
    private Integer status;

    /**
     * 服务类型Id（1：日常保洁，2：开荒保洁，3：玻璃清洗，4：机器拆洗，5：企业保洁）
     */
    @NotNull(message = "服务类型Id不能为空", groups = {OrderPayGroup.class})
    private Long serviceTypeId;

    /**
     * 预约时间
     */
    @NotNull(message = "预约时间不能为空", groups = {OrderPayGroup.class})
    private Date appointmentTime;

    /**
     * 上一次的预约时间
     */
    private Date lastAppointmentTime;

    /**
     * 订单日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date orderDate;

    /**
     * 完成时间
     */
    private Date orderCompletionTime;

    /**
     * 服务结束时间
     */
    private Date serviceEndTime;
    /**
     * 订单金额
     */
    @NotNull(message = "订单金额不能为空", groups = {OrderPayGroup.class})
    private BigDecimal totalPrice;

    /**
     * 优惠金额
     */
    @NotNull(message = "优惠金额不能为空", groups = {OrderPayGroup.class})
    private BigDecimal preferentialPrice;

    /**
     * 实际付款金额
     */
    @NotNull(message = "实际付款金额不能为空", groups = {OrderPayGroup.class})
    private BigDecimal actualPaymentPrice;


    /**
     * 阿姨收入
     */
    private BigDecimal caregiverIncome;

    /**
     * 商家收入
     */
    private BigDecimal merchantIncome;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 支付类型（1：WechatPay：微信，2：AliPay：支付宝，3：WalletPay：钱包，4：微信+钱包，5：支付宝+钱包，6：微信小程序支付，7：钱包+微信小程序支付）
     */
    @Min(value = 1, message = "支付方式非法")
    @Max(value = 3, message = "支付方式非法")
    private Integer payMethods;

    private Integer payMethods4DB;

    /**
     * 支付密码
     */
    private String payPassword;

    /**
     * 支付时间
     */
    private Date payTime;

    /**
     * 优惠券id
     */
    private Long couponId;

    /**
     * 订单类型（1：立即分配，2：预约大厅，3：预约阿姨）
     */
    private Integer orderType;

    /**
     * 分页（开始的页数）
     */
    private Integer page;

    /**
     *分页（一页有多少信息）
     */
    private Integer size;

    @NotNull(message = "是否接受不能为空")
    private Boolean accept;

    /**
     * 拒绝原因
     */
    private String message;

    /**
     * 选择的取消原因ID
     */
    private Long reasonId;

    /**
     * 自定义原因（如果 reasonId 是“其他”）
     */
    private String customReason;

    /**
     * 用户是否确认了修改加价（前端需勾选确认框）
     */
    private Boolean confirmModify;

    /**
     *  对应 service_type 表中的 name 字段
     */
    private String serviceTypeName;

    /**
     * 接收参数
     */
    private List<OrderAttributeDTO> attributes;

    /**
     * 再下一单时返回参数
     */
    private List<ServiceAttributeDTO.OptionDTO> options;

    // 地址信息
    private String detailedAddress;
    private String area;
    private String locationDetails;
    private String placeName;
    /**
     * 小程序支付使用
     */
    private String code;

    /**
     * 对应address表中的phone_number字段
     */
    private String phoneNumber;

    /**
     * 对应address表中的longitude字段
     */
    private Double longitude;

    /**
     * 对应address表中的latitude字段
     */
    private Double latitude;

    /**
     * 二次支付商户订单编号
     */
    private String secondaryOrderNumber;

    /**
     * 二次支付订单号
     */
    private String secondaryPayOrderNumber;

    private Integer userType;

    private String phone;

    /**
     * 用户使用的优惠券
     */
    private String userCouponId;
    /**
     * 是否迟到（0：否，1：是）
     */
    private Integer isLate;

    /**
     * 是否查看
     */
    private Integer view;

}