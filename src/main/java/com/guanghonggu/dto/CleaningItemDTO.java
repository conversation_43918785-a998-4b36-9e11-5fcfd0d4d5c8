package com.guanghonggu.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 清洁工具与清洁剂表
 * @TableName cleaning_item
 */
@Data
public class CleaningItemDTO extends PageDTO{
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 工具或清洁剂名称
     */
    private String name;

    /**
     * 物品类型（工具/清洁剂）
     */
    private String type;

    /**
     * 所属分类ID（关联分类表）
     */
    private Long categoryId;

    /**
     * 材质/类型描述（如“超细纤维”）
     */
    private String material;

    /**
     * 适用范围（如“布艺/皮质家具”）
     */
    private String applicableScope;

    /**
     * 备注/说明
     */
    private String remark;

    /**
     * 价格
     */
    private BigDecimal price;


    private Long itemId;

    private String itemName;

    private String categoryName;

    private String imageUrl;

    private List<String> imageUrlList;

}