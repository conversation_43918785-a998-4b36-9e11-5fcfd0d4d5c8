package com.guanghonggu.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.guanghonggu.validator.group.CaregiverScheduleAppointmentGroup;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.time.LocalTime;
import java.util.Date;
import java.util.List;

/**
 * 阿姨日程表
 */
@Data
public class CaregiverScheduleDTO {
    /**
     * 日程id
     */
    private Long scheduleId;

    /**
     * 阿姨id
     */
    @NotNull(message = "阿姨id不能为空", groups = {CaregiverScheduleAppointmentGroup.class})
    private Long caregiverId;

    /**
     * 日程日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date scheduleDate;

    /**
     * 日程类型
     */
    private Integer scheduleType;;

    /**
     * 日程开始时间
     */
    @JsonFormat(pattern = "HH:mm")
    @NotNull(message = "请选择日程开始时间")
    private LocalTime scheduleStartTime;

    /**
     * 日程结束时间
     */
    @JsonFormat(pattern = "HH:mm")
    @NotNull(message = "请选择日程结束时间")
    private LocalTime scheduleEndTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 小时列表
     */
    private List<Integer> hourList; // 小时列表：9 表示 9-10，14 表示 14-15

    /**
     * 阿姨下班时间
     */
    @JsonFormat(pattern = "HH:mm")
    private LocalTime stopOrderTime;

    /**
     * 服务类型id
     */
    private Long serviceTypeId;

    /**
     * 地址id
     */
    private Long addressId;

    /**
     * 订单信息
     */
    @Valid
    @NotNull(message = "订单信息不能为空")
    private OrderDTO order;

    /**
     * 小程序支付需要
     */
    private String code;

    /**
     * 订单id
     */
    private Integer orderId;
}