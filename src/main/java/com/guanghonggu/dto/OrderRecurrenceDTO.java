package com.guanghonggu.dto;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Date;

@Data
public class OrderRecurrenceDTO {
    /**
     * 周期规则ID
     */
    private Long recurrenceId;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 服务模式（2：按周，3：按月）
     */
    private Integer serviceMode;

    /**
     * 重复规则（按周:1,3,5；按月:5,15,25 或 iCal 规则）
     */
    private String recurrenceRule;

    /**
     * 周期次数
     */
    private Integer recurrenceCount;

    /**
     * 已使用次数
     */
    private Integer usedCount;

    /**
     * 总服务次数
     */
    private Integer totalServiceCount;

    /**
     * 单次服务金额
     */
    private BigDecimal singleServiceAmount;

    /**
     * 服务时间
     */
    private LocalTime serviceTime;

    /**
     * 下次服务时间
     */
    private Date nextServiceTime;

    /**
     * 上次服务时间
     */
    private LocalDateTime lastServiceTime;

    /**
     * 已结算次数
     */
    private Integer settledCount;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 修改类型（1：下一次，2：全部）
     */
    private Integer editType;

}