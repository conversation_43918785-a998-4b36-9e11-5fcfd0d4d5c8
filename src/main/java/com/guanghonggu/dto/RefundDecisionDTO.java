package com.guanghonggu.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class RefundDecisionDTO {
    @NotNull(message = "退款ID不能为空")
    private Long refundId; // 退款单ID

    @NotNull(message = "阿姨ID不能为空")
    private Long caregiverId; // 阿姨ID

    @NotNull(message = "输入是否接受")
    private Boolean accept; // true=接受，false=拒绝

    private String rejectReason; // 拒绝理由（只有在拒绝时填写）
}
