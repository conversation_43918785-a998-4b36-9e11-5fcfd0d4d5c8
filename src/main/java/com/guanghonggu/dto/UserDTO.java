package com.guanghonggu.dto;

import lombok.Data;

import java.util.Date;

/**
 * 用户表
 */
@Data
public class UserDTO {
    /**
     * 用户id
     */
    private Long userId;

    /**
     * 用户类型（1=用户，2=阿姨）
     */
    private Integer type;

    /**
     * 手机号
     */
    private String phoneNumber;

    /**
     * 微信开放id
     */
    private String wecahtOpenid;

    /**
     * 昵称
     */
    private String nickname;

    /**
     * 头像
     */
    private String avatar;

    /**
     * 账户状态 (0: 禁用, 1: 启用)
     */
    private Integer status;

    /**
     * 注册时间
     */
    private Date registrationTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 支付密码
     */
    private String payPassword;

    /**
     * 会员等级
     */
    private String vipLevel;

    /**
     * 旧密码
     */
    private String oldPassword;

    /**
     * 新密码
     */
    private String newPassword;

    /**
     * 验证码
     */
    private String verificationCode;

    /**
     * 极光推送id
     */
    private String registrationId;


}