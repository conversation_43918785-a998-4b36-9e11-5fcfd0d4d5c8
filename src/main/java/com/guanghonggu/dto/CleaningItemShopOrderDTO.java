package com.guanghonggu.dto;

import com.guanghonggu.entity.CleaningItemShopOrderDetail;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 服务人员购买清洁工具订单表
 * @TableName cleaning_item_shop_order
 */
@Data
public class CleaningItemShopOrderDTO {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 订单编号
     */
    private String orderNumber;

    /**
     * 服务人员ID
     */
    private Long caregiverId;

    /**
     * 清洁物品ID
     */
    private Long itemId;

    /**
     * 总价格
     */
    private BigDecimal totalPrice;

    /**
     * 收货地址ID
     */
    private Long addressId;

    /**
     * 物流状态（0=待发货，1=已发货，2=已签收，3=已取消，4=待支付）
     */
    private Integer status;

    /**
     * 支付方式（1=微信，2=支付宝，3=钱包）
     */
    private Integer paymentMethod;

    /**
     * 支付单号（微信/支付宝交易号或钱包流水号）
     */
    private String payOrderNumber;

    /**
     * 购买时间
     */
    private Date purchaseTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 支付密码
     */
    private String payPassword;

    /**
     * 前端多选商品传值
     */
    private List<CleaningItemShopOrderDetailDTO> commodityList;

    /**
     * 收获人详细地址信息
     */
    private String fullAddress;

    /**
     * 收获人姓名
     */
    private String receiverName;

    /**
     * 收获人手机号
     */
    private String receiverPhone;

    /**
     * 是否已查看
     */
    private Integer view;

    /**
     * 小程序code
     */
    private String code;

    /**
     * 微信跳转小程序下单使用
     */
    private Long userId;
    private Integer userType;
    private String phone;

}