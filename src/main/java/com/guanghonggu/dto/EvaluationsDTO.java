package com.guanghonggu.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import java.util.Date;

/**
 * 评价表
 */
@Data
public class EvaluationsDTO {
    /**
     * 评价id
     */
    private Long evaluationsId;

    /**
     * 评价人id（用户id）
     */
    private Long evaluatorsId;

    /**
     * 被评价人id（阿姨id）
     */
    private Long evaluatedId;

    /**
     * 整体评价
     */
    private Integer overallEvaluation;

    /**
     * 服务态度
     */
    private Integer serviceAttitude;

    /**
     * 清洁程度
     */
    private Integer cleanlinessLevel;

    /**
     * 评价内容
     */
    private String evaluationContent;

    /**
     * 评价状态 (0: 待审核, 1: 已审核, 2: 被删除)
     */
    private Integer status;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 分页（开始的页数）
     */
    private Integer page;

    /**
     *分页（一页有多少信息）
     */
    private Integer size;

    /**
     *生成链接
     */
    private String objectName;

    /**
     * 上传图片
     */
    private MultipartFile file;

    /**
     * 判断ID
     */
    private Long userId;

}