package com.guanghonggu.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 优惠卷表
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CouponDTO extends PageDTO {
    /**
     * 优惠卷id
     */
    private Long couponId;

    /**
     * 名称标题
     */
    private String title;

    /**
     * 详细描述
     */
    private String description;

    /**
     * 优惠卷类型（1：固定金额，2：百分比）
     */
    private Integer couponType;

    /**
     * 满减金额
     */
    private BigDecimal fullAmount;

    /**
     * 折扣值（百分比或金额）
     */
    private String discountValue;

    /**
     * 最大折扣值
     */
    private BigDecimal maxDiscount;

    /**
     * 有效天数
     */
    private Integer validDay;

    /**
     * 限制数量
     */
    private Integer limitCount;

    /**
     * 有效开始时间
     */
    private Date validStartTime;

    /**
     * 有效结束时间
     */
    private Date validEndTime;

    /**
     * 发行总数
     */
    private String quantity;

    /**
     * 剩余可用数量
     */
    private String remainingQuantity;

    /**
     * 领取开始时间
     */
    private Date collectStartTime;

    /**
     * 领取结束时间
     */
    private Date collectEndTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 到期时间
     */
    private Date expirationTime;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 优惠类型（1：固定金额，2：百分比）
     */
    private Integer discountType;

    /**
     * 是否默认使用
     */
    private Integer isDefaultUse;

    /**
     * 用户优惠券id
     */
    private Long userCouponId;

}