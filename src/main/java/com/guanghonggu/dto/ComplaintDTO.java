package com.guanghonggu.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 投诉表
 */
@Data
public class ComplaintDTO {
    /**
     * 投诉id
     */
    private Long complaintId;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 阿姨id
     */
    private Long caregiverId;

    /**
     * 投诉状态 (0: 待处理, 1: 处理中, 2: 已解决)
     */
    private Integer status;

    /**
     * 投诉内容
     */
    private String complaintDetail;

    /**
     * 反馈
     */
    private String feedback;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 分页（开始的页数）
     */
    private Integer page;

    /**
     *分页（一页有多少信息）
     */
    private Integer size;

    /**
     * 投诉类型
     */
    private List<Long> complaintTypeList;

}