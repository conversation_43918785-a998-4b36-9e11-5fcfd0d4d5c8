package com.guanghonggu.dto;

import lombok.Data;

import java.util.Date;

/**
 * 代办提醒表
 */
@Data
public class ToDoRemindersDTO extends PageDTO{
    /**
     * 提醒id
     */
    private Long remindersId;

    /**
     * 阿姨id
     */
    private Long caregiverId;

    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 标题
     */
    private String title;

    /**
     * 描述
     */
    private String description;

    /**
     * 提醒时间
     */
    private Date reminderTime;

    /**
     * 状态（1: 待处理, 2: 已完成）
     */
    private Integer status;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

}