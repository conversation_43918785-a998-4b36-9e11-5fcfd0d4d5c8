package com.guanghonggu.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 钱包表
 */
@Data
public class WalletDTO {
    /**
     * 钱包id
     */
    private Long walletId;

    /**
     * 用户/阿姨id
     */
    private Long userId;

    /**
     * 用户类型 (1: 用户, 2: 阿姨)

     */
    private Integer userType;

    /**
     * 余额
     */
    private BigDecimal balance;

    /**
     * 充值余额
     */
    private BigDecimal rechargeBalance;

    /**
     * 总收入
     */
    private BigDecimal totalIncome;

    /**
     * 总支出
     */
    private BigDecimal totalSpent;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 提现方式 (1: APP微信, 2: 支付宝，3微信小程序)
     */
    private Integer withdrawType;


    /** 充值金额（单位：元，BigDecimal 类型） */
    private BigDecimal amount;

    /** 商品标题（例如：充值钱包） */
    private String subject;

    /**
     * 小程序支付code
     */
    private String code;

    /**
     * 支付方式（1：APP微信支付，2：支付宝支付，3：钱包支付，4：钱包+APP微信支付，5：钱包+支付宝，6：微信小程序支付，7：钱包+微信小程序支付）
     */
    private Integer payMethods;

}