package com.guanghonggu.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 钱包交易记录表
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class WalletTransactionDTO extends PageDTO {
    /**
     * 交易id
     */
    private Long transactionId;

    /**
     * 钱包id
     */
    private Long walletId;

    /**
     * 交易类型 (1充值、2消费、3提现)
     */
    private Integer transactionType;

    /**
     * 交易渠道
     */
    private Integer paymentChannel;

    /**
     * 交易金额
     */
    private BigDecimal amount;

    /**
     * 交易时间
     */
    private Date transactionTime;

    /**
     * 状态 (0: 待处理, 1: 完成, 2: 失败)
     */
    private Integer status;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date startTime;

    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endTime;

    /**
     * 第三方支付订单号
     */
    private String externalTradeNo;

    /**
     * 商户订单号
     */
    private String outTradeNo;

    /**
     * 普通余额支付金额
     */
    private BigDecimal balanceAmount;

    /**
     * 储值金额支付
     */
    private BigDecimal rechargeAmount;

    /**
     * 第三方支付金额
     */
    private BigDecimal externalAmount;

    /**
     * 提现支付宝账号
     */
    private String alipayAccount;

    /**
     * 真实姓名
     */
    private String realName;

}