package com.guanghonggu.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class LoginDTO {

    private Long userId;

    private Long caregiverId;

    private String phoneNumber;
    private String token;

    /**
     * 是否为新用户（0：否，1：是）
     */
    private Integer isNewUser;

    /**
     * 头像
     */
    private String avatar;

    /**
     * 是否未设置姓名
     */
    private Boolean isNotSetName;

}
