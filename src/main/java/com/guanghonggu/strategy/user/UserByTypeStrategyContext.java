package com.guanghonggu.strategy.user;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author：zzn
 * @description：钱包策略模式上下文
 * @createData：2025/4/30 15:50
 */
@Component
public class UserByTypeStrategyContext {
    /**
     * key：getType值，value：实例对象
     */
    private final Map<Integer, UserByTypeStrategyInterface> walletByTypeStrategies;


    @Autowired
    public UserByTypeStrategyContext(List<UserByTypeStrategyInterface> strategies) {
        this.walletByTypeStrategies = strategies.stream()
                .collect(Collectors.toMap(UserByTypeStrategyInterface::getType, Function.identity())
        );
    }

    /**
     * 根据类型找到备份实例调用
     * @param type  1:用户钱包，2：阿姨钱包
     */
    public UserByTypeStrategyInterface getTypeInstance(Integer type) {

        if (type == null) {
            throw new IllegalArgumentException("类型不能为空");
        }

        return walletByTypeStrategies.get(type);
    }
}