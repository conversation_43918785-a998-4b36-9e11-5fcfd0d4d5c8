package com.guanghonggu.strategy.user;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.guanghonggu.dto.ResultDTO;
import com.guanghonggu.entity.Caregiver;
import com.guanghonggu.entity.Order;
import com.guanghonggu.entity.Wallet;
import com.guanghonggu.handler.exception.BizException;
import com.guanghonggu.mapper.CaregiverMapper;
import com.guanghonggu.mapper.OrderMapper;
import com.guanghonggu.mapper.WalletMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


/**
 * @author：zzn
 * @description：阿姨钱包策略处理
 * @createData：2025/4/30 16:15
 */
@Slf4j
@Component
public class CaregiverStrategyService implements UserByTypeStrategyInterface {

    @Autowired
    private CaregiverMapper caregiverMapper;

    @Autowired
    private WalletMapper walletMapper;
    @Autowired
    private OrderMapper orderMapper;

    @Override
    public Integer getType() {
        return 2;
    }

    @Override
    public ResultDTO<Wallet> getBalance(Long userId) {
        LambdaQueryWrapper<Wallet> walletLambdaQueryWrapper = new LambdaQueryWrapper<>();
        walletLambdaQueryWrapper.select(Wallet::getWalletId, Wallet::getBalance, Wallet::getRechargeBalance,
                        Wallet::getTotalIncome)
                .eq(Wallet::getUserId, userId)
                .eq(Wallet::getUserType, 2);
        Wallet wallet = walletMapper.selectOne(walletLambdaQueryWrapper);
        if (wallet == null) {
            return ResultDTO.error("获取余额失败");
        }

        return ResultDTO.success("获取余额成功", wallet);
    }

    @Override
    public String getOpenId(Long userId) {
        Caregiver caregiver = caregiverMapper.selectById(userId);
        return caregiver.getWechatOpenid();
    }

    @Override
    public void wechatBind(String openId, Long userId) {
        LambdaQueryWrapper<Caregiver> caregiverLambdaQueryWrapper = new LambdaQueryWrapper<>();
        caregiverLambdaQueryWrapper.eq(Caregiver::getWechatOpenid, openId);
        Long count = caregiverMapper.selectCount(caregiverLambdaQueryWrapper);
        if (count > 0) {
            throw new BizException("该微信已绑定其他账号");
        }
        Caregiver caregiver = new Caregiver();
        caregiver.setCaregiverId(userId);
        caregiver.setWechatOpenid(openId);
        caregiverMapper.updateById(caregiver);
    }

    @Override
    public void updateRegistrationId(Long userId, String registrationId) {
        Caregiver caregiver = new Caregiver();
        caregiver.setCaregiverId(userId);
        caregiver.setRegistrationId(registrationId);
        caregiverMapper.updateById(caregiver);
    }

    @Override
    public void deleteOrder(Long orderId) {
        Order order = new Order();
        order.setOrderId(orderId);
        order.setCaregiverIsDelete(1);
        orderMapper.updateById(order);
    }
}
