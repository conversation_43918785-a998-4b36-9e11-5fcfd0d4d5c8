package com.guanghonggu.strategy.user;

import com.guanghonggu.dto.ResultDTO;
import com.guanghonggu.entity.Wallet;

import java.math.BigDecimal;

/**
 * @author：zzn
 * @description：钱包策略接口
 * @createData：2025/4/30 15:51
 */
public interface UserByTypeStrategyInterface {

    /**
     * 获取类型 1：用户，2：阿姨
     *
     * @return
     */
    Integer getType();

    /**
     * 获取余额
     *
     * @return
     */
    ResultDTO<Wallet> getBalance(Long userId);

    /**
     * 获取openId
     */
    String getOpenId(Long userId);

    /**
     * 微信绑定
     * @param openId
     * @param userId
     * @return
     */
    void wechatBind(String openId, Long userId);

    /**
     * 设置极光推送id
     * @param userId 用户/阿姨id
     * @param registrationId 极光推送id
     */
    void updateRegistrationId(Long userId, String registrationId);
}
