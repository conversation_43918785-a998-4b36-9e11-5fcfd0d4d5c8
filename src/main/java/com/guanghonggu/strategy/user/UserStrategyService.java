package com.guanghonggu.strategy.user;

import cn.dev33.satoken.stp.StpUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.guanghonggu.dto.LoginUserDTO;
import com.guanghonggu.dto.ResultDTO;
import com.guanghonggu.entity.Order;
import com.guanghonggu.entity.User;
import com.guanghonggu.entity.Wallet;
import com.guanghonggu.handler.exception.BizException;
import com.guanghonggu.mapper.OrderMapper;
import com.guanghonggu.mapper.UserMapper;
import com.guanghonggu.mapper.WalletMapper;
import lombok.extern.slf4j.Slf4j;
import org.omg.CORBA.PRIVATE_MEMBER;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

/**
 * @author：zzn
 * @description：用户钱包策略处理
 * @createData：2025/4/30 16:14
 */
@Slf4j
@Component
public class UserStrategyService implements UserByTypeStrategyInterface {

    @Autowired
    private WalletMapper walletMapper;
    @Autowired
    private UserMapper userMapper;

    @Autowired
    private OrderMapper orderMapper;

    @Override
    public Integer getType() {
        return 1;
    }

    @Override
    public ResultDTO<Wallet> getBalance(Long userId) {
        LambdaQueryWrapper<Wallet> walletLambdaQueryWrapper = new LambdaQueryWrapper<>();
        walletLambdaQueryWrapper.select(Wallet::getWalletId, Wallet::getBalance, Wallet::getRechargeBalance,
                        Wallet::getTotalSpent)
                .eq(Wallet::getUserId, userId)
                .eq(Wallet::getUserType, 1);
        Wallet wallet = walletMapper.selectOne(walletLambdaQueryWrapper);
        if (wallet == null) {
            return ResultDTO.error("获取余额失败");
        }

        return ResultDTO.success("获取余额成功", wallet);
    }

    @Override
    public String getOpenId(Long userId) {
        User user = userMapper.selectById(userId);
        return user.getWechatOpenid();
    }

    @Override
    public void wechatBind(String openId, Long userId) {
        LambdaQueryWrapper<User> userLambdaQueryWrapper = new LambdaQueryWrapper<>();
        userLambdaQueryWrapper.eq(User::getWechatOpenid, openId);
        Long count = userMapper.selectCount(userLambdaQueryWrapper);
        if (count > 0) {
            throw new BizException("该微信已绑定其他账号");
        }
        User user = new User();
        user.setUserId(userId);
        user.setWechatOpenid(openId);
        userMapper.updateById(user);
    }

    @Override
    public void updateRegistrationId(Long userId, String registrationId) {
        User user = new User();
        user.setUserId(userId);
        user.setRegistrationId(registrationId);
        userMapper.updateById(user);
    }

    @Override
    public void deleteOrder(Long orderId) {
        Order order = new Order();
        order.setOrderId(orderId);
        order.setUserIsDelete(1);
        orderMapper.updateById(order);
    }
}