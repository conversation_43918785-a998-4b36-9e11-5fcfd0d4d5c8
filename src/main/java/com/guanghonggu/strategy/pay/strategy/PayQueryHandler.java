package com.guanghonggu.strategy.pay.strategy;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.guanghonggu.dto.ResultDTO;
import com.guanghonggu.entity.CleaningItemShopOrder;
import com.guanghonggu.entity.Order;
import com.guanghonggu.entity.Wallet;
import com.guanghonggu.entity.WalletTransaction;
import com.guanghonggu.handler.exception.BizException;
import com.guanghonggu.mapper.CleaningItemShopOrderMapper;
import com.guanghonggu.mapper.OrderMapper;
import com.guanghonggu.mapper.UserCouponMapper;
import com.guanghonggu.mapper.WalletMapper;
import com.guanghonggu.mapper.WalletTransactionMapper;
import com.guanghonggu.service.impl.CouponServiceImpl;
import com.guanghonggu.service.impl.OrderServiceImpl;
import com.guanghonggu.strategy.pay.wechat.WechatPayConfig;
import com.guanghonggu.util.HttpClientUtil;
import com.wechat.pay.java.service.payments.model.Transaction;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.OffsetDateTime;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @author：zzn
 * @description：
 * @createData：2025/6/19 11:21
 */
@Slf4j
@Component
public class PayQueryHandler {

    @Autowired
    private OrderMapper orderMapper;
    @Autowired
    private WalletMapper walletMapper;
    @Autowired
    private WalletTransactionMapper walletTransactionMapper;

    @Autowired
    private OrderServiceImpl orderService;

    @Autowired
    private CouponServiceImpl couponService;

    @Autowired
    private CleaningItemShopOrderMapper cleaningItemShopOrderMapper;

    @Autowired
    private WechatPayConfig wechatPayConfig;

    @Autowired
    private UserCouponMapper userCouponMapper;

    /**
     * 查询订单基础信息
     */
    public Order getOrderInfo(String orderNumber) {
        LambdaQueryWrapper<Order> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(Order::getOrderId, Order::getOrderType, Order::getUserId, Order::getPayMethods,
                        Order::getCouponId)
                .eq(Order::getOrderNumber, orderNumber);
        return orderMapper.selectOne(wrapper);
    }

    /**
     * 下单支付成功处理：更新订单、钱包流水、发券
     */
    public ResultDTO<Map<String, Object>> handlePaySuccess(Order order, String outTradeNo, String platformTradeNo, String payTime, BigDecimal amountYuan) {
        HashMap<String, Object> resMap = new HashMap<>();

        switch (order.getOrderType()) {
            case 2:
            case 3:
                orderService.updateOrderPayment(outTradeNo, platformTradeNo, payTime, 0);
                orderService.updateWalletTransaction(outTradeNo, platformTradeNo, amountYuan, 1);
                break;
            default:
                throw new RuntimeException("订单类型错误");
        }

        Map<String, Object> coupon = couponService.grantRandomCoupon(order.getUserId());
        resMap.put("status", "SUCCESS");
        if (coupon != null) {
            resMap.put("grantedCoupon", coupon);
        }
        return ResultDTO.success("支付成功", resMap);
    }

    /**
     * 下单支付已关闭处理：更新订单、钱包流水、组合退款
     */
    public ResultDTO<Map<String, Object>> handlePayClosed(Order order, String outTradeNo) {
        orderService.updateOrderPayment(outTradeNo, null, null, 4);
        orderService.updateWalletTransaction(outTradeNo, null, null,2);
//        orderService.deleteWalletTransaction(outTradeNo, 2);

        if (order.getPayMethods() != null && (order.getPayMethods() == 4 || order.getPayMethods() == 5 || order.getPayMethods() == 7)) {
            WalletTransaction originalTransaction = walletTransactionMapper.selectOne(
                    new LambdaQueryWrapper<WalletTransaction>()
                            .eq(WalletTransaction::getOutTradeNo, outTradeNo)
                            .eq(WalletTransaction::getTransactionType, 2)
            );
            if (originalTransaction != null) {
                Wallet wallet = walletMapper.selectWalletByIdForUpdate(originalTransaction.getWalletId());
                wallet.setBalance(wallet.getBalance().add(originalTransaction.getBalanceAmount()));
                wallet.setRechargeBalance(wallet.getRechargeBalance().add(originalTransaction.getRechargeAmount()));
                walletMapper.updateById(wallet);
            }
        }
        // 退还优惠券
        Long couponId = order.getCouponId();
        if (couponId != null) {
            int rows = userCouponMapper.updateUsedIfUnused(couponId, 1);
        }

        HashMap<String, Object> resMap = new HashMap<>();
        resMap.put("status", "CLOSED");
        return ResultDTO.success("支付已关闭", resMap);
    }

    /**
     * 构造未支付返回
     */
    public ResultDTO<Map<String, Object>> handleNotPay() {
        HashMap<String, Object> resMap = new HashMap<>();
        resMap.put("status", "NOTPAY");
        return ResultDTO.success("未支付", resMap);
    }


    /**
     * 获取待处理的充值交易
     */
    public WalletTransaction getPendingRechargeTransaction(String outTradeNo) {
        return walletTransactionMapper.selectOne(
                new LambdaQueryWrapper<WalletTransaction>()
                        .eq(WalletTransaction::getOutTradeNo, outTradeNo)
                        .eq(WalletTransaction::getTransactionType, 1)
                        .eq(WalletTransaction::getStatus, 0)
        );
    }

    /**
     * 处理充值成功：更新交易记录 + 钱包金额 + 发券 + #维护用户vip等级
     */
    public void handleRechargeSuccess(WalletTransaction transaction, String externalTradeNo, Date payTime, BigDecimal amount, int channel) {
        Wallet wallet = walletMapper.selectWalletByIdForUpdate(transaction.getWalletId());
        if (wallet == null) {
            throw new RuntimeException("钱包不存在，walletId = " + transaction.getWalletId());
        }

        // 更新交易记录
        transaction.setStatus(1); // 成功
        transaction.setExternalTradeNo(externalTradeNo);
        transaction.setTransactionTime(payTime);
        walletTransactionMapper.updateById(transaction);

        // 更新钱包金额
        wallet.setRechargeBalance(wallet.getBalance().add(amount));
        wallet.setTotalIncome(wallet.getTotalIncome().add(amount));
        walletMapper.updateById(wallet);

        // 发券
        couponService.giveDiscountCoupons(wallet.getUserId(), amount);

        log.info("【充值成功】渠道：{}，金额：{}，walletId：{}", channel == 1  ? "微信" : "支付宝", amount, wallet.getWalletId());
    }

    /**
     * 处理充值关闭/失败
     */
    public void handleRechargeClosed(WalletTransaction transaction) {
//        transaction.setStatus(2); // 失败
//        transaction.setUpdateTime(new Date());
//        walletTransactionMapper.updateById(transaction);
        orderService.deleteWalletTransaction(transaction.getOutTradeNo(), 1);
        log.warn("【充值失败或关闭】订单号：{}", transaction.getOutTradeNo());
    }


    /**
     * 幂等处理提现失败：只加一次余额，删除记录
     */
    public void handleFailedWithdraw(WalletTransaction walletTransaction, Wallet wallet, String outTradeNo) {
        int updated = walletTransactionMapper.updateStatusIfUnprocessed(walletTransaction.getTransactionId(), null, 0, 2);
        if (updated == 0) {
            log.info("订单 {} 已处理过失败逻辑，跳过", outTradeNo);
            return;
        }
        // 回滚余额
        wallet.setBalance(wallet.getBalance().add(walletTransaction.getAmount()));
        walletMapper.updateById(wallet);

        // 删除记录（或更新为失败）
//        orderService.deleteWalletTransaction(outTradeNo, 3);
        // 回滚 wallet_monthly_summary.total_withdraw
        // 回滚扣税提现金额
//        try {
//            Long userId = wallet.getUserId();
//            Integer userType = wallet.getUserType();
//            BigDecimal withdrawAmount = walletTransaction.getAmount();
//
//            YearMonth now = YearMonth.now();
//            int year = now.getYear();
//            int month = now.getMonthValue();
//
//            WalletMonthlySummary summary = walletMonthlySummaryMapper.selectByUserAndMonth(userId, userType, year, month);
//            if (summary != null) {
//                summary.setTotalWithdraw(summary.getTotalWithdraw().subtract(withdrawAmount));
//                // 防止负数
//                if (summary.getTotalWithdraw().compareTo(BigDecimal.ZERO) < 0) {
//                    summary.setTotalWithdraw(BigDecimal.ZERO);
//                }
//                walletMonthlySummaryMapper.updateById(summary);
//            } else {
//                log.warn("提现失败回滚时未找到月度汇总记录，userId: {}, month: {}-{}", userId, year, month);
//            }
//        } catch (Exception e) {
//            log.error("回滚月度提现金额失败：{}", e.getMessage(), e);
//        }
    }

    /**
     * 查询需要查单的order
     *
     * @return
     */
    public List<Order> getUnpaidOrders() {
        LambdaQueryWrapper<Order> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Order::getStatus, 7); // 待支付
        return orderMapper.selectList(wrapper);
    }


    public CleaningItemShopOrder getShopOrderInfo(String orderNumber) {
        LambdaQueryWrapper<CleaningItemShopOrder> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(CleaningItemShopOrder::getId)
                .eq(CleaningItemShopOrder::getOrderNumber, orderNumber);
        return cleaningItemShopOrderMapper.selectOne(wrapper);
    }

    /**
     * 处理商城支付成功  更新订单状态，更新钱包交易记录
     */
    public void handleShopPaySuccess(CleaningItemShopOrder order, Transaction transaction) {
        String successTime = transaction.getSuccessTime();
        OffsetDateTime offsetDateTime = OffsetDateTime.parse(successTime);
        Date payTime = Date.from(offsetDateTime.toInstant());
        // 1. 更新订单状态为“待发货”
        CleaningItemShopOrder updateOrder = new CleaningItemShopOrder();
        updateOrder.setId(order.getId());
        updateOrder.setStatus(0); // 0 = 待发货
        updateOrder.setPaymentMethod(1); // 微信
        updateOrder.setPayOrderNumber(transaction.getTransactionId());
        updateOrder.setPayTime(payTime);
        cleaningItemShopOrderMapper.updateById(updateOrder);

        // 2. 更新钱包交易记录
        WalletTransaction walletTxn = walletTransactionMapper.selectOne(
                new LambdaQueryWrapper<WalletTransaction>()
                        .eq(WalletTransaction::getOutTradeNo, transaction.getOutTradeNo())
                        .eq(WalletTransaction::getStatus, 0)
        );

        if (walletTxn != null) {
            walletTxn.setExternalTradeNo(transaction.getTransactionId());
            walletTxn.setStatus(1); // 成功
            walletTxn.setTransactionTime(new Date());
            walletTransactionMapper.updateById(walletTxn);
        }

        log.info("微信支付成功处理完成，订单号：{}", transaction.getOutTradeNo());
    }

    /**
     * 保洁商城 - 微信支付关闭
     * @param order
     * @param transaction
     */
    public void handleShopPayClosed(CleaningItemShopOrder order, Transaction transaction) {
        // 1. 更新订单状态为已取消
        CleaningItemShopOrder closedOrder = new CleaningItemShopOrder();
        closedOrder.setId(order.getId());
        closedOrder.setStatus(5); // 5 = 已取消支付
        cleaningItemShopOrderMapper.updateById(closedOrder);

        String outTradeNo = transaction.getOutTradeNo();
        orderService.updateWalletTransaction(outTradeNo, null, null,2);

        // 删除未支付交易记录
//        orderService.deleteWalletTransaction(outTradeNo, 2);
        // 组合支付：退款钱包支付的金额
        if (order.getPaymentMethod() != null && (order.getPaymentMethod() == 4 || order.getPaymentMethod() == 5 || order.getPaymentMethod() == 7)) {
            WalletTransaction originalTransaction = walletTransactionMapper.selectOne(
                    new LambdaQueryWrapper<WalletTransaction>()
                            .eq(WalletTransaction::getOutTradeNo, outTradeNo)
                            .eq(WalletTransaction::getTransactionType, 2)
            );
            if (originalTransaction != null) {
                Wallet wallet = walletMapper.selectWalletByIdForUpdate(originalTransaction.getWalletId());
                wallet.setBalance(wallet.getBalance().add(originalTransaction.getBalanceAmount()));
                wallet.setRechargeBalance(wallet.getRechargeBalance().add(originalTransaction.getRechargeAmount()));
                walletMapper.updateById(wallet);
            }
        }
        log.info("微信支付已关闭处理完成，订单号：{}", transaction.getOutTradeNo());
    }

    /**
     * 钱包支付，支付后在这里进行更新订单、插入交易记录
     * @param pay
     * @param orderNumber
     * @return
     */
    public void walletPayUpdateTransaction(Map<String, Object> pay, String orderNumber, Integer status) {
        BigDecimal usedBalanceAmount = new BigDecimal(pay.get("usedBalanceAmount").toString());
        BigDecimal usedRechargeAmount = new BigDecimal(pay.get("usedRechargeAmount").toString());
        // 更新交易记录
        LambdaQueryWrapper<WalletTransaction> walletTransactionLambdaQueryWrapper = new LambdaQueryWrapper<>();
        walletTransactionLambdaQueryWrapper.eq(WalletTransaction::getOutTradeNo, orderNumber)
                .eq(WalletTransaction::getTransactionType, 2);
        WalletTransaction record = new WalletTransaction();
        record.setBalanceAmount(usedBalanceAmount);
        record.setRechargeAmount(usedRechargeAmount);
        record.setStatus(status);
        walletTransactionMapper.update(record, walletTransactionLambdaQueryWrapper);
    }

    public void walletPayUpdateOrder(Long orderId) {
        Date date = new Date();
        // 修改订单状态
        Order updateorder = orderMapper.selectById(orderId);
        if (updateorder == null) {
            throw new BizException("订单不存在");
        }
        updateorder.setStatus(0); // 已支付，待分配或确认状态
        updateorder.setPayTime(date);
        orderMapper.updateById(updateorder);
    }

    /**
     * 更新清洁物品商城订单
     * @param id
     */
    public void walletPayUpdateShopOrder(Long id) {
        Date date = new Date();
        // 修改订单状态
        CleaningItemShopOrder cleaningItemShopOrder = cleaningItemShopOrderMapper.selectById(id);
        if (cleaningItemShopOrder == null) {
            throw new BizException("订单不存在");
        }
        cleaningItemShopOrder.setStatus(0); // 待发货
        cleaningItemShopOrder.setPayTime(date);
        cleaningItemShopOrderMapper.updateById(cleaningItemShopOrder);
    }

    /**
     * 根据code获取code
     * @param code
     * @return
     */
    public JSONObject getWechatMiniOpenIdByCode(String code) {
        // 换取 openid
        String url = "https://api.weixin.qq.com/sns/jscode2session?appid=" + wechatPayConfig.getMiniPrograms().getAppId()
                + "&secret=" + wechatPayConfig.getMiniPrograms().getAppSecret()
                + "&js_code=" + code
                + "&grant_type=authorization_code";

        String result = null;
        try {
            result = HttpClientUtil.doGet(url);
        } catch (Exception e) {
            log.error("微信code换取openid失败", e);
            throw new BizException("微信code换取openid失败");
        }
        JSONObject json = JSON.parseObject(result);
        if (json.get("errcode") != null) {
            throw new BizException("微信code换取openid失败：" + json.getString("errmsg"));
        }
        String openid = json.getString("openid");
        if (openid == null) {
            throw new BizException("openid 获取失败");
        }
        return json;
    }
}

