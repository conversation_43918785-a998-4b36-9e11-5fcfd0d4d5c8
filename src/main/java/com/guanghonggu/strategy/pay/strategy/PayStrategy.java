package com.guanghonggu.strategy.pay.strategy;


import com.wechat.pay.java.service.payments.model.Transaction;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

/**
 * @author：zzn
 * @description：
 * @createData：2025/5/13 9:22
 */
public interface PayStrategy {

    /**
     * 类型（1：APP微信支付，2：支付宝支付，3：钱包支付，4：钱包+APP微信支付，5：钱包+支付宝，6：微信小程序，7：钱包+微信小程序支付）
     * @return
     */
    Integer getType();
    /**
     * 发起支付
     */
    Map<String, Object> pay(Map<String, Object> map);

    /**
     * 处理异步回调
     */
    Transaction handleCallback(HttpServletRequest request);

    /**
     * 提现
     */
    Map<String, Object> withdraw(Map<String, Object> map);
}
