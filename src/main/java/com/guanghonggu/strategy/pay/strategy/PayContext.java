package com.guanghonggu.strategy.pay.strategy;

import com.guanghonggu.handler.exception.BizException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author：zzn
 * @description：
 * @createData：2025/5/13 9:20
 */
@Component
public class PayContext {
    /**
     * key：1WeChatPay、2Alipay、3WalletPay
     */
    private final Map<Integer, PayStrategy> strategyMap;

    @Autowired
    public PayContext(List<PayStrategy> strategies) {
        this.strategyMap = strategies.stream()
                .collect(Collectors.toMap(PayStrategy::getType, Function.identity()));
    }

    /**
     * 获取支付实例方法
     *
     * @param type（1：WechatPay：微信，2：AliPay：支付宝，3：WalletPay：钱包）
     * @return
     */
    public PayStrategy executePay(Integer type) {

        if (type == null) {
            throw new BizException("请选择支付方式");
        }

        return strategyMap.get(type);
    }


}
