package com.guanghonggu.strategy.pay.wallet;

import com.guanghonggu.entity.Wallet;
import com.guanghonggu.mapper.WalletMapper;
import com.guanghonggu.strategy.pay.strategy.PayStrategy;
import com.guanghonggu.strategy.pay.wechat.jsapi.WechatMiniPayStrategyService;
import com.wechat.pay.java.service.payments.model.Transaction;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

/**
 * @author：zzn
 * @description：
 * @createData：2025/7/7 15:43
 */
@Component
public class WalletAndWechatMiniPayService implements PayStrategy {

    @Autowired
    private WalletMapper walletMapper;

    @Autowired
    private WalletPayStrategyService walletPayStrategyService;

    @Autowired
    private WechatMiniPayStrategyService wechatPayStrategyService;

    @Override
    public Integer getType() {
        return 7;
    }

    @Override
    public Map<String, Object> pay(Map<String, Object> map) {
        BigDecimal actualPay = new BigDecimal(map.get("actualPaymentPrice").toString());
        Long userId = Long.parseLong(map.get("userId").toString());
        Integer userType = Integer.parseInt(map.get("userType").toString());

        // 1. 查询钱包余额
        Wallet wallet = walletMapper.selectWalletByUserForUpdate(userId, userType);
        BigDecimal walletBalance = wallet.getBalance();
        BigDecimal rechargeBalance = wallet.getRechargeBalance();
        BigDecimal total = walletBalance.add(rechargeBalance);
        HashMap<String, Object> resMap = new HashMap<>();
        // 2. 尝试钱包支付（能支付多少就支付多少）
        BigDecimal walletUsed = total.min(actualPay);
        if (walletUsed.compareTo(BigDecimal.ZERO) > 0) {
            map.put("actualPaymentPrice", walletUsed);
            Map<String, Object> pay = walletPayStrategyService.pay(map);// 复用 WalletPay 内部逻辑
            resMap.put("usedBalanceAmount", pay.get("usedBalanceAmount"));
            resMap.put("usedRechargeAmount", pay.get("usedRechargeAmount"));
        }

        // 3. 剩余部分用微信支付
        BigDecimal remaining = actualPay.subtract(walletUsed);
        if (remaining.compareTo(BigDecimal.ZERO) > 0) {
            map.put("actualPaymentPrice", remaining);
            Map<String, Object> pay = wechatPayStrategyService.pay(map);
            resMap.put("sign", pay.get("sign"));
            resMap.put("nonceStr", pay.get("nonceStr"));
            resMap.put("timestamp", pay.get("timestamp"));
            resMap.put("prepayId", pay.get("prepayId"));
            resMap.put("package", pay.get("package"));
        } else {
            resMap.put("walletPayAll", true);
        }

        resMap.put("result", true);
        return resMap;
    }

    @Override
    public Transaction handleCallback(HttpServletRequest request) {
        return null;
    }

    @Override
    public Map<String, Object> withdraw(Map<String, Object> map) {
        return Collections.emptyMap();
    }
}
