package com.guanghonggu.strategy.pay.wallet;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.guanghonggu.entity.Caregiver;
import com.guanghonggu.entity.Order;
import com.guanghonggu.entity.User;
import com.guanghonggu.entity.Wallet;
import com.guanghonggu.entity.WalletTransaction;
import com.guanghonggu.handler.exception.BizException;
import com.guanghonggu.mapper.CaregiverMapper;
import com.guanghonggu.mapper.OrderMapper;
import com.guanghonggu.mapper.UserMapper;
import com.guanghonggu.mapper.WalletMapper;
import com.guanghonggu.mapper.WalletTransactionMapper;
import com.guanghonggu.strategy.pay.strategy.PayStrategy;
import com.guanghonggu.util.MD5Utils;
import com.wechat.pay.java.service.payments.model.Transaction;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * @author：zzn
 * @description：钱包支付策略
 * @createData：2025/5/13 9:25
 */
@Component
public class WalletPayStrategyService implements PayStrategy {

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private CaregiverMapper caregiverMapper;

    @Autowired
    private WalletMapper walletMapper;

    @Autowired
    private WalletTransactionMapper walletTransactionMapper;

    @Autowired
    private OrderMapper orderMapper;

    @Override
    public Integer getType() {
        return 3;
    }

    @Override
    public Map<String, Object> pay(Map<String, Object> map) {
        Long userId = Long.parseLong(map.get("userId").toString());
        int userType = Integer.parseInt(map.get("userType").toString());
        String payPassword = (String) map.get("payPassword");
        if (payPassword == null) {
            throw new BizException("请输入支付密码");
        }
        BigDecimal orderTotalAmount = new BigDecimal(map.get("actualPaymentPrice").toString());

        String originalPayPassword;

        // 1. 获取用户原始支付密码（加密后的）
        if (userType == 1) {
            User user = userMapper.selectById(userId);
            if (user == null || user.getPayPassword() == null) {
                throw new BizException("未设置支付密码");
            }
            originalPayPassword = user.getPayPassword();
        } else {
            Caregiver caregiver = caregiverMapper.selectById(userId);
            if (caregiver == null || caregiver.getPayPassword() == null) {
                throw new BizException("未设置支付密码");
            }
            originalPayPassword = caregiver.getPayPassword();
        }

        // 2. 校验支付密码
        boolean verify = MD5Utils.verify(payPassword, originalPayPassword);
        if (!verify) {
            throw new BizException("支付密码错误");
        }

        // 3. 查询钱包余额（加锁）
        Wallet wallet = walletMapper.selectWalletByUserForUpdate(userId, userType);
        if (wallet == null) {
            throw new BizException("钱包账户不存在");
        }

        BigDecimal rechargeBalance = wallet.getRechargeBalance() == null ? BigDecimal.ZERO : wallet.getRechargeBalance();
        BigDecimal balance = wallet.getBalance() == null ? BigDecimal.ZERO : wallet.getBalance();

        BigDecimal totalAvailable = rechargeBalance.add(balance);

        // 4. 判断余额是否充足
        if (totalAvailable.compareTo(orderTotalAmount) < 0) {
            throw new BizException("余额不足");
        }

        // 5. 优先扣除储值余额
        BigDecimal remaining = orderTotalAmount;
        BigDecimal usedRechargeAmount = BigDecimal.ZERO;
        BigDecimal usedBalanceAmount = BigDecimal.ZERO;

        if (rechargeBalance.compareTo(remaining) >= 0) {
            // 储值余额足够支付
            usedRechargeAmount = remaining;
            rechargeBalance = rechargeBalance.subtract(remaining);
        } else {
            // 储值余额不足，先扣储值余额，再扣普通余额
            usedRechargeAmount = rechargeBalance;
            remaining = remaining.subtract(rechargeBalance);
            rechargeBalance = BigDecimal.ZERO;

            usedBalanceAmount = remaining;
            balance = balance.subtract(remaining);
        }

        // 6. 更新钱包余额
        wallet.setRechargeBalance(rechargeBalance);
        wallet.setBalance(balance);

        int updateCount = walletMapper.updateById(wallet);
        if (updateCount == 0) {
            throw new BizException("余额扣除失败，请稍后重试");
        }

        // 9. 返回结果
        Map<String, Object> resMap = new HashMap<>();
        resMap.put("usedBalanceAmount", usedBalanceAmount);
        resMap.put("usedRechargeAmount", usedRechargeAmount);
        return resMap;
    }


    @Override
    public Transaction handleCallback(HttpServletRequest request) {
        return null;
    }

    @Override
    public Map<String, Object> withdraw(Map<String, Object> map) {
        return Collections.emptyMap();
    }
}
