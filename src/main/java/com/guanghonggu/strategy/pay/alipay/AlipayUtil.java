package com.guanghonggu.strategy.pay.alipay; // 指定当前类所在的包路径

import com.alibaba.fastjson.JSON;
import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.DefaultAlipayClient;
import com.alipay.api.domain.AlipayTradeAppPayModel;
import com.alipay.api.internal.util.AlipaySignature;
import com.alipay.api.request.AlipayFundTransUniTransferRequest;
import com.alipay.api.request.AlipayTradeAppPayRequest;
import com.alipay.api.request.AlipayTradeQueryRequest;
import com.alipay.api.response.AlipayFundTransUniTransferResponse;
import com.alipay.api.response.AlipayTradeQueryResponse;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;


/**
 * 支付宝支付工具类
 * 用于封装和执行支付宝网页支付逻辑
 */
@Slf4j
@Service
@Getter
public class AlipayUtil {

    /**
     * 支付宝网关地址（沙箱或正式环境）
     */
    @Value("${alipay.gateway-url}")
    private String gatewayUrl;

    /**
     * 支付宝分配给你的应用 APPID
     */
    @Value("${alipay.app-id}")
    private String appId;

    /**
     * 商户自己生成的私钥，用于签名请求
     */
    @Value("${alipay.private-key}")
    private String merchantPrivateKey;

    /**
     * 支付宝公钥，用于验签回调
     */
    @Value("${alipay.alipay-public-key}")
    private String alipayPublicKey;

    /**
     * 编码格式，一般为 UTF-8
     */
    @Value("${alipay.charset}")
    private String charset;

    /**
     * 签名类型，一般为 RSA2
     */
    @Value("${alipay.sign-type}")
    private String signType;

    /**
     * 同步回调地址：用户支付完成后，支付宝前端跳回页面
     */
    @Value("${alipay.return-url}")
    private String returnUrl;

    /**
     * 异步通知地址：支付宝服务器后台回调告知支付/退款结果
     */
    @Value("${alipay.notify-url}")
    private String notifyUrl;

    @Value("${alipay.rechargeNotifyUrl}")
    private String rechargeNotifyUrl;


    // ------------------ 功能方法 ------------------

    /**
     * 1. APP 下单 → 返回给客户端一串 orderStr，
     *    在 APP 端会传给支付宝 SDK 发起支付
     *
     * @param orderNo  商户订单号
     * @param amount   支付金额（字符串格式）
     * @param subject  商品标题
     * @return 支付宝 SDK 所需的 orderStr 参数串
     * @throws AlipayApiException 网络或 API 错误时抛出
     */
    /**
     * APP 支付：生成 orderStr 给 App 调用 SDK
     */
    public String buildAppOrderStr(String orderNo, String amount, String subject, String transactionType) throws AlipayApiException {
        DefaultAlipayClient client = new DefaultAlipayClient(
                gatewayUrl, appId, merchantPrivateKey,
                "json", charset, alipayPublicKey, signType
        );

        AlipayTradeAppPayModel model = new AlipayTradeAppPayModel();
        model.setOutTradeNo(orderNo);
        model.setTotalAmount(amount);
        model.setSubject(subject);
        model.setProductCode("QUICK_MSECURITY_PAY");

        AlipayTradeAppPayRequest request = new AlipayTradeAppPayRequest();
        request.setBizModel(model);
        if ("1".equals(transactionType)) {
            // 充值
            request.setNotifyUrl(notifyUrl);
        } else if ("2".equals(transactionType)) {
            // 下单支付
            request.setNotifyUrl(rechargeNotifyUrl);
        }

        return client.sdkExecute(request).getBody();
    }

    /**
     * 查询订单状态
     */
    public AlipayTradeQueryResponse queryOrder(String orderNo) throws AlipayApiException {
        DefaultAlipayClient client = new DefaultAlipayClient(
                gatewayUrl, appId, merchantPrivateKey,
                "json", charset, alipayPublicKey, signType
        );

        AlipayTradeQueryRequest request = new AlipayTradeQueryRequest();
        request.setBizContent("{\"out_trade_no\":\"" + orderNo + "\"}");

        return client.execute(request);
    }

    /**
     * 恢复方法（重试失败时）
     */
    public Object recover(AlipayApiException ex, Object... args) {
        throw new RuntimeException("支付宝接口调用失败：" + ex.getErrMsg(), ex);
    }

    /**
     * 验签
     */
    public boolean checkSignature(Map<String, String> params) throws AlipayApiException {
        return AlipaySignature.rsaCheckV1(params, alipayPublicKey, charset, signType);
    }

    public AlipayFundTransUniTransferResponse transferToAlipay(String alipayAccount, BigDecimal amount, String outTradeNo,String realName) {
        AlipayClient client = new DefaultAlipayClient(
                gatewayUrl, appId, merchantPrivateKey, "json", charset, alipayPublicKey, signType);

        AlipayFundTransUniTransferRequest request = new AlipayFundTransUniTransferRequest();

        Map<String, Object> bizContent = new HashMap<>();
        bizContent.put("out_biz_no", outTradeNo);
        bizContent.put("trans_amount", amount.toPlainString());
        bizContent.put("product_code", "TRANS_ACCOUNT_NO_PWD");
        bizContent.put("biz_scene", "DIRECT_TRANSFER");

        Map<String, String> payeeInfo = new HashMap<>();
        payeeInfo.put("identity", alipayAccount);                  // 沙箱 UID
        payeeInfo.put("identity_type", "ALIPAY_USER_ID");          // UID 模式
        payeeInfo.put("name", "eegnjl4606");
        //上线后更换
//        payeeInfo.put("identity_type", "ALIPAY_LOGON_ID");         // 登录账号类型
//
//        // 如果你开启实名校验，可以传真实姓名
//        if (realName != null && !realName.isEmpty()) {
//            payeeInfo.put("name", realName);                       // 可选：用户真实姓名
//        }// 沙箱用户名称（务必一致）

        bizContent.put("payee_info", payeeInfo);
        System.out.println("【最终请求】" + JSON.toJSONString(bizContent));

        request.setBizContent(JSON.toJSONString(bizContent));

        try {
            AlipayFundTransUniTransferResponse response = client.execute(request);
            System.out.println("【支付宝转账返回】" + response.getBody()); // 打印完整响应内容

            return response; // 把整个响应对象返回，而不是只返回 true/false
        } catch (AlipayApiException e) {
            e.printStackTrace();
            return null;
        }
    }
}