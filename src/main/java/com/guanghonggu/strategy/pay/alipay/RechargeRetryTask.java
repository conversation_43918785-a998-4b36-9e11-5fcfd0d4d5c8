package com.guanghonggu.strategy.pay.alipay;

import com.alipay.api.response.AlipayTradeQueryResponse;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.guanghonggu.entity.WalletTransaction;
import com.guanghonggu.mapper.WalletTransactionMapper;
import com.guanghonggu.service.AlipayAppService;
import com.guanghonggu.service.WalletService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
public class RechargeRetryTask {

    @Autowired
    private WalletTransactionMapper walletTransactionMapper;

    @Autowired
    private WalletService walletService;

    @Autowired
    private AlipayUtil alipayUtil;

    @Autowired
    private AlipayAppService alipayAppService;

    /**
     * 每 5 分钟执行一次，检查所有未处理的充值交易
     */
//    @Scheduled(cron = "0 */5 * * * ?")
    public void retryUnprocessedRecharge() {
        log.info("开始执行支付宝充值补单任务（无状态字段）");

        // 查询状态为 0（未支付）的充值记录
        List<WalletTransaction> txList = walletTransactionMapper.selectList(
                new QueryWrapper<WalletTransaction>()
                        .eq("status", 0)
        );

        for (WalletTransaction tx : txList) {
            try {
                // 调用支付宝接口查询该订单的交易状态
                AlipayTradeQueryResponse res = alipayUtil.queryOrder(tx.getOutTradeNo());

                if (res != null && "TRADE_SUCCESS".equals(res.getTradeStatus())) {
                    // 如果查到已经支付成功，手动补偿加钱逻辑（必须幂等）
                    log.info("查到订单号 {} 支付成功，执行补偿", tx.getOutTradeNo());
                    alipayAppService.handleSuccessfulRecharge(tx);
                }

            } catch (Exception e) {
                log.error(" 查询支付宝订单失败，订单号：{}", tx.getOutTradeNo(), e);
                // 不做数据库状态记录，下一轮定时任务会继续查
            }
        }

        log.info("支付宝充值补单任务执行完毕");
    }
}