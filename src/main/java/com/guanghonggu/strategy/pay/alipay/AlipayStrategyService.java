package com.guanghonggu.strategy.pay.alipay;

import com.alipay.api.AlipayApiException;
import com.guanghonggu.strategy.pay.strategy.PayStrategy;
import com.wechat.pay.java.service.payments.model.Transaction;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.util.Collections;
import java.util.Map;

/**
 * @author：zzn
 * @description：支付宝支付策略
 * @createData：2025/5/13 9:28
 */
@Component
public class AlipayStrategyService implements PayStrategy {

    @Autowired
    private AlipayUtil alipayUtil;

    @Override
    public Integer getType() {
        return 2;
    }

    @Override
    public Map<String, Object> pay(Map<String, Object> map) {
        String orderNumber = map.get("orderNumber").toString();
        String actualPaymentPrice = map.get("actualPaymentPrice").toString();
        String description = map.get("description").toString();
        String transactionType = map.get("transactionType").toString();
        String body = null;
        try {

            body = alipayUtil.buildAppOrderStr(orderNumber, actualPaymentPrice, description,transactionType);
        } catch (AlipayApiException e) {
            throw new RuntimeException(e);
        }
        map.put("alipayBody", body);
        return map;
    }

    @Override
    public Transaction handleCallback(HttpServletRequest request) {
        return null;
    }

    @Override
    public Map<String, Object> withdraw(Map<String, Object> map) {
        return Collections.emptyMap();
    }
}
