package com.guanghonggu.strategy.pay.alipay;

import lombok.Data;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Data
public class TradeDTO {

    /** 发起支付校验分组 */
    public interface PayGroup {}

    /** 查询订单校验分组 */
    public interface QueryGroup {}

    /** 申请退款校验分组 */
    public interface RefundGroup {}

    /** 商户订单号，三种场景都必填 */
    @NotBlank(message = "订单号不能为空",
            groups = {PayGroup.class, QueryGroup.class, RefundGroup.class})
    private String orderNo;

    /** 支付金额，发起支付时必填 */
    @NotNull(message = "支付金额不能为空", groups = PayGroup.class)
    @DecimalMin(value = "0.01", message = "支付金额必须 ≥ 0.01", groups = PayGroup.class)
    private BigDecimal amount;

    /** 商品标题，发起支付时必填 */
    @NotBlank(message = "商品标题不能为空", groups = PayGroup.class)
    private String subject;

    /** 退款金额，申请退款时必填 */
    @NotNull(message = "退款金额不能为空", groups = RefundGroup.class)
    @DecimalMin(value = "0.01", message = "退款金额必须 ≥ 0.01", groups = RefundGroup.class)
    private BigDecimal refundAmount;

    /** 退款原因，申请退款时可选 */
    private String refundReason;
}
