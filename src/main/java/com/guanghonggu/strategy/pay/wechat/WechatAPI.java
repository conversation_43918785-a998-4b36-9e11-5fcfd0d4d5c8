package com.guanghonggu.strategy.pay.wechat;

import com.guanghonggu.entity.Wallet;
import com.guanghonggu.entity.WalletTransaction;
import com.guanghonggu.mapper.WalletMapper;
import com.guanghonggu.mapper.WalletTransactionMapper;
import com.guanghonggu.service.impl.OrderServiceImpl;
import com.guanghonggu.strategy.pay.strategy.PayQueryHandler;
import com.guanghonggu.strategy.pay.wechat.transfer.GetTransferBillByOutNo;
import com.wechat.pay.java.core.Config;
import com.wechat.pay.java.service.payments.app.AppService;
import com.wechat.pay.java.service.payments.app.model.CloseOrderRequest;
import com.wechat.pay.java.service.payments.app.model.QueryOrderByOutTradeNoRequest;
import com.wechat.pay.java.service.payments.model.Transaction;
import lombok.extern.slf4j.Slf4j;
import java.time.OffsetDateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;


/**
 * @author：zzn
 * @description：
 * @createData：2025/5/28 9:44
 */
@Slf4j
@Component
public class WechatAPI {

    @Autowired
    private WechatPayConfig wechatPayConfig;

    @Autowired
    private WechatAPI wechatAPI;

    @Autowired
    private WalletTransactionMapper walletTransactionMapper;

    @Autowired
    private WalletMapper walletMapper;

    @Autowired
    private PayQueryHandler payQueryHandler;

    @Autowired
    private OrderServiceImpl orderServiceImpl;

    @Autowired
    private GetTransferBillByOutNo getTransferBillByOutNo;

    /**
     * 关单
     *
     * @param orderNumber 订单编号
     */
    public void closeOrder(String orderNumber) {
        Config config = WechatUtils.getInstance(wechatPayConfig);
        AppService service = new AppService.Builder().config(config).build();

        CloseOrderRequest request = new CloseOrderRequest();
        request.setMchid(wechatPayConfig.getMerchantId());
        request.setOutTradeNo(orderNumber);

        service.closeOrder(request);
    }

    /**
     * 查询订单 - 支付
     *
     * @param orderNumber 订单编号
     */
    public Transaction searchOrder(String orderNumber) {
        Config config = WechatUtils.getInstance(wechatPayConfig);
        AppService service = new AppService.Builder().config(config).build();

        QueryOrderByOutTradeNoRequest request = new QueryOrderByOutTradeNoRequest();
        request.setMchid(wechatPayConfig.getMerchantId());
        request.setOutTradeNo(orderNumber);
        Transaction transaction = service.queryOrderByOutTradeNo(request);
//        String tradeState = transaction.getTradeState().name();
//        String outTradeNo = transaction.getOutTradeNo();
        return transaction;
    }

    /**
     * 查询订单 - 微信转账（提现至微信）
     *
     * @param orderNumber 订单号
     * @return
     */
    public GetTransferBillByOutNo.TransferBillEntity searchTransferOrder(String orderNumber) {

//        GetTransferBillByOutNo client = new GetTransferBillByOutNo();
        GetTransferBillByOutNo.GetTransferBillByOutNoRequest request = new GetTransferBillByOutNo.GetTransferBillByOutNoRequest();
        request.outBillNo = orderNumber;
        GetTransferBillByOutNo.TransferBillEntity response = getTransferBillByOutNo.run(request);
        return response;
    }

    /**
     * 微信充值查单
     *
     * @param walletTransaction
     */
    @Transactional(rollbackFor = Exception.class)
    public void processOrder(WalletTransaction walletTransaction) {
        String outTradeNo = walletTransaction.getOutTradeNo();
        log.info("【微信查单任务】查询订单：{}", outTradeNo);

        Transaction transaction = wechatAPI.searchOrder(outTradeNo);
        String tradeState = transaction.getTradeState().name();
        String transactionId = transaction.getTransactionId();
        String successTime = transaction.getSuccessTime();
//        BigDecimal amountYuan = new BigDecimal(transaction.getAmount().getTotal()).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP);
        if ("SUCCESS".equals(tradeState)) {
            OffsetDateTime offsetDateTime = OffsetDateTime.parse(successTime);
            Date payTime = Date.from(offsetDateTime.toInstant());

            payQueryHandler.handleRechargeSuccess(walletTransaction, transactionId, payTime, walletTransaction.getAmount(), 1); // 1 = 微信
            log.info("【微信查单任务】充值成功并处理完成：{}", outTradeNo);
        } else if ("CLOSED".equals(tradeState)) {
            orderServiceImpl.deleteWalletTransaction(outTradeNo, 1);
            log.warn("【微信查单任务】订单已关闭：{}", outTradeNo);
        }
    }

    /**
     * 微信提现查单
     *
     * @param walletTransaction
     */
    @Transactional(rollbackFor = Exception.class)
    public void processOrder4Withdrawal(WalletTransaction walletTransaction) {
        String outTradeNo = walletTransaction.getOutTradeNo();
//        log.info("【微信提现查单任务】查询订单：{}", outTradeNo);

        GetTransferBillByOutNo.TransferBillEntity transaction = wechatAPI.searchTransferOrder(outTradeNo);
        String tradeState = transaction.getState().name();
        String transferBillNo = transaction.getTransferBillNo();
        Wallet wallet = walletMapper.selectWalletByIdForUpdate(walletTransaction.getWalletId());
        if ("SUCCESS".equals(tradeState)) {
            int updateCount = walletTransactionMapper.updateStatusIfUnprocessed(
                    walletTransaction.getTransactionId(), transferBillNo, 0, 1);

            if (updateCount == 0) {
                log.info("【微信提现查单任务】订单 {} 已被其他线程处理，跳过", outTradeNo);
                return;
            }

            log.info("【微信提现查单任务】提现成功并处理完成：{}", outTradeNo);
        } else if ("FAIL".equals(tradeState) || "CANCELLED".equals(tradeState)) {
            payQueryHandler.handleFailedWithdraw(walletTransaction, wallet, outTradeNo);

            log.warn("【微信提现查单任务】订单已关闭：{}", outTradeNo);
        }
    }
}