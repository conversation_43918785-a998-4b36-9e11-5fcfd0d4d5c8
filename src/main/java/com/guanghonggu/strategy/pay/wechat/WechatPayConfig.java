package com.guanghonggu.strategy.pay.wechat;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Component
@ConfigurationProperties(prefix = "wx")
public class WechatPayConfig {

    /**
     * 微信 App 应用配置（如公众号、小程序等）
     */
    private App app;

    /**
     * 小程序配置
     */
    private MiniPrograms miniPrograms;

    /**
     * 微信登录回调地址
     */
    private String redirectUri;

    /**
     * 支付回调地址
     */
    private String notifyUrl;

    /**
     * 商户号（可从mch-id或merchant-id中选一个）
     */
    private String merchantId;

    /**
     * 私钥路径
     */
    private String privateKeyPath;

    /**
     * 平台公钥路径
     */
    private String publicKeyPath;

    /**
     * 平台公钥ID
     */
    private String publicKeyId;

    /**
     * 商户证书序列号
     */
    private String merchantSerialNumber;

    /**
     * API v3 密钥
     */
    private String apiV3Key;

    /**
     * 转账回调地址
     */
    private String transferNotifyUrl;

    /**
     * 商家转账id
     */
    private String transferSceneId;

    /**
     * 充值回调地址
     */
    private String rechargeNotifyUrl;

    /**
     * 保洁物品商城订单回调地址
     */
    private String shopOrderNotifyUrl;

    @Data
    public static class App {
        private String appId;
        private String appSecret;
    }

    @Data
    public static class MiniPrograms {
        private String appId;
        private String appSecret;
    }
}
