package com.guanghonggu.strategy.pay.wechat;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.guanghonggu.entity.Caregiver;
import com.guanghonggu.entity.CleaningItemShopOrder;
import com.guanghonggu.entity.Order;
import com.guanghonggu.entity.WalletTransaction;
import com.guanghonggu.mapper.CleaningItemShopOrderMapper;
import com.guanghonggu.mapper.OrderMapper;
import com.guanghonggu.mapper.WalletTransactionMapper;
import com.guanghonggu.service.CleaningItemShopOrderService;
import com.guanghonggu.service.OrderService;
import com.guanghonggu.strategy.pay.strategy.PayQueryHandler;
import com.guanghonggu.strategy.pay.wechat.transfer.CancelTransfer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @author：zzn
 * @description：
 * @createData：2025/5/22 8:48
 */
@Slf4j
@Configuration
@EnableScheduling
public class WechatOrderCheckScheduler {
    @Autowired
    private OrderMapper orderMapper;

    @Autowired
    private OrderService orderService;

    @Autowired
    private WechatAPI wechatAPI;

    @Autowired
    private CancelTransfer cancelTransfer;

    @Autowired
    private WalletTransactionMapper walletTransactionMapper;

    @Autowired
    private PayQueryHandler payQueryHandler;

    @Autowired
    private CleaningItemShopOrderMapper cleaningItemShopOrderMapper;

    @Autowired
    private CleaningItemShopOrderService cleaningItemShopOrderService;

    /**
     * 订单 - 微信查单接口
     */
    @Scheduled(cron = "*/50 * * * * ?")
    public void checkUnpaidOrders() {
        List<Order> unpaidOrders = payQueryHandler.getUnpaidOrders();

        for (Order order : unpaidOrders) {
            try {
                log.info("检查订单：{}", order.getOrderNumber());
                orderService.searchWechatByOrderNumber(order.getOrderNumber());
            } catch (Exception e) {
                log.error("查询订单 [{}] 状态失败：{}", order.getOrderNumber(), e.getMessage(), e);
            }
        }
    }

    /**
     * 订单 - 微信关单接口
     */
    @Scheduled(cron = "0 */1 * * * ?")
    public void closeOrder() {
        // 当前时间减去2分钟
        LocalDateTime expireTime = LocalDateTime.now().minusMinutes(2);

        // 构建查询：未支付 + 创建时间早于2分钟前
        LambdaQueryWrapper<Order> wrapper = new LambdaQueryWrapper<>();
        wrapper.le(Order::getCreateTime, expireTime)
                .eq(Order::getStatus, 7); // 7表示“未支付”

        List<Order> orderList = orderMapper.selectList(wrapper);

        for (Order order : orderList) {
            // 调用微信关单接口
            try {
                wechatAPI.closeOrder(order.getOrderNumber());
//                Order updateOrder = new Order();
//                updateOrder.setOrderId(order.getOrderId());
//                updateOrder.setStatus(4);
//                orderMapper.updateById(updateOrder);
                log.info("订单 {} 已关闭", order.getOrderNumber());
            } catch (Exception e) {
                log.error("关闭订单 {} 失败", order.getOrderNumber(), e);
            }
        }
    }

    /**
     * 微信提现 - 查单接口
     */
    @Scheduled(cron = "*/50 * * * * ?")
    public void selectWithdrawOrders() {

        List<WalletTransaction> pendingList = walletTransactionMapper.selectList(
                new LambdaQueryWrapper<WalletTransaction>()
                        .eq(WalletTransaction::getTransactionType, 3)
                        .eq(WalletTransaction::getStatus, 0)
                        .eq(WalletTransaction::getPaymentChannel, 1)
        );

        if (CollectionUtils.isEmpty(pendingList)) {
            return;
        }

        for (WalletTransaction walletTransaction : pendingList) {
            try {
                // 每笔订单交给代理执行事务方法
                wechatAPI.processOrder4Withdrawal(walletTransaction);
            } catch (Exception e) {
                log.error("【微信查单任务】处理订单 {} 异常", walletTransaction.getOutTradeNo(), e);
            }
        }
    }

    /**
     * 微信转账 - 取消转账 - 关单
     */
    @Scheduled(cron = "0 */1 * * * ?")
    public void cancelTransfer() {
        LocalDateTime expireTime = LocalDateTime.now().minusMinutes(5);

        LambdaQueryWrapper<WalletTransaction> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(WalletTransaction::getTransactionType, 3)   // 提现
                .eq(WalletTransaction::getStatus, 0)            // 未处理
                .eq(WalletTransaction::getPaymentChannel, 1)    // 微信
                .le(WalletTransaction::getTransactionTime, expireTime);

        List<WalletTransaction> transactionList = walletTransactionMapper.selectList(wrapper);

        if (CollectionUtils.isEmpty(transactionList)) {
//            log.info("【微信关单任务】暂无超时未支付的充值订单");
            return;
        }

        for (WalletTransaction tx : transactionList) {
            String outTradeNo = tx.getOutTradeNo();
            try {
                cancelTransfer.cancelTransfer(tx);

            } catch (Exception e) {
                log.error("【微信关单任务】关闭订单 {} 失败", outTradeNo, e);
            }
        }
    }

    /**
     * 微信充值 - 查单接口
     */
    @Scheduled(cron = "*/50 * * * * ?")
    public void checkUnprocessedRechargeOrders() {

        List<WalletTransaction> pendingList = walletTransactionMapper.selectList(
                new LambdaQueryWrapper<WalletTransaction>()
                        .eq(WalletTransaction::getTransactionType, 1)
                        .eq(WalletTransaction::getStatus, 0)
                        .eq(WalletTransaction::getPaymentChannel, 1)
        );

        if (CollectionUtils.isEmpty(pendingList)) {
            return;
        }

        for (WalletTransaction walletTransaction : pendingList) {
            try {
                // 每笔订单交给代理执行事务方法
                wechatAPI.processOrder(walletTransaction);
            } catch (Exception e) {
                log.error("【微信查单任务】处理订单 {} 异常", walletTransaction.getOutTradeNo(), e);
            }
        }
    }

    /**
     * 微信充值 - 关单接口
     */
    @Scheduled(cron = "0 */1 * * * ?")
    public void closeUnpaidRechargeOrders() {
//        log.info("【微信关单任务】开始检查未支付的充值订单");

        // 2分钟未支付的订单
        LocalDateTime expireTime = LocalDateTime.now().minusMinutes(2);

        LambdaQueryWrapper<WalletTransaction> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(WalletTransaction::getTransactionType, 1)   // 充值
                .eq(WalletTransaction::getStatus, 0)            // 未处理
                .eq(WalletTransaction::getPaymentChannel, 1)    // 微信
                .le(WalletTransaction::getTransactionTime, expireTime);

        List<WalletTransaction> transactionList = walletTransactionMapper.selectList(wrapper);

        if (CollectionUtils.isEmpty(transactionList)) {
//            log.info("【微信关单任务】暂无超时未支付的充值订单");
            return;
        }

        for (WalletTransaction tx : transactionList) {
            String outTradeNo = tx.getOutTradeNo();
            try {
                wechatAPI.closeOrder(outTradeNo); // 微信关单
                log.info("【微信关单任务】订单 {} 已提交关闭", outTradeNo);

                // 更新本地状态为失败（2=失败）
                tx.setStatus(2);
                walletTransactionMapper.updateById(tx);

            } catch (Exception e) {
                log.error("【微信关单任务】关闭订单 {} 失败", outTradeNo, e);
            }
        }

        log.info("【微信关单任务】执行完成");
    }

    /**
     * 保洁商城订单 - 微信查单接口
     */
    @Scheduled(cron = "*/50 * * * * ?")
    public void checkShopOrders() {

        LambdaQueryWrapper<CleaningItemShopOrder> cleaningItemShopOrderLambdaQueryWrapper = new LambdaQueryWrapper<>();
        cleaningItemShopOrderLambdaQueryWrapper.eq(CleaningItemShopOrder::getStatus, 4);
        List<CleaningItemShopOrder> cleaningItemShopOrders = cleaningItemShopOrderMapper.selectList(cleaningItemShopOrderLambdaQueryWrapper);

        for (CleaningItemShopOrder order : cleaningItemShopOrders) {
            try {
                log.info("检查保洁商城订单订单：{}", order.getOrderNumber());
                cleaningItemShopOrderService.searchWechatByOrderNumber(order.getOrderNumber());
            } catch (Exception e) {
                log.error("查询订单 [{}] 状态失败：{}", order.getOrderNumber(), e.getMessage(), e);
            }
        }
    }
    /**
     * 保洁商城订单 - 微信关单接口
     */
    @Scheduled(cron = "0 */1 * * * ?")
    public void closeShopOrder() {
        // 当前时间减去2分钟
        LocalDateTime expireTime = LocalDateTime.now().minusMinutes(2);

        // 构建查询：未支付 + 创建时间早于2分钟前
        LambdaQueryWrapper<CleaningItemShopOrder> wrapper = new LambdaQueryWrapper<>();
        wrapper.le(CleaningItemShopOrder::getCreateTime, expireTime)
                .eq(CleaningItemShopOrder::getStatus, 4); // 4表示“未支付”

        List<CleaningItemShopOrder> orderList = cleaningItemShopOrderMapper.selectList(wrapper);

        for (CleaningItemShopOrder order : orderList) {
            // 调用微信关单接口
            try {
                wechatAPI.closeOrder(order.getOrderNumber());
                CleaningItemShopOrder cleaningItemShopOrder = new CleaningItemShopOrder();
                cleaningItemShopOrder.setId(order.getId());
                cleaningItemShopOrder.setStatus(5);

                cleaningItemShopOrderMapper.updateById(cleaningItemShopOrder);
                log.info("保洁成成订单 {} 已关闭", order.getOrderNumber());
            } catch (Exception e) {
                log.error("关闭保洁商城订单 {} 失败", order.getOrderNumber(), e);
            }
        }
    }

}