package com.guanghonggu.strategy.pay.wechat.transfer;

import com.guanghonggu.strategy.pay.wechat.WechatPayConfig;
import com.guanghonggu.strategy.pay.wechat.WechatUtils;
import com.wechat.pay.java.core.Config;
import com.wechat.pay.java.core.notification.NotificationConfig;
import com.wechat.pay.java.core.notification.NotificationParser;
import com.wechat.pay.java.core.notification.RequestParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;

/**
 * @author：zzn
 * @description：
 * @createData：2025/7/8 9:32
 */
@Component
public class WechatTransferNotify {

    @Autowired
    private WechatPayConfig wechatPayConfig;

    public GetTransferBillByOutNo.TransferBillEntity handleCallback(HttpServletRequest request) {
        String signature = request.getHeader("Wechatpay-Signature");
        String serial = request.getHeader("Wechatpay-Serial");
        String nonce = request.getHeader("Wechatpay-Nonce");
        String timestamp = request.getHeader("Wechatpay-Timestamp");

        RequestParam requestParam = new RequestParam.Builder()
                .serialNumber(serial)
                .nonce(nonce)
                .signature(signature)
                .timestamp(timestamp)
                .body(WechatUtils.getRequestBody(request))
                .build();
        Config config = WechatUtils.getInstance(wechatPayConfig);
        NotificationConfig notificationConfig = (NotificationConfig) config;
        NotificationParser parser = new NotificationParser(notificationConfig);
        GetTransferBillByOutNo.TransferBillEntity transaction = parser.parse(requestParam, GetTransferBillByOutNo.TransferBillEntity.class);

        return transaction;
    }
}
