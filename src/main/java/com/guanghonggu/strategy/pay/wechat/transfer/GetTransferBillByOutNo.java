package com.guanghonggu.strategy.pay.wechat.transfer;

import com.google.gson.ExclusionStrategy;
import com.google.gson.FieldAttributes;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonSyntaxException;
import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

import java.io.IOException;
import java.io.UncheckedIOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.security.InvalidKeyException;
import java.security.KeyFactory;
import java.security.NoSuchAlgorithmException;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.SecureRandom;
import java.security.Signature;
import java.security.SignatureException;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.time.DateTimeException;
import java.time.Duration;
import java.time.Instant;
import java.util.Base64;
import java.util.Objects;
import javax.annotation.PostConstruct;
import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;

import com.guanghonggu.strategy.pay.wechat.WechatPayConfig;
import lombok.Data;
import okhttp3.Headers;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import okio.BufferedSource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 商户单号查询转账单
 */
@Component
public class GetTransferBillByOutNo {

    @Autowired
    private WechatPayConfig wechatPayConfig;

    private static String host = "https://api.mch.weixin.qq.com";
    private static String path = "/v3/fund-app/mch-transfer/transfer-bills/out-bill-no/{out_bill_no}";
    private static String method = "GET";

    private String mchid;
    private String certificateSerialNo;
    private PrivateKey privateKey;
    private String wechatPayPublicKeyId;
    private PublicKey wechatPayPublicKey;

    @PostConstruct
    public void init() {
        this.mchid = wechatPayConfig.getMerchantId();
        this.certificateSerialNo = wechatPayConfig.getMerchantSerialNumber();
        String privateKeyFilePath = wechatPayConfig.getPrivateKeyPath();
        this.privateKey = Utility.loadPrivateKeyFromPath(privateKeyFilePath);
        this.wechatPayPublicKeyId = wechatPayConfig.getPublicKeyId();
        String publicKeyFilePath = wechatPayConfig.getPublicKeyPath();
        this.wechatPayPublicKey = Utility.loadPublicKeyFromPath(publicKeyFilePath);
    }

    /**
     * https://pay.weixin.qq.com/doc/v3/merchant/4012716437
     * <p>
     * 商家转账用户确认模式下，根据商户单号查询转账单的详细信息。
     * <p>
     * **注意**
     * <p>
     * * API只支持查询最近30天内的转账单，超过30天可以通过资金账单对账确认。
     * * 转账单中涉及金额的字段单位为“分”。
     * * **如果遇到新的错误码，请务必不要换单重试，请联系客服确认转账情况**。如果有新的错误码，会更新到此API文档中。
     * * 错误码描述字段message只供人工定位问题时做参考，系统实现时请不要依赖这个字段来做自动化处理。
     * <p>
     * **接口限频：** 单个商户 100 QPS，如果超过频率限制，会报错 FREQUENCY\_LIMITED，请降低频率请求。
     *
     * @return TransferBillEntity
     */
    public TransferBillEntity run(GetTransferBillByOutNoRequest request) {
        // 请求参数验证
//        validateRequestParameters(request);

        // 构造HTTP请求
        String uri = path;
        uri = uri.replace("{out_bill_no}", Utility.urlEncode(request.outBillNo));

        // 发送HTTP请求
        try (Response httpResponse = this.sendHttpRequest(uri)) {
            // 应答结果检查
            String respBody = validateResponse(httpResponse);

            // 从HTTP应答报文构建返回数据
            return parseResponse(respBody);
        } catch (IOException e) {
            throw new UncheckedIOException("Sending request to " + uri + " failed.", e);
        }
    }

    /**
     * 商家转账-商户单号查询转账单-验证请求参数
     * <p>
     * 参数列表：
     * - out_bill_no string(32): [必填] **【商户单号】**  商户系统内部的商家单号，要求此参数只能由数字、大小写字母组成，在商户系统内部唯一
     * <p>
     * 请根据上述字段列表提供的参数的必要性、最长长度限制，并根据描述中所述的关联，编写参数检查逻辑，每个字段以驼峰命名Public访问，无需Getter
     *
     * @param request 请求参数
     */
    private void validateRequestParameters(GetTransferBillByOutNoRequest request) {
        // 商家转账-商户单号查询转账单-验证请求参数 生成示例，需选中该注释，通过 @demo 指令触发
        throw new UnsupportedOperationException("Method not implemented yet");
    }

    private String buildAuthorization(String method, String uri) {
        return Utility.buildAuthorization(mchid, certificateSerialNo, privateKey, method, uri, null);
    }

    private Response sendHttpRequest(String uri) throws IOException {
        Request.Builder builder = new Request.Builder().url(host + uri);
        builder.addHeader("Accept", "application/json");
        builder.addHeader("Wechatpay-Serial", wechatPayPublicKeyId);
        builder.addHeader("Authorization", buildAuthorization(method, uri));
        builder.method(method, null);

        Request request = builder.build();
        OkHttpClient client = new OkHttpClient.Builder().build();
        return client.newCall(request).execute();
    }

    private String validateResponse(Response response) {
        String body = "";
        if (response.body() != null) {
            try {
                BufferedSource source = response.body().source();
                body = source.readUtf8();
            } catch (IOException e) {
                throw new RuntimeException(String.format("An error occurred during reading response body. Status: %d", response.code()), e);
            }
        }

        if (response.code() >= 200 && response.code() < 300) {
            // 2XX 成功，继续验证应答签名
            Headers headers = response.headers();
            Utility.validateResponse(this.wechatPayPublicKeyId, this.wechatPayPublicKey, headers, body);
            return body;
        }

        // TODO: 根据错误码执行不同的处理
        throw new UnsupportedOperationException(String.format("接口请求错误，StatusCode: %d, Body: %s",
                response.code(), body));
    }

    /**
     * 商家转账-商户单号查询转账单-解析回包结果
     * - mch_id string: [必填] **【商户号】** 微信支付分配的商户号
     * - out_bill_no string(32): [必填] **【商户单号】** 商户系统内部的商家单号，要求此参数只能由数字、大小写字母组成，在商户系统内部唯一
     * - transfer_bill_no string: [必填] **【商家转账订单号】** 商家转账订单的主键，唯一定义此资源的标识
     * - appid string: [必填] **【商户AppID】** 是微信开放平台和微信公众平台为开发者的应用程序(APP、小程序、公众号、企业号corpid即为此AppID)提供的一个唯一标识。此处，可以填写这四种类型中的任意一种APPID，但请确保该appid与商户号有绑定关系。详见：[普通商户模式开发必要参数说明](https://pay.weixin.qq.com/doc/v3/merchant/4013070756)。
     * - state string: [必填] **【单据状态】**
     * - transfer_amount integer: [必填] **【转账金额】** 转账金额单位为“分”。
     * - transfer_remark string(32): [必填] **【转账备注】** 单条转账备注（微信用户会收到该备注），UTF8编码，最多允许32个字符
     * - fail_reason string: [选填] **【失败原因】** 订单已失败或者已退资金时，会返回 [订单失败原因](https://pay.weixin.qq.com/doc/v3/merchant/4013774966)
     * - openid string(64): [选填] **【收款用户OpenID】** 用户在商户appid下的唯一标识。发起转账前需获取到用户的OpenID，获取方式详见 [参数说明](https://pay.weixin.qq.com/doc/v3/merchant/4012068676)。
     * - user_name string: [选填] **【收款用户姓名】** 收款方真实姓名。支持标准RSA算法和国密算法，公钥由微信侧提供转账金额 >= 2,000元时，该笔明细必须填写若商户传入收款用户姓名，微信支付会校验用户OpenID与姓名是否一致，并提供电子回单
     * - create_time string: [必填] **【单据创建时间】** 单据受理成功时返回，按照使用rfc3339所定义的格式，格式为yyyy-MM-DDThh:mm:ss+TIMEZONE
     * - update_time string: [必填] **【最后一次状态变更时间】** 单据最后更新时间，按照使用rfc3339所定义的格式，格式为yyyy-MM-DDThh:mm:ss+TIMEZONE
     *
     * @return TransferBillEntity
     */
    private TransferBillEntity parseResponse(String body) {
        // 商家转账-商户单号查询转账单-解析回包结果 生成示例，需选中该注释，通过 @demo 指令触发
        TransferBillEntity response = Utility.fromJson(body, TransferBillEntity.class);
        return response;
    }

    static class Utility {
        private static final Gson gson = new GsonBuilder()
                .disableHtmlEscaping()
                .addSerializationExclusionStrategy(new ExclusionStrategy() {
                    @Override
                    public boolean shouldSkipField(FieldAttributes fieldAttributes) {
                        final Expose expose = fieldAttributes.getAnnotation(Expose.class);
                        return expose != null && !expose.serialize();
                    }

                    @Override
                    public boolean shouldSkipClass(Class<?> aClass) {
                        return false;
                    }
                })
                .addDeserializationExclusionStrategy(new ExclusionStrategy() {
                    @Override
                    public boolean shouldSkipField(FieldAttributes fieldAttributes) {
                        final Expose expose = fieldAttributes.getAnnotation(Expose.class);
                        return expose != null && !expose.deserialize();
                    }

                    @Override
                    public boolean shouldSkipClass(Class<?> aClass) {
                        return false;
                    }
                })
                .create();
        private static final char[] SYMBOLS = "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ".toCharArray();
        private static final SecureRandom random = new SecureRandom();

        public static String toJson(Object object) {
            return gson.toJson(object);
        }

        public static <T> T fromJson(String json, Class<T> classOfT) throws JsonSyntaxException {
            return gson.fromJson(json, classOfT);
        }

        private static String readKeyStringFromPath(String keyPath) {
            try {
                return new String(Files.readAllBytes(Paths.get(keyPath)), StandardCharsets.UTF_8);
            } catch (IOException e) {
                throw new UncheckedIOException(e);
            }
        }

        public static PrivateKey loadPrivateKeyFromString(String keyString) {
            try {
                keyString = keyString.replace("-----BEGIN PRIVATE KEY-----", "")
                        .replace("-----END PRIVATE KEY-----", "")
                        .replaceAll("\\s+", "");
                return KeyFactory.getInstance("RSA").generatePrivate(
                        new PKCS8EncodedKeySpec(Base64.getDecoder().decode(keyString)));
            } catch (NoSuchAlgorithmException e) {
                throw new UnsupportedOperationException(e);
            } catch (InvalidKeySpecException e) {
                throw new IllegalArgumentException(e);
            }
        }

        public static PrivateKey loadPrivateKeyFromPath(String keyPath) {
            return loadPrivateKeyFromString(readKeyStringFromPath(keyPath));
        }

        public static PublicKey loadPublicKeyFromString(String keyString) {
            try {
                keyString = keyString.replace("-----BEGIN PUBLIC KEY-----", "")
                        .replace("-----END PUBLIC KEY-----", "")
                        .replaceAll("\\s+", "");
                return KeyFactory.getInstance("RSA").generatePublic(
                        new X509EncodedKeySpec(Base64.getDecoder().decode(keyString)));
            } catch (NoSuchAlgorithmException e) {
                throw new UnsupportedOperationException(e);
            } catch (InvalidKeySpecException e) {
                throw new IllegalArgumentException(e);
            }
        }

        public static PublicKey loadPublicKeyFromPath(String keyPath) {
            return loadPublicKeyFromString(readKeyStringFromPath(keyPath));
        }

        public static String createNonce(int length) {
            char[] buf = new char[length];
            for (int i = 0; i < length; ++i) {
                buf[i] = SYMBOLS[random.nextInt(SYMBOLS.length)];
            }
            return new String(buf);
        }

        public static String encrypt(PublicKey publicKey, String plaintext) {
            final String transformation = "RSA/ECB/OAEPWithSHA-1AndMGF1Padding";

            try {
                Cipher cipher = Cipher.getInstance(transformation);
                cipher.init(Cipher.ENCRYPT_MODE, publicKey);
                return Base64.getEncoder().encodeToString(cipher.doFinal(plaintext.getBytes(StandardCharsets.UTF_8)));
            } catch (NoSuchAlgorithmException | NoSuchPaddingException e) {
                throw new IllegalArgumentException("The current Java environment does not support " + transformation, e);
            } catch (InvalidKeyException e) {
                throw new IllegalArgumentException("RSA encryption using an illegal publicKey", e);
            } catch (BadPaddingException | IllegalBlockSizeException e) {
                throw new IllegalArgumentException("Plaintext is too long", e);
            }
        }

        public static String sign(String message, String algorithm, PrivateKey privateKey) {
            byte[] sign;
            try {
                Signature signature = Signature.getInstance(algorithm);
                signature.initSign(privateKey);
                signature.update(message.getBytes(StandardCharsets.UTF_8));
                sign = signature.sign();
            } catch (NoSuchAlgorithmException e) {
                throw new UnsupportedOperationException("The current Java environment does not support " + algorithm, e);
            } catch (InvalidKeyException e) {
                throw new IllegalArgumentException(algorithm + " signature uses an illegal privateKey.", e);
            } catch (SignatureException e) {
                throw new RuntimeException("An error occurred during the sign process.", e);
            }
            return Base64.getEncoder().encodeToString(sign);
        }

        public static boolean verify(String message, String signature, String algorithm, PublicKey publicKey) {
            try {
                Signature sign = Signature.getInstance(algorithm);
                sign.initVerify(publicKey);
                sign.update(message.getBytes(StandardCharsets.UTF_8));
                return sign.verify(Base64.getDecoder().decode(signature));
            } catch (SignatureException e) {
                return false;
            } catch (InvalidKeyException e) {
                throw new IllegalArgumentException("verify uses an illegal publickey.", e);
            } catch (NoSuchAlgorithmException e) {
                throw new UnsupportedOperationException("The current Java environment does not support" + algorithm, e);
            }
        }

        public static String buildAuthorization(String mchid, String certificateSerialNo, PrivateKey privateKey,
                                                String method, String uri, String body) {
            String nonce = createNonce(32);
            long timestamp = Instant.now().getEpochSecond();

            String message = String.format("%s\n%s\n%d\n%s\n%s\n", method, uri, timestamp, nonce, body == null ? "" : body);

            String signature = sign(message, "SHA256withRSA", privateKey);

            return String.format(
                    "WECHATPAY2-SHA256-RSA2048 mchid=\"%s\",nonce_str=\"%s\",signature=\"%s\",timestamp=\"%d\",serial_no=\"%s\"",
                    mchid, nonce, signature, timestamp, certificateSerialNo);
        }

        public static String urlEncode(String content) {
            try {
                return URLEncoder.encode(content, StandardCharsets.UTF_8.name());
            } catch (UnsupportedEncodingException e) {
                throw new RuntimeException(e);
            }
        }

        public static void validateResponse(String wechatpayPublicKeyId, PublicKey wechatpayPublicKey, Headers headers,
                                            String body) {
            String timestamp = headers.get("Wechatpay-Timestamp");
            try {
                Instant responseTime = Instant.ofEpochSecond(Long.parseLong(timestamp));
                // 拒绝过期请求
                if (Duration.between(responseTime, Instant.now()).abs().toMinutes() >= 5) {
                    throw new IllegalArgumentException(
                            String.format("Validate http response,timestamp[%s] of httpResponse is expires, "
                                            + "request-id[%s]",
                                    timestamp, headers.get("Request-ID")));
                }
            } catch (DateTimeException | NumberFormatException e) {
                throw new IllegalArgumentException(
                        String.format("Validate http response,timestamp[%s] of httpResponse is invalid, request-id[%s]", timestamp,
                                headers.get("Request-ID")));
            }
            String message = String.format("%s\n%s\n%s\n", timestamp, headers.get("Wechatpay-Nonce"), body == null ? "" : body);
            String serialNumber = headers.get("Wechatpay-Serial");
            if (!Objects.equals(serialNumber, wechatpayPublicKeyId)) {
                throw new IllegalArgumentException(
                        String.format("Invalid Wechatpay-Serial, Local: %s, Remote: %s", wechatpayPublicKeyId, serialNumber));
            }
            String signature = headers.get("Wechatpay-Signature");

            boolean success = verify(message, signature, "SHA256withRSA", wechatpayPublicKey);
            if (!success) {
                throw new IllegalArgumentException(
                        String.format("Validate response failed,the WechatPay signature is incorrect.%n"
                                        + "Request-ID[%s]\tresponseHeader[%s]\tresponseBody[%.1024s]",
                                headers.get("Request-ID"), headers, body));
            }
        }
    }

    /**
     * TransferBillEntity
     */
    @Data
    public static class TransferBillEntity {

        /**
         * 商户号
         * 说明：微信支付分配的商户号
         */
        @SerializedName("mch_id")
        public String mchId;

        /**
         * 商户单号
         * 说明：商户系统内部的商家单号，要求此参数只能由数字、大小写字母组成，在商户系统内部唯一
         */
        @SerializedName("out_bill_no")
        public String outBillNo;

        /**
         * 商家转账订单号
         * 说明：商家转账订单的主键，唯一定义此资源的标识
         */
        @SerializedName("transfer_bill_no")
        public String transferBillNo;

        /**
         * 商户AppID
         * 说明：是微信开放平台和微信公众平台为开发者的应用程序(APP、小程序、公众号、企业号corpid即为此AppID)提供的一个唯一标识。此处，可以填写这四种类型中的任意一种APPID，但请确保该appid与商户号有绑定关系。详见：[普通商户模式开发必要参数说明](https://pay.weixin.qq.com/doc/v3/merchant/4013070756)。
         */
        @SerializedName("appid")
        public String appid;

        /**
         * 单据状态
         * 说明：
         */
        @SerializedName("state")
        public TransferBillStatus state;

        /**
         * 转账金额
         * 说明：转账金额单位为“分”。
         */
        @SerializedName("transfer_amount")
        public Long transferAmount;

        /**
         * 转账备注
         * 说明：单条转账备注（微信用户会收到该备注），UTF8编码，最多允许32个字符
         */
        @SerializedName("transfer_remark")
        public String transferRemark;

        /**
         * 失败原因
         * 说明：订单已失败或者已退资金时，会返回 [订单失败原因](https://pay.weixin.qq.com/doc/v3/merchant/4013774966)
         */
        @SerializedName("fail_reason")
        public String failReason;

        /**
         * 收款用户OpenID
         * 说明：用户在商户appid下的唯一标识。发起转账前需获取到用户的OpenID，获取方式详见 [参数说明](https://pay.weixin.qq.com/doc/v3/merchant/4012068676)。
         */
        @SerializedName("openid")
        public String openid;

        /**
         * 收款用户姓名
         * 说明：收款方真实姓名。支持标准RSA算法和国密算法，公钥由微信侧提供转账金额 >= 2,000元时，该笔明细必须填写若商户传入收款用户姓名，微信支付会校验用户OpenID与姓名是否一致，并提供电子回单
         */
        @SerializedName("user_name")
        public String userName;

        /**
         * 单据创建时间
         * 说明：单据受理成功时返回，按照使用rfc3339所定义的格式，格式为yyyy-MM-DDThh:mm:ss+TIMEZONE
         */
        @SerializedName("create_time")
        public String createTime;

        /**
         * 最后一次状态变更时间
         * 说明：单据最后更新时间，按照使用rfc3339所定义的格式，格式为yyyy-MM-DDThh:mm:ss+TIMEZONE
         */
        @SerializedName("update_time")
        public String updateTime;
    }

    /**
     * TransferBillStatus
     */
    public enum TransferBillStatus {

        @SerializedName("ACCEPTED")
        ACCEPTED,

        @SerializedName("PROCESSING")
        PROCESSING,

        @SerializedName("WAIT_USER_CONFIRM")
        WAIT_USER_CONFIRM,

        @SerializedName("TRANSFERING")
        TRANSFERING,

        @SerializedName("SUCCESS")
        SUCCESS,

        @SerializedName("FAIL")
        FAIL,

        @SerializedName("CANCELING")
        CANCELING,

        @SerializedName("CANCELLED")
        CANCELLED
    }

    /**
     * GetTransferBillByOutNoRequest
     */
    public static class GetTransferBillByOutNoRequest {

        /**
         * 商户单号
         * 说明：商户系统内部的商家单号，要求此参数只能由数字、大小写字母组成，在商户系统内部唯一
         */
        @SerializedName("out_bill_no")
        @Expose(serialize = false)
        public String outBillNo;
    }
}