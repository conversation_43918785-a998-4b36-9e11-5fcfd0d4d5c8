package com.guanghonggu.strategy.pay.wechat.jsapi;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.guanghonggu.handler.exception.BizException;
import com.guanghonggu.strategy.pay.strategy.PayQueryHandler;
import com.guanghonggu.strategy.pay.strategy.PayStrategy;
import com.guanghonggu.strategy.pay.wechat.WechatPayConfig;
import com.guanghonggu.strategy.pay.wechat.WechatUtils;
import com.guanghonggu.util.HttpClientUtil;
import com.wechat.pay.java.core.Config;
import com.wechat.pay.java.service.payments.jsapi.JsapiService;
import com.wechat.pay.java.service.payments.jsapi.model.Amount;
import com.wechat.pay.java.service.payments.jsapi.model.Payer;
import com.wechat.pay.java.service.payments.jsapi.model.PrepayRequest;
import com.wechat.pay.java.service.payments.model.Transaction;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Collections;
import java.util.Map;

/**
 * @author：zzn
 * @description：
 * @createData：2025/7/1 13:45
 */
@Slf4j
@Component
public class WechatMiniPayStrategyService implements PayStrategy {

    @Autowired
    private WechatPayConfig wechatPayConfig;

    @Autowired
    private PayQueryHandler payQueryHandler;
    @Override
    public Integer getType() {
        return 6;
    }

    @Override
    public Map<String, Object> pay(Map<String, Object> map) {
        String orderNumber = map.get("orderNumber").toString();
        String actualPaymentPriceStr = map.get("actualPaymentPrice").toString();
        String description = map.get("description").toString();
        String transactionType = map.get("transactionType").toString();
        String code = map.get("code").toString();
        BigDecimal actualPaymentPrice = new BigDecimal(actualPaymentPriceStr);
        // 转换为“分”，并四舍五入，避免精度问题
        int amountInCents = actualPaymentPrice.multiply(BigDecimal.valueOf(100))
                .setScale(0, RoundingMode.HALF_UP)
                .intValue();
        Config config = WechatUtils.getInstance(wechatPayConfig);
        // 初始化service
        JsapiService service = new JsapiService.Builder().config(config).build();
        PrepayRequest request = new PrepayRequest();
        request.setAppid(wechatPayConfig.getMiniPrograms().getAppId());
        request.setMchid(wechatPayConfig.getMerchantId());
        request.setDescription(description);
        request.setOutTradeNo(orderNumber);
        if ("1".equals(transactionType)) {
            // 充值
            request.setNotifyUrl(wechatPayConfig.getRechargeNotifyUrl());
        } else if ("2".equals(transactionType)) {
            // 下单支付
            request.setNotifyUrl(wechatPayConfig.getNotifyUrl());
        }
        Amount amount = new Amount();
        amount.setTotal(amountInCents);
        amount.setCurrency("CNY");
        request.setAmount(amount);
        JSONObject json = payQueryHandler.getWechatMiniOpenIdByCode(code);
        String openid = json.getString("openid"); // 获取 openid
        Payer payer = new Payer();
        payer.setOpenid(openid);
        request.setPayer(payer);
        // 调用预支付接口
        String prepayId = service.prepay(request).getPrepayId();
        Map<String, Object> sign;
        try {
            sign = WechatUtils.getSign(prepayId, wechatPayConfig, 2);
        } catch (Exception e) {
            throw new RuntimeException("获取sign签名失败", e);
        }
        return sign;
    }


    @Override
    public Transaction handleCallback(HttpServletRequest request) {
        return null;
    }

    @Override
    public Map<String, Object> withdraw(Map<String, Object> map) {
        return Collections.emptyMap();
    }
}
