package com.guanghonggu.strategy.pay.wechat.transfer;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;
import com.guanghonggu.entity.Wallet;
import com.guanghonggu.entity.WalletTransaction;
import com.guanghonggu.mapper.WalletMapper;
import com.guanghonggu.mapper.WalletTransactionMapper;
import com.guanghonggu.strategy.pay.wechat.WechatPayConfig;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.io.UncheckedIOException;
import java.math.BigDecimal;
import java.security.PrivateKey;
import java.security.PublicKey;

/**
 * @author：zzn
 * @description：撤销转账
 * @createData：2025/7/8 11:31
 */
@Slf4j
@Component
public class CancelTransfer {

    @Autowired
    private WechatPayConfig wechatPayConfig;

    @Autowired
    private WalletMapper walletMapper;

    @Autowired
    private WalletTransactionMapper walletTransactionMapper;

    private static String HOST = "https://api.mch.weixin.qq.com";
    private static String METHOD = "POST";
    private static String PATH = "/v3/fund-app/mch-transfer/transfer-bills/out-bill-no/{out_bill_no}/cancel";

    private String mchid;
    private String certificateSerialNo;
    private PrivateKey privateKey;
    private String wechatPayPublicKeyId;
    private PublicKey wechatPayPublicKey;

    @PostConstruct
    public void init() {
        this.mchid = wechatPayConfig.getMerchantId();
        this.certificateSerialNo = wechatPayConfig.getMerchantSerialNumber();
        String privateKeyFilePath = wechatPayConfig.getPrivateKeyPath();
        this.privateKey = GetTransferBillByOutNo.Utility.loadPrivateKeyFromPath(privateKeyFilePath);
        this.wechatPayPublicKeyId = wechatPayConfig.getPublicKeyId();
        String publicKeyFilePath = wechatPayConfig.getPublicKeyPath();
        this.wechatPayPublicKey = GetTransferBillByOutNo.Utility.loadPublicKeyFromPath(publicKeyFilePath);
    }


    public CancelTransferResponse run(CancelTransferRequest request) {
        String uri = PATH;
        uri = uri.replace("{out_bill_no}", WXPayUtility.urlEncode(request.outBillNo));

        Request.Builder reqBuilder = new Request.Builder().url(HOST + uri);
        reqBuilder.addHeader("Accept", "application/json");
        reqBuilder.addHeader("Wechatpay-Serial", wechatPayPublicKeyId);
        reqBuilder.addHeader("Authorization", WXPayUtility.buildAuthorization(mchid, certificateSerialNo, privateKey, METHOD, uri, null));
        reqBuilder.addHeader("Content-Type", "application/json");
        RequestBody emptyBody = RequestBody.create(null, "");
        reqBuilder.method(METHOD, emptyBody);
        Request httpRequest = reqBuilder.build();

        // 发送HTTP请求
        OkHttpClient client = new OkHttpClient.Builder().build();
        try (Response httpResponse = client.newCall(httpRequest).execute()) {
            String respBody = WXPayUtility.extractBody(httpResponse);
            if (httpResponse.code() >= 200 && httpResponse.code() < 300) {
                // 2XX 成功，验证应答签名
                WXPayUtility.validateResponse(this.wechatPayPublicKeyId, this.wechatPayPublicKey,
                        httpResponse.headers(), respBody);

                // 从HTTP应答报文构建返回数据
                return WXPayUtility.fromJson(respBody, CancelTransferResponse.class);
            } else {
                throw new WXPayUtility.ApiException(httpResponse.code(), respBody, httpResponse.headers());
            }
        } catch (IOException e) {
            throw new UncheckedIOException("Sending request to " + uri + " failed.", e);
        }
    }


    public static class CancelTransferResponse {
        @SerializedName("out_bill_no")
        public String outBillNo;

        @SerializedName("transfer_bill_no")
        public String transferBillNo;

        @SerializedName("state")
        public String state;

        @SerializedName("update_time")
        public String updateTime;
    }

    @Data
    public static class CancelTransferRequest {
        @SerializedName("out_bill_no")
        @Expose(serialize = false)
        public String outBillNo;
    }

    /**
     * 取消转账
     *
     * @param tx 商户订单号
     */
    @Transactional(rollbackFor = Exception.class)
    public void cancelTransfer(WalletTransaction tx) {
        String outTradeNo = tx.getOutTradeNo();
        Long walletId = tx.getWalletId();
        BigDecimal amount = tx.getAmount();


        CancelTransfer.CancelTransferRequest cancelTransferRequest = new CancelTransfer.CancelTransferRequest();
        cancelTransferRequest.setOutBillNo(outTradeNo);
        CancelTransferResponse res = run(cancelTransferRequest);
        String state = res.state;
        if ("CANCELLED".equals(state)) {
            log.info("【微信转账任务】订单 {} 已取消", outTradeNo);
            Wallet wallet = walletMapper.selectWalletByIdForUpdate(walletId);
            wallet.setBalance(wallet.getBalance().add(amount));
            walletMapper.updateById(wallet);
            // 更新本地状态为失败（2=失败）
            WalletTransaction walletTransaction = new WalletTransaction();
            walletTransaction.setTransactionId(tx.getTransactionId());
            walletTransaction.setStatus(2);
            walletTransactionMapper.updateById(walletTransaction);
        }
    }

}
