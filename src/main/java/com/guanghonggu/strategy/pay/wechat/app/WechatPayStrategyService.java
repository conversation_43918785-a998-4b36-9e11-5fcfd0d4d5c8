package com.guanghonggu.strategy.pay.wechat.app;

import com.guanghonggu.strategy.pay.strategy.PayStrategy;
import com.guanghonggu.strategy.pay.wechat.WechatPayConfig;
import com.guanghonggu.strategy.pay.wechat.WechatUtils;
import com.guanghonggu.strategy.pay.wechat.transfer.TransferToUser;
import com.wechat.pay.java.core.Config;
import com.wechat.pay.java.core.RSAAutoCertificateConfig;
import com.wechat.pay.java.core.notification.NotificationConfig;
import com.wechat.pay.java.core.notification.NotificationParser;
import com.wechat.pay.java.core.notification.RequestParam;
import com.wechat.pay.java.service.payments.app.AppService;
import com.wechat.pay.java.service.payments.app.model.Amount;
import com.wechat.pay.java.service.payments.app.model.PrepayRequest;
import com.wechat.pay.java.service.payments.model.Transaction;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.io.IOException;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @author：zzn
 * @description：微信支付策略
 * @createData：2025/5/13 9:28
 */
@Slf4j
@Component
public class WechatPayStrategyService implements PayStrategy {

    @Autowired
    private WechatPayConfig wechatPayConfig;

    @Autowired
    private TransferToUser transferToUser;

    @Override
    public Integer getType() {
        return 1;
    }

    @Override
    public Map<String, Object> pay(Map<String, Object> map) {
        log.info("微信支付");

        // 更新证书配置，这里用官方sdk，不需要考虑时效性问题
//        Config config = new RSAPublicKeyConfig.Builder()
//                .merchantId(WechatPayConfig.getMerchantId())
//                .privateKeyFromPath(WechatPayConfig.getPrivateKeyPath())
//                .publicKeyFromPath(WechatPayConfig.getPublicKeyPath())
//                .publicKeyId(WechatPayConfig.getPublicKeyPath())
//                .merchantSerialNumber(WechatPayConfig.getMerchantSerialNumber())
//                .apiV3Key(WechatPayConfig.getApiV3Key())
//                .build();
        String orderNumber = map.get("orderNumber").toString();
        String actualPaymentPriceStr = map.get("actualPaymentPrice").toString();
        String description = map.get("description").toString();
        String transactionType = map.get("transactionType").toString();
        BigDecimal actualPaymentPrice = new BigDecimal(actualPaymentPriceStr);
        // 转换为“分”，并四舍五入，避免精度问题
        int amountInCents = actualPaymentPrice.multiply(BigDecimal.valueOf(100))
                .setScale(0, RoundingMode.HALF_UP)
                .intValue();
        Config config = WechatUtils.getInstance(wechatPayConfig);
        // 初始化service
        AppService service = new AppService.Builder().config(config).build();
        PrepayRequest request = new PrepayRequest();
        request.setAppid(wechatPayConfig.getApp().getAppId());
        request.setMchid(wechatPayConfig.getMerchantId());
        request.setDescription(description);
        request.setOutTradeNo(orderNumber);
        if ("1".equals(transactionType)) {
            // 充值
            request.setNotifyUrl(wechatPayConfig.getRechargeNotifyUrl());
        } else if ("2".equals(transactionType)) {
            // 下单支付
            request.setNotifyUrl(wechatPayConfig.getNotifyUrl());
        } else if ("3".equals(transactionType)) {
            // 保洁物品商城下单支付回调
            request.setNotifyUrl(wechatPayConfig.getShopOrderNotifyUrl());
        }
        Amount amount = new Amount();
        amount.setTotal(amountInCents);
        request.setAmount(amount);
        // 调用预支付接口
        String prepayId = service.prepay(request).getPrepayId();
        Map<String, Object> sign;
        try {
            sign = WechatUtils.getSign(prepayId, wechatPayConfig, 1);
        } catch (Exception e) {
            throw new RuntimeException("获取sign签名失败", e);
        }
        return sign;
    }

    @Override
    public Transaction handleCallback(HttpServletRequest request) {
        String signature = request.getHeader("Wechatpay-Signature");
        String serial = request.getHeader("Wechatpay-Serial");
        String nonce = request.getHeader("Wechatpay-Nonce");
        String timestamp = request.getHeader("Wechatpay-Timestamp");

        RequestParam requestParam = new RequestParam.Builder()
                .serialNumber(serial)
                .nonce(nonce)
                .signature(signature)
                .timestamp(timestamp)
                .body(WechatUtils.getRequestBody(request))
                .build();
//        NotificationConfig config = new RSAPublicKeyConfig.Builder()
//                .merchantId(wechatPayConfig.getMerchantId())
//                .privateKeyFromPath(wechatPayConfig.getPrivateKeyPath())
//                .publicKeyFromPath(wechatPayConfig.getPublicKeyPath())
//                .publicKeyId(wechatPayConfig.getPublicKeyId())
//                .merchantSerialNumber(wechatPayConfig.getMerchantSerialNumber())
//                .apiV3Key(wechatPayConfig.getApiV3Key())
//                .build();
        Config config = WechatUtils.getInstance(wechatPayConfig);
//        NotificationConfig config = new RSAPublicKeyNotificationConfig.Builder()
//                .publicKeyFromPath(wechatPayConfig.getPublicKeyPath())
//                .publicKeyId(wechatPayConfig.getPublicKeyId())
//                .apiV3Key(wechatPayConfig.getApiV3Key())
//                .build();
        NotificationConfig notificationConfig = (NotificationConfig) config;
        NotificationParser parser = new NotificationParser(notificationConfig);
        Transaction transaction = parser.parse(requestParam, Transaction.class);

        // 封装返回值
        HashMap<String, Object> resultMap = WechatUtils.getStringObjectHashMap(transaction);

        return transaction;
    }

    @Override
    public Map<String, Object> withdraw(Map<String, Object> map) {
        String outBillNo = map.get("outBillNo").toString();
        String openid = map.get("openid").toString();
        String withdrawType = map.get("withdrawType").toString();
        BigDecimal transferAmount = new BigDecimal(map.get("transferAmount").toString());
        long amountInFen = transferAmount.multiply(BigDecimal.valueOf(100))
                .setScale(0, RoundingMode.HALF_UP)
                .longValue();

        String transferRemark = map.get("transferRemark").toString();
        TransferToUser.TransferToUserRequest request = new TransferToUser.TransferToUserRequest();

        if ("1".equals(withdrawType)) {
            request.appid = wechatPayConfig.getApp().getAppId();
        }else {
            request.appid = wechatPayConfig.getMiniPrograms().getAppId();
        }
        request.outBillNo = outBillNo;
        request.openid = openid;
        request.transferAmount = amountInFen;
        request.transferRemark = transferRemark;
        request.notifyUrl = wechatPayConfig.getTransferNotifyUrl();
        request.transferSceneId = wechatPayConfig.getTransferSceneId();
        TransferToUser.TransferSceneReportInfo transferSceneReportInfo = new TransferToUser.TransferSceneReportInfo();
        transferSceneReportInfo.infoType = "岗位类型";
        transferSceneReportInfo.infoContent = "客户";

        TransferToUser.TransferSceneReportInfo transferSceneReportInfo2 = new TransferToUser.TransferSceneReportInfo();
        transferSceneReportInfo2.infoType = "报酬说明";
        transferSceneReportInfo2.infoContent = "钱包提现";
        request.transferSceneReportInfos = new ArrayList<>();

        request.transferSceneReportInfos.add(transferSceneReportInfo);
        request.transferSceneReportInfos.add(transferSceneReportInfo2);


        TransferToUser.TransferToUserResponse response = transferToUser.transfer(request);
        log.info("商户单号：{}", response.outBillNo);
        log.info("微信转账单号：{}", response.transferBillNo);
        log.info("状态：{}", response.state);
        log.info("创建时间：{}", response.createTime);
        log.info("失败原因：{}", response.failReason);
        log.info("转账成功后，返回的转账单据信息：{}", response.packageInfo);
        String transferBillNo = response.transferBillNo;
        String stateName = response.state.name();
        String createTime = response.createTime;
        String packageInfo = response.packageInfo;
        HashMap<String, Object> resMap = new HashMap<>();
        resMap.put("transferBillNo", transferBillNo);
        resMap.put("stateName", stateName);
        resMap.put("createTime", createTime);
        resMap.put("packageInfo", packageInfo);
        resMap.put("mchId", wechatPayConfig.getMerchantId());
        resMap.put("appId", request.appid);
        return resMap;
    }

}