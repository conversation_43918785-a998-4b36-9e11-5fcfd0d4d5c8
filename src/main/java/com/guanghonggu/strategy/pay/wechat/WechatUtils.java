package com.guanghonggu.strategy.pay.wechat;

import com.guanghonggu.util.WXPayUtil;
import com.wechat.pay.java.core.Config;
import com.wechat.pay.java.core.RSAAutoCertificateConfig;
import com.wechat.pay.java.service.payments.model.Transaction;
import lombok.extern.slf4j.Slf4j;

import javax.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.security.InvalidKeyException;
import java.security.KeyFactory;
import java.security.NoSuchAlgorithmException;
import java.security.PrivateKey;
import java.security.SignatureException;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.PKCS8EncodedKeySpec;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * @author：zzn
 * @description：
 * @createData：2025/5/16 11:00
 */
@Slf4j
public class WechatUtils {

    // 商户的全局配置类
    private static Config instance;

    /**
     * 定义商户的全局配置信息，要求一个商户号对应一个配置
     * 不能重复生成配置
     * RSAAutoCertificateConfig 会利用 AutoCertificateService 自动下载微信支付平台证书。
     * AutoCertificateService 将启动一个后台线程，定期（目前为每60分钟）更新证书，
     * 以实现证书过期时的平滑切换。
     * 在每次构建 RSAAutoCertificateConfig 时，
     * SDK 首先会使用传入的商户参数下载一次微信支付平台证书。 如果下载成功，SDK 会将商户参数注册或更
     * 新至 AutoCertificateService。若下载失败，将会抛出异常。
     * 为了提高性能，建议将配置类作为全局变量。 复用 RSAAutoCertificateConfig
     * 可以减少不必要的证书下载，避免资源浪费。 只有在配置发生变更时，
     * 才需要重新构造 RSAAutoCertificateConfig。
     */
    public static Config getInstance(WechatPayConfig wxPayConfig) {
        if (instance == null) {
            //如果实例不存在，创建一个新的实例
            synchronized (WechatPayConfig.class) {
                //双重检查锁定，防止多线程竞争时创建多个实例
                if (instance == null) {
                    try {
                        if (wxPayConfig == null) {
                            log.info("配置信息加载出错");
                            return null;
                        }
                        log.info("商户号为==={}", wxPayConfig.getMerchantId());
                        log.info("商户私钥路径为==={}", wxPayConfig.getPrivateKeyPath());
                        log.info("序列号为==={}", wxPayConfig.getMerchantSerialNumber());
                        log.info("密钥为==={}", wxPayConfig.getApiV3Key());
                        instance = new RSAAutoCertificateConfig.Builder()
                                .merchantId(wxPayConfig.getMerchantId())
//                                .privateKey(wxPayConfig.getPrivateKey())
                                .privateKeyFromPath(wxPayConfig.getPrivateKeyPath())
                                .merchantSerialNumber(wxPayConfig.getMerchantSerialNumber())
                                .apiV3Key(wxPayConfig.getApiV3Key())
                                .build();
                    } catch (Exception e) {
                        log.error("构建商户配置信息出错，错误信息为{}", e.getMessage(), e);
                        return null;
                    }
                }
            }
        }
        return instance;
    }

    /**
     * 获取sign支付签名
     *
     * @param prepayId 预支付id
     * @param type 支付类型(1：app支付，2：小程序支付)
     * @return
     */
    public static Map<String, Object> getSign(String prepayId, WechatPayConfig wechatPayConfig, Integer type) {
        String appId = wechatPayConfig.getApp().getAppId();
        if (type == 2) {
            prepayId = "prepay_id=" + prepayId;
            appId = wechatPayConfig.getMiniPrograms().getAppId();
        }
        //生成32位的随机字符串
        String nonceStr = UUID.randomUUID().toString().replace("-", "");
        long timestamp = System.currentTimeMillis() / 1000;
        String message = buildMessage(appId, timestamp, nonceStr, prepayId);

//        Signature sign = Signature.getInstance(SHA256WITHRSA);
//        sign.initSign(getPrivateKey(wechatPayConfig.getPrivateKeyPath()));
//        sign.update(message.getBytes(StandardCharsets.UTF_8));
//        String signature = Base64.getEncoder().encodeToString(sign.sign());
        String paySign;
        try {
            paySign = WXPayUtil.signWithPrivateKey(message, wechatPayConfig.getPrivateKeyPath());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        HashMap<String, Object> map = new HashMap<>();
        map.put("sign", paySign);
        map.put("nonceStr", nonceStr);
        map.put("timestamp", timestamp);
        map.put("prepayId", prepayId);
        map.put("package", prepayId);
        return map;
    }

    /**
     * 拼接需要的信息
     *
     * @param appId
     * @param timestamp
     * @param nonceStr
     * @param prepay_id
     * @return
     */
    public static String buildMessage(String appId, long timestamp, String nonceStr, String prepay_id) {
        return appId + "\n"
                + timestamp + "\n"
                + nonceStr + "\n"
                + prepay_id + "\n";
//                + "prepay_id=" + prepay_id + "\n";
    }

    /**
     * 获取私钥文件
     *
     * @param privateKeyPath  私钥路径
     * @return
     * @throws IOException
     */
    public static PrivateKey getPrivateKey(String privateKeyPath) throws IOException {

        String content = new String(Files.readAllBytes(Paths.get(privateKeyPath)), StandardCharsets.UTF_8);
        try {
            String privateKey = content.replace("-----BEGIN PRIVATE KEY-----", "")
                    .replace("-----END PRIVATE KEY-----", "")
                    .replaceAll("\\s+", "");

            KeyFactory kf = KeyFactory.getInstance("RSA");
            return kf.generatePrivate(
                    new PKCS8EncodedKeySpec(Base64.getDecoder().decode(privateKey)));
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("当前Java环境不支持RSA", e);
        } catch (InvalidKeySpecException e) {
            throw new RuntimeException("无效的密钥格式");
        }
    }

    /**
     * 获取request body
     *
     * @param request
     * @return
     */
    public static String getRequestBody(HttpServletRequest request) {
        try (BufferedReader reader = request.getReader()) {
            return reader.lines().collect(Collectors.joining(System.lineSeparator()));
        } catch (IOException e) {
            throw new RuntimeException("获取request body失败", e);
        }
    }

    /**
     * 封装回调返回值
     *
     * @param transaction 微信回调参数
     * @return
     */
    public static HashMap<String, Object> getStringObjectHashMap(Transaction transaction) {
        Transaction.TradeStateEnum tradeState = transaction.getTradeState();
        String stateName = tradeState.name();
        // 交易类型：APP、JSAPI
        Transaction.TradeTypeEnum tradeType = transaction.getTradeType();
        String typeName = tradeType.name();
        // 支付时间
        String successTime = transaction.getSuccessTime();
        // 商户订单号
        String outTradeNo = transaction.getOutTradeNo();
        // 微信支付订单号
        String transactionId = transaction.getTransactionId();
        HashMap<String, Object> resultMap = new HashMap<>();
        resultMap.put("tradeState", stateName);
        resultMap.put("tradeType", typeName);
        resultMap.put("successTime", successTime);
        resultMap.put("outTradeNo", outTradeNo);
        resultMap.put("transactionId", transactionId);
        return resultMap;
    }
}