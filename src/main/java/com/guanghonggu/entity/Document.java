package com.guanghonggu.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@TableName("document")
@Data
public class Document {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("keyword")
    private String keyWord;

    @TableField("bot_msg")
    private String botMsg;

    @TableField("created_at")
    private Date createTime;

    @TableField("updated_at")
    private Date updateTime;




}
