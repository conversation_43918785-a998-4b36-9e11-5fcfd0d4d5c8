package com.guanghonggu.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 
 * @TableName service_attribute
 */
@TableName(value ="service_attribute")
@Data
public class ServiceAttribute {
    /**
     * 
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 
     */
    @TableField(value = "service_type_code")
    private String serviceTypeCode;

    /**
     * 
     */
    @TableField(value = "attribute_key")
    private String attributeKey;

    /**
     * 
     */
    @TableField(value = "label")
    private String label;

    /**
     * 输入类型（number，select，radio，checkbox）
     */
    @TableField(value = "input_type")
    private String inputType;

    /**
     * 是否必须（0：否，1：是）
     */
    @TableField(value = "required")
    private Integer required;

    /**
     * 排序
     */
    @TableField(value = "sort")
    private Integer sort;
}