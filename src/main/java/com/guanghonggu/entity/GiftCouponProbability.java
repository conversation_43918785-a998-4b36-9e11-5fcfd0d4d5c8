package com.guanghonggu.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import lombok.Data;

/**
 * 
 * @TableName gift_coupon_probability
 */
@TableName(value ="gift_coupon_probability")
@Data
public class GiftCouponProbability {
    /**
     * 
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 发放概率
     */
    @TableField(value = "release_probability")
    private BigDecimal releaseProbability;

    /**
     * 优惠券id
     */
    @TableField(value = "coupon_id")
    private Long couponId;
}