package com.guanghonggu.entity;



import java.util.Date;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 用户优惠卷表
 * @TableName user_coupon
 */
@TableName(value ="user_coupon")
@Data
public class UserCoupon {
    /**
     * 
     */
    @TableId(value = "user_coupon_id", type = IdType.AUTO)
    private Long userCouponId;

    /**
     * 用户id
     */
    @TableField(value = "user_id")
    private Long userId;

    /**
     * 优惠卷id
     */
    @TableField(value = "coupon_id")
    private Long couponId;

    /**
     * 使用的订单编号
     */
    @TableField(value = "order_id")
    private Long orderId;

    /**
     * 获取时间
     */
    @TableField(value = "acquire_time")
    private Date acquireTime;

    /**
     * 使用时间
     */
    @TableField(value = "used_time")
    private Date usedTime;

    /**
     * 到期时间
     */
    @TableField(value = "expiration_time")
    private Date expirationTime;

    /**
     * 状态（1：未使用，2：已使用，3：已过期）
     */
    @TableField(value = "status")
    private Integer status;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

}