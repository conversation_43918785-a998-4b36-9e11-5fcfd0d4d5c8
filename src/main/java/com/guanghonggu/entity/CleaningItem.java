package com.guanghonggu.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 清洁工具与清洁剂表
 * @TableName cleaning_item
 */
@TableName(value ="cleaning_item")
@Data
public class CleaningItem {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 工具或清洁剂名称
     */
    @TableField(value = "name")
    private String name;

    /**
     * 物品类型（工具/清洁剂）
     */
    @TableField(value = "type")
    private String type;

    /**
     * 所属分类ID（关联分类表）
     */
    @TableField(value = "category_id")
    private Long categoryId;

    /**
     * 材质/类型描述（如“超细纤维”）
     */
    @TableField(value = "material")
    private String material;

    /**
     * 适用范围（如“布艺/皮质家具”）
     */
    @TableField(value = "applicable_scope")
    private String applicableScope;

    /**
     * 备注/说明
     */
    @TableField(value = "remark")
    private String remark;

    /**
     * 价格
     */
    @TableField(value = "price")
    private BigDecimal price;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private Date updateTime;
}