package com.guanghonggu.entity;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 评价表
 * @TableName evaluations
 */
@TableName(value ="evaluations")
@Data
public class Evaluations {
    /**
     * 评价id
     */
    @TableId(value = "evaluations_id", type = IdType.AUTO)
    private Long evaluationsId;

    /**
     * 评价人id（用户id）
     */
    @TableField(value = "evaluators_id")
    private Long evaluatorsId;

    /**
     * 被评价人id（阿姨id）
     */
    @TableField(value = "evaluated_id")
    private Long evaluatedId;

    /**
     * 整体评价
     */
    @TableField(value = "overall_evaluation")
    private Integer overallEvaluation;

    /**
     * 整体评价
     */
    @TableField(value = "service_attitude")
    private Integer serviceAttitude;

    /**
     * 整体评价
     */
    @TableField(value = "cleanliness_level")
    private Integer cleanlinessLevel;

    /**
     * 评价内容
     */
    @TableField(value = "evaluation_content")
    private String evaluationContent;

    /**
     * 评价状态 (0: 待审核, 1: 已审核, 2: 被删除)
     */
    @TableField(value = "status")
    private Integer status;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     *对应 service_type 表中的 name 字段（非数据库字段）
     */
    @TableField(exist = false)
    private String serviceTypeName;

}