package com.guanghonggu.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 
 * @TableName order_attribute
 */
@TableName(value ="order_attribute")
@Data
public class OrderAttribute {
    /**
     * 
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 
     */
    @TableField(value = "order_id")
    private Long orderId;

    /**
     * 属性标签
     */
    @TableField(value = "label")
    private String label;

    /**
     * 
     */
    @TableField(value = "attribute_key")
    private String attributeKey;

    /**
     * 
     */
    @TableField(value = "attribute_value")
    private String attributeValue;

    /**
     * 属性选项id
     */
    @TableField(value = "attribute_option_id")
    private Long attributeOptionId;
}