package com.guanghonggu.entity;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 阿姨地址表
 * @TableName caregiver_address
 */
@TableName(value ="caregiver_address")
@Data
public class CaregiverAddress {
    /**
     * 阿姨地址id
     */
    @TableId(value = "caregiver_address_id", type = IdType.AUTO)
    private Long caregiverAddressId;

    /**
     * 省区域id
     */
    @TableField(value = "province_region_id")
    private Long provinceRegionId;

    /**
     * 市区域id
     */
    @TableField(value = "city_region_id")
    private Long cityRegionId;

    /**
     * 区级区域id
     */
    @TableField(value = "area_region_id")
    private Long areaRegionId;

    /**
     * 详细地址
     */
    @TableField(value = "detailed_address")
    private String detailedAddress;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;


}