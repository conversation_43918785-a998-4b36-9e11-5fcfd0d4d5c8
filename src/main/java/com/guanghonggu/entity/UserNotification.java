package com.guanghonggu.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.util.Date;

@Data
@TableName("user_notification")
public class UserNotification {

    @TableId(type = IdType.AUTO)
    private Long id; // 通知主键ID

    private Long orderId; // 订单ID

    private Long caregiverId; // 阿姨ID

    private String message; // 通知内容（拒绝理由）

    @TableField(fill = FieldFill.INSERT)
    private Date createTime; // 创建时间
}