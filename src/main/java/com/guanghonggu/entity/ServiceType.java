package com.guanghonggu.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Data;

/**
 * 服务类型表
 * @TableName service_type
 */
@TableName(value ="service_type")
@Data
public class ServiceType {
    /**
     * 服务类型id
     */
    @TableId(value = "service_type_id", type = IdType.AUTO)
    private Long serviceTypeId;

    /**
     * 代号
     */
    @TableField(value = "code")
    private String code;

    /**
     * 服务类型名称
     */
    @TableField(value = "name")
    private String name;

    /**
     * 排序
     */
    @TableField(value = "sort")
    private Integer sort;


}