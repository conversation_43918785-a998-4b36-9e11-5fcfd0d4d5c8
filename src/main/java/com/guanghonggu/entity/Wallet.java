package com.guanghonggu.entity;


import java.math.BigDecimal;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 钱包表
 * @TableName wallet
 */
@TableName(value ="wallet")
@Data
public class Wallet {
    /**
     * 钱包id
     */
    @TableId(value = "wallet_id", type = IdType.AUTO)
    private Long walletId;

    /**
     * 用户/阿姨id
     */
    @TableField(value = "user_id")
    private Long userId;

    /**
     * 用户类型 (1: 用户, 2: 阿姨)

     */
    @TableField(value = "user_type")
    private Integer userType;

    /**
     * 余额
     */
    @TableField(value = "balance")
    private BigDecimal balance;

    /**
     * 储值余额
     */
    @TableField(value = "recharge_balance")
    private BigDecimal rechargeBalance;

    /**
     * 总收入
     */
    @TableField(value = "total_income")
    private BigDecimal totalIncome;

    /**
     * 总支出
     */
    @TableField(value = "total_spent")
    private BigDecimal totalSpent;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

}