package com.guanghonggu.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Data;

/**
 * 
 * @TableName caregiver_service_type
 */
@TableName(value ="caregiver_service_type")
@Data
public class CaregiverServiceType {
    /**
     * 
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 阿姨id
     */
    @TableField(value = "caregiver_id")
    private Long caregiverId;

    /**
     * 服务类型id
     */
    @TableField(value = "service_type_id")
    private Long serviceTypeId;

    /**
     * 是否支付押金（0：否，1：是）
     */
    @TableField(value = "pay_deposit")
    private Integer payDeposit;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

}