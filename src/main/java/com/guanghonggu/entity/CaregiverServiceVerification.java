package com.guanghonggu.entity;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 阿姨服务验证
 * @TableName caregiver_service_verification
 */
@TableName(value ="caregiver_service_verification")
@Data
public class CaregiverServiceVerification {
    /**
     * 服务验证id
     */
    @TableId(value = "service_verification_id", type = IdType.AUTO)
    private Long serviceVerificationId;

    /**
     * 阿姨id
     */
    @TableField(value = "caregiver_id")
    private Long caregiverId;

    /**
     * 订单id
     */
    @TableField(value = "order_id")
    private Long orderId;

    /**
     * 到达时间
     */
    @TableField(value = "arrival_time")
    private Date arrivalTime;

    /**
     * 拍照验证时间
     */
    @TableField(value = "verification_time")
    private Date verificationTime;

    /**
     * 状态（0：待验证，1：验证通过，2：验证未通过）
     */
    @TableField(value = "status")
    private Integer status;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

}