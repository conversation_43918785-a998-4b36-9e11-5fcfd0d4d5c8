package com.guanghonggu.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("resource")
public class Resource {

    @TableId(type = IdType.AUTO)
    private Long resourceId; // 主键ID

    @TableField("evaluations_id")
    private Long evaluationsId;   // 评价ID（外键）

    @TableField("user_id")
    private Long userId;          // 上传用户ID

    @TableField("object_name")
    private String objectName;    // OSS路径

    @TableField("create_time")
    private LocalDateTime createTime;

    @TableField("update_time")
    private LocalDateTime updateTime;
}