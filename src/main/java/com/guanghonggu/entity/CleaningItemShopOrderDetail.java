package com.guanghonggu.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 订单商品明细表
 * @TableName cleaning_item_shop_order_detail
 */
@TableName(value ="cleaning_item_shop_order_detail")
@Data
public class CleaningItemShopOrderDetail {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 所属订单ID
     */
    @TableField(value = "shop_order_id")
    private Long shopOrderId;

    /**
     * 清洁物品ID
     */
    @TableField(value = "item_id")
    private Long itemId;

    /**
     * 购买时商品名称快照
     */
    @TableField(value = "item_name")
    private String itemName;

    /**
     * 单价快照
     */
    @TableField(value = "unit_price")
    private BigDecimal unitPrice;

    /**
     * 数量
     */
    @TableField(value = "quantity")
    private Integer quantity;

    /**
     * 小计金额
     */
    @TableField(value = "total_price")
    private BigDecimal totalPrice;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
}