package com.guanghonggu.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 用户每月钱包收支汇总表
 * @TableName wallet_monthly_summary
 */
@TableName(value ="wallet_monthly_summary")
@Data
public class WalletMonthlySummary {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    @TableField(value = "user_id")
    private Long userId;

    /**
     * 用户类型（如0-用户、1-服务人员等）
     */
    @TableField(value = "user_type")
    private Integer userType;

    /**
     * 年份
     */
    @TableField(value = "year")
    private Integer year;

    /**
     * 月份（1-12）
     */
    @TableField(value = "month")
    private Integer month;

    /**
     * 当月总收入金额
     */
    @TableField(value = "total_income")
    private BigDecimal totalIncome;

    /**
     * 当月总提现金额
     */
    @TableField(value = "total_withdraw")
    private BigDecimal totalWithdraw;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private Date updateTime;
}