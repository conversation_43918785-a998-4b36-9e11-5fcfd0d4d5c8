package com.guanghonggu.entity;

import java.sql.Time;
import java.time.LocalTime;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 阿姨日程表
 * @TableName caregiver_schedule
 */
@TableName(value ="caregiver_schedule")
@Data
public class CaregiverSchedule {
    /**
     * 日程id
     */
    @TableId(value = "schedule_id", type = IdType.AUTO)
    private Long scheduleId;

    /**
     * 阿姨id
     */
    @TableField(value = "caregiver_id")
    private Long caregiverId;

    /**
     * 日程日期
     */
    @TableField(value = "schedule_date")
    private Date scheduleDate;

    /**
     * 日程类型(1:已接单时间，2：拒绝接单时间)
     */
    @TableField(value = "schedule_type")
    private Integer scheduleType;

    /**
     * 日程开始时间
     */
    @TableField(value = "schedule_start_time")
    private LocalTime scheduleStartTime;

    /**
     * 日程结束时间
     */
    @TableField(value = "schedule_end_time")
    private LocalTime  scheduleEndTime;

    /**
     * 订单id
     */
    @TableField(value = "order_id")
    private Long orderId;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

}