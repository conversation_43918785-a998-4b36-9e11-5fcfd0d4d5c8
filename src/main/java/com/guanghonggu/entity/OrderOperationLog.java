package com.guanghonggu.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Data;

/**
 * 订单操作日志表
 * @TableName order_operation_log
 */
@TableName(value ="order_operation_log")
@Data
public class OrderOperationLog {
    /**
     * 主键ID
     */
    @TableId(value = "log_id", type = IdType.AUTO)
    private Long logId;

    /**
     * 关联订单ID
     */
    @TableField(value = "order_id")
    private Long orderId;

    /**
     * 操作者ID（可为用户、阿姨、管理员）
     */
    @TableField(value = "operator_id")
    private Long operatorId;

    /**
     * 操作者类型（1：用户，2：阿姨，3：平台管理员）
     */
    @TableField(value = "operator_type")
    private Integer operatorType;

    /**
     * 操作类型（如：1创建订单，2接单，3开始服务，4服务完成，5申请退款，6退款成功，7退款审核中，8取消订单）
     */
    @TableField(value = "operation_type")
    private Integer operationType;

    /**
     * 操作描述（如：用户申请退款，平台审核通过）
     */
    @TableField(value = "operation_desc")
    private String operationDesc;

    /**
     * 扩展数据（可存JSON，记录状态前后对比或其他信息）
     */
    @TableField(value = "extra_data")
    private Object extraData;

    /**
     * 操作时间
     */
    @TableField(value = "create_time")
    private Date createTime;
}