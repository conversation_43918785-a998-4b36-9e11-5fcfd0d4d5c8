package com.guanghonggu.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@TableName(value = "sys_picture")
@Data
public class SysPicture {

    /**
     * 图片id
     */
    @TableId(value = "picture_id", type = IdType.AUTO)
    private Long pictureId;

    /**
     * 图片名称
     */
    @TableField(value = "picture_name")
    private String pictureName;

    /**
     * 图片类型
     */
    @TableField(value = "picture_type")
    private String pictureType;

    /**
     * 图片地址
     */
    @TableField(value = "download_address")
    private String downloadAddress;

    /**
     * 跳转链接
     */
    @TableField(value = "jump_link")
    private String jumpLink;

    /**
     * 排序
     */
    @TableField(value = "sort")
    private Integer sort;
}