package com.guanghonggu.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import lombok.Data;

/**
 * 
 * @TableName user_vip
 */
@TableName(value ="user_vip")
@Data
public class UserVip {
    /**
     * 
     */
    @TableId(value = "id")
    private Long id;

    /**
     * vip等级
     */
    @TableField(value = "vip_level")
    private String vipLevel;

    /**
     * 储值金额
     */
    @TableField(value = "recharge_balance")
    private BigDecimal rechargeBalance;

    /**
     * 优惠券id
     */
    @TableField(value = "coupon_id")
    private Long couponId;

    /**
     * 发放数量
     */
    @TableField(value = "issued_count")
    private Integer issuedCount;
}