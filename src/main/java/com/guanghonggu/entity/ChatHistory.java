package com.guanghonggu.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;
import java.util.List;

@TableName("chat_history")
@Data
public class ChatHistory {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("user_id")
    private Long userId;

    @TableField("client_msg")
    private String clientMsg;

    @TableField("bot_msg")
    private String botMsg;

    @TableField("created_at")
    private Date createTime;

    @TableField("updated_at")
    private Date updateTime;

}
