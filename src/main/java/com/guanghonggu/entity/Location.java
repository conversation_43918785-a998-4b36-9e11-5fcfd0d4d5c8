package com.guanghonggu.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName("location")
public class Location {
    @TableId(type = IdType.AUTO)
    private Long id;

    private Long caregiverId;
    private Double latitude;
    private Double longitude;
    private Date createTime;
}
