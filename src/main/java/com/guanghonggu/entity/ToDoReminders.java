package com.guanghonggu.entity;


import java.util.Date;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 代办提醒表
 * @TableName to_do_reminders
 */
@TableName(value ="to_do_reminders")
@Data
public class ToDoReminders {
    /**
     * 提醒id
     */
    @TableId(value = "reminders_id", type = IdType.AUTO)
    private Long remindersId;

    /**
     * 阿姨id
     */
    @TableField(value = "caregiver_id")
    private Long caregiverId;

    /**
     * 订单id
     */
    @TableField(value = "order_id")
    private Long orderId;

    /**
     * 标题
     */
    @TableField(value = "title")
    private String title;

    /**
     * 描述
     */
    @TableField(value = "description")
    private String description;

    /**
     * 提醒时间
     */
    @TableField(value = "reminder_time")
    private Date reminderTime;

    /**
     * 状态（1: 待处理, 2: 已完成）
     */
    @TableField(value = "status")
    private Integer status;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

}