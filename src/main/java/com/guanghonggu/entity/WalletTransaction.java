package com.guanghonggu.entity;



import java.math.BigDecimal;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 钱包交易记录表
 * @TableName wallet_transaction
 */
@TableName(value ="wallet_transaction")
@Data
public class WalletTransaction {
    /**
     * 交易id
     */
    @TableId(value = "transaction_id", type = IdType.AUTO)
    private Long transactionId;

    /**
     * 钱包id
     */
    @TableField(value = "wallet_id")
    private Long walletId;

    /**
     * 交易类型 (1充值、2消费、3提现、4退款、5收入)

     */
    @TableField(value = "transaction_type")
    private Integer transactionType;

    /**
     * 支付渠道 (1微信, 2支付宝，3钱包)
     */
    @TableField(value = "payment_channel")
    private Integer paymentChannel;

    /**
     * 交易金额
     */
    @TableField(value = "amount")
    private BigDecimal amount;

    /**
     * 交易时间
     */
    @TableField(value = "transaction_time")
    private Date transactionTime;

    /**
     * 状态 (0: 待处理, 1: 完成, 2: 失败)

     */
    @TableField(value = "status")
    private Integer status;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 商户订单号
     */
    @TableField(value = "out_trade_no")
    private String outTradeNo;

    /**
     * 第三方支付订单号
     */
    @TableField(value = "external_trade_no")
    private String externalTradeNo;

    /**
     * 普通余额支付金额
     */
    @TableField(value = "balance_amount")
    private BigDecimal balanceAmount;

    /**
     * 储值金额支付
     */
    @TableField(value = "recharge_amount")
    private BigDecimal rechargeAmount;

    /**
     * 第三方支付金额
     */
    @TableField(value = "external_amount")
    private BigDecimal externalAmount;
}