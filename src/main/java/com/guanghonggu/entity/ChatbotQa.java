package com.guanghonggu.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Data;

/**
 * 
 * @TableName chatbot_qa
 */
@TableName(value ="chatbot_qa")
@Data
public class ChatbotQa {
    /**
     * 
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户问题
     */
    @TableField(value = "question")
    private String question;

    /**
     * 机器人答案
     */
    @TableField(value = "answer")
    private String answer;

    /**
     * 
     */
    @TableField(value = "created_at")
    private Date createdAt;
}