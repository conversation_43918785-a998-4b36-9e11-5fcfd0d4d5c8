package com.guanghonggu.entity;


import java.util.Date;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 用户表
 * @TableName user
 */
@TableName(value ="user")
@Data
public class User {
    /**
     * 用户id
     */
    @TableId(value = "user_id", type = IdType.AUTO)
    private Long userId;

    /**
     * 手机号
     */
    @TableField(value = "phone_number")
    private String phoneNumber;

    /**
     * 微信开放id
     */
    @TableField(value = "wechat_openid")
    private String wechatOpenid;

    /**
     * 昵称
     */
    @TableField(value = "nickname")
    private String nickname;

    /**
     * 头像
     */
    @TableField(value = "avatar")
    private String avatar;

    /**
     * 账户状态 (0: 禁用, 1: 启用)
     */
    @TableField(value = "status")
    private Integer status;

    /**
     * 注册时间
     */
    @TableField(value = "registration_time", fill = FieldFill.INSERT)
    private Date registrationTime;

    /**
     * 支付密码
     */
    @TableField(value = "pay_password")
    private String payPassword;
    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 会员等级
     */
    @TableField(value = "vip_level")
    private String vipLevel;

    /**
     * 极光推送id
     */
    @TableField(value = "registration_id")
    private String registrationId;
}