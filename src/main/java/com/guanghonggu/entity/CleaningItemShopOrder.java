package com.guanghonggu.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 服务人员购买清洁工具订单表
 * @TableName cleaning_item_shop_order
 */
@TableName(value ="cleaning_item_shop_order")
@Data
public class CleaningItemShopOrder {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 订单编号
     */
    @TableField(value = "order_number")
    private String orderNumber;

    /**
     * 服务人员ID
     */
    @TableField(value = "caregiver_id")
    private Long caregiverId;

    /**
     * 总价格
     */
    @TableField(value = "total_price")
    private BigDecimal totalPrice;

    /**
     * 收货地址ID
     */
    @TableField(value = "address_id")
    private Long addressId;

    /**
     * 物流状态（0=待发货，1=已发货，2=已签收，3=已退款，4=待支付，5=已取消支付）
     */
    @TableField(value = "status")
    private Integer status;

    /**
     * 支付方式（1=微信，2=支付宝，3=钱包）
     */
    @TableField(value = "payment_method")
    private Integer paymentMethod;

    /**
     * 支付单号（微信/支付宝交易号或钱包流水号）
     */
    @TableField(value = "pay_order_number")
    private String payOrderNumber;

    /**
     * 支付时间
     */
    @TableField(value = "pay_time")
    private Date payTime;

    /**
     * 备注
     */
    @TableField(value = "remark")
    private String remark;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 是否查看
     */
    @TableField(value = "view")
    private Integer view;
}