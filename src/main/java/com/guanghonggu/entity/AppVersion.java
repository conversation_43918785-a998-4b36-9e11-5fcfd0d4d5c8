package com.guanghonggu.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Data;

/**
 * 
 * @TableName app_version
 */
@TableName(value ="app_version")
@Data
public class AppVersion {
    /**
     * 
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 平台（android/ios）
     */
    @TableField(value = "platform")
    private String platform;

    /**
     * app类型（1：用户端app；2：阿姨端app）
     */
    @TableField(value = "app_type")
    private Integer appType;

    /**
     * 版本号
     */
    @TableField(value = "version_code")
    private String versionCode;

    /**
     * 下载链接
     */
    @TableField(value = "download_url")
    private String downloadUrl;

    /**
     * 是否强制更新（0否，1是）
     */
    @TableField(value = "is_force_update")
    private Integer isForceUpdate;

    /**
     * 更新内容
     */
    @TableField(value = "update_content")
    private String updateContent;

    /**
     * 
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;
}