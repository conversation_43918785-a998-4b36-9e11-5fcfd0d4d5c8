package com.guanghonggu.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName("answer")
public class Answer {


    /**
     * 地址id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField(value = "answer")
    private String answer;



}
