package com.guanghonggu.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Data;

/**
 * 
 * @TableName chatbot_conversation
 */
@TableName(value ="chatbot_conversation")
@Data
public class ChatbotConversation {
    /**
     * 
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 
     */
    @TableField(value = "user_id")
    private Long userId;

    /**
     * 
     */
    @TableField(value = "session_id")
    private String sessionId;

    /**
     * 
     */
    @TableField(value = "question")
    private String question;

    /**
     * 
     */
    @TableField(value = "answer")
    private String answer;

    /**
     * 
     */
    @TableField(value = "create_time")
    private Date createTime;
}