package com.guanghonggu.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Data;

/**
 * 
 * @TableName order_sequence
 */
@TableName(value ="order_sequence")
@Data
public class OrderSequence {
    /**
     * 
     */
    @TableId(value = "seq_id", type = IdType.AUTO)
    private Long seqId;

    /**
     * 日期，格式yyyyMMdd
     */
    @TableField(value = "seq_date")
    private String seqDate;

    /**
     * 当天已用的最大序列号
     */
    @TableField(value = "current_seq")
    private Long currentSeq;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private Date updateTime;
}