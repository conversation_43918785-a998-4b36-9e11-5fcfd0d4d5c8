package com.guanghonggu.entity;


import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName("user_message")
public class UserMessage {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField(value = "user_id")
    private Long userId;

    @TableField(value = "message_id")
    private Long messageId;

    /**
     * 是否已读（0：未读，1：已读）
     */
    @TableField(value = "is_read")
    private Integer isRead;

    @TableField(value = "read_time")
    private Date readTime;

    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

}
