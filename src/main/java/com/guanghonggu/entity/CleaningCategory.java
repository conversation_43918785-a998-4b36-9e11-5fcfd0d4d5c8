package com.guanghonggu.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Data;

/**
 * 清洁分类表
 * @TableName cleaning_category
 */
@TableName(value ="cleaning_category")
@Data
public class CleaningCategory {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 分类名称（如“常用清洁工具与清洁剂”）
     */
    @TableField(value = "name")
    private String name;

    /**
     * 父级ID（支持多级分类）
     */
    @TableField(value = "parent_id")
    private Long parentId;

    /**
     * 分类层级（1=顶级，2=子级）
     */
    @TableField(value = "level")
    private Integer level;

    /**
     * 排序值
     */
    @TableField(value = "sort_order")
    private Integer sortOrder;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 图片URL
     */
    @TableField(value = "image_url")
    private String imageUrl;

    /**
     * 备注
     */
    @TableField(value = "remark")
    private String remark;
}