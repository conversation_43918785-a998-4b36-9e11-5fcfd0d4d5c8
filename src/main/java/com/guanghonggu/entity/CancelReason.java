package com.guanghonggu.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@TableName(value = "cancel_reason")
@Data
public class CancelReason {

    /** 取消原因字典表ID（主键） */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /** 取消原因内容 */
    @TableField(value = "reason_text")
    private String reasonText;

    /** 取消类型 1=阿姨取消，2=用户取消 */
    @TableField(value = "type")
    private Long type;

    /** 显示排序，越小越靠前 */
    @TableField(value = "sort")
    private Long sort;

    /** 创建时间 */
    @TableField(value = "create_time")
    private Date createTime;

    /** 更新时间 */
    @TableField(value = "update_time")
    private Date updateTime;


}
