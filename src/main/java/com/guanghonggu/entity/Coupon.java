package com.guanghonggu.entity;


import java.math.BigDecimal;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 优惠卷表
 * @TableName coupon
 */
@TableName(value ="coupon")
@Data
public class Coupon {
    /**
     * 优惠卷id
     */
    @TableId(value = "coupon_id", type = IdType.AUTO)
    private Long couponId;

    /**
     * 名称标题
     */
    @TableField(value = "title")
    private String title;

    /**
     * 详细描述
     */
    @TableField(value = "description")
    private String description;

    /**
     * 优惠类型（1：固定金额，2：百分比）
     */
    @TableField(value = "discount_type")
    private Integer discountType;

    /**
     * 优惠券类型（ 全部 0
     * 普通保洁	1
     * 开荒保洁	2
     * 玻璃清洗	3
     * 机器拆洗	4
     * 企业保洁	5）
     */
    @TableField(value = "coupon_type")
    private Integer couponType;

    /**
     * 优惠券类型（1：下单赠送，2：注册赠送，3：充值赠送，4：活动领取）
     */
    @TableField(value = "distribution_method")
    private Integer distributionMethod;

    /**
     * 是否可以叠加
     */
    @TableField(value = "is_superposition")
    private Integer isSuperposition;

    /**
     * 满减金额
     */
    @TableField(value = "full_amount")
    private BigDecimal fullAmount;

    /**
     * 折扣值（百分比或金额）
     */
    @TableField(value = "discount_value")
    private BigDecimal discountValue;

    /**
     * 最大折扣值
     */
    @TableField(value = "max_discount")
    private BigDecimal maxDiscount;

    /**
     * 有效天数
     */
    @TableField(value = "valid_day")
    private Integer validDay;

    /**
     * 限制数量
     */
    @TableField(value = "limit_count")
    private Integer limitCount;

    /**
     * 有效开始时间
     */
    @TableField(value = "valid_start_time")
    private Date validStartTime;

    /**
     * 有效结束时间
     */
    @TableField(value = "valid_end_time")
    private Date validEndTime;

    /**
     * 发行总数
     */
    @TableField(value = "quantity")
    private Integer quantity;

    /**
     * 剩余可用数量
     */
    @TableField(value = "remaining_quantity")
    private Integer remainingQuantity;

    /**
     * 领取开始时间
     */
    @TableField(value = "collect_start_time")
    private Date collectStartTime;

    /**
     * 领取结束时间
     */
    @TableField(value = "collect_end_time")
    private Date collectEndTime;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 是否删除（0：否，1：是）
     */
    @TableField(value = "is_delete")
    private Integer isDelete;

}