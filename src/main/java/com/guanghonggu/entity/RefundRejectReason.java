package com.guanghonggu.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.util.Date;

@Data
@TableName("refund_reject_reason")
public class RefundRejectReason {

    @TableId(type = IdType.AUTO)
    private Long id; // 主键ID

    private Long refundId; // 退款单ID
    private Long caregiverId; // 阿姨ID
    private String rejectReason; // 拒绝理由

    @TableField(fill = FieldFill.INSERT)
    private Date createTime; // 创建时间
}