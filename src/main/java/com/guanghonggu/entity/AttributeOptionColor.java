package com.guanghonggu.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 属性选项颜色配置表
 * @TableName attribute_option_color
 */
@TableName(value ="attribute_option_color")
@Data
public class AttributeOptionColor {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 属性key，如 clean_area
     */
    @TableField(value = "attribute_key")
    private String attributeKey;

    /**
     * 颜色顺序，用于排序
     */
    @TableField(value = "color_index")
    private Integer colorIndex;

    /**
     * 字体颜色，如 rgba(...)
     */
    @TableField(value = "text_color")
    private String textColor;

    /**
     * 背景颜色，如 rgba(...)
     */
    @TableField(value = "area_color")
    private String areaColor;
}