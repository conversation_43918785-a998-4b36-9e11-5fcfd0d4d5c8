package com.guanghonggu.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Date;
import lombok.Data;

/**
 * 周期订单规则表
 * @TableName order_recurrence
 */
@TableName(value ="order_recurrence")
@Data
public class OrderRecurrence {
    /**
     * 周期规则ID
     */
    @TableId(value = "recurrence_id", type = IdType.AUTO)
    private Long recurrenceId;

    /**
     * 订单ID
     */
    @TableField(value = "order_id")
    private Long orderId;

    /**
     * 服务模式（2：按周，3：按月）
     */
    @TableField(value = "service_mode")
    private Integer serviceMode;

    /**
     * 重复规则（按周:1,3,5；按月:5,15,25 或 iCal 规则）
     */
    @TableField(value = "recurrence_rule")
    private String recurrenceRule;

    /**
     * 周期次数
     */
    @TableField(value = "recurrence_count")
    private Integer recurrenceCount;

    /**
     * 已使用次数
     */
    @TableField(value = "used_count")
    private Integer usedCount;

    /**
     * 总服务次数
     */
    @TableField(value = "total_service_count")
    private Integer totalServiceCount;

    /**
     * 单次服务金额
     */
    @TableField(value = "single_service_amount")
    private BigDecimal singleServiceAmount;

    /**
     * 服务时间
     */
    @TableField(value = "service_time")
    private LocalTime serviceTime;

    /**
     * 下次服务时间
     */
    @TableField(value = "next_service_time")
    private LocalDateTime nextServiceTime;

    /**
     * 上次服务时间
     */
    @TableField(value = "last_service_time")
    private LocalDateTime lastServiceTime;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private Date updateTime;
}