package com.guanghonggu.entity;

import java.math.BigDecimal;
import java.time.LocalTime;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 阿姨表
 * @TableName caregiver
 */
@TableName(value ="caregiver")
@Data
public class Caregiver {
    /**
     * 阿姨id
     */
    @TableId(value = "caregiver_id", type = IdType.AUTO)
    private Long caregiverId;

    /**
     * 手机号
     */
    @TableField(value = "phone_number")
    private String phoneNumber;

    /**
     * 微信开放id
     */
    @TableField(value = "wechat_openid")
    private String wechatOpenid;

    /**
     * 状态 (0: 禁用, 1: 启用)
     */
    @TableField(value = "status")
    private Integer status;

    /**
     * 下班时间
     */
    @TableField(value = "stop_order_time")
    private LocalTime stopOrderTime;

    /**
     * 姓名
     */
    @TableField(value = "name")
    private String name;

    /**
     * 年龄
     */
    @TableField(value = "age")
    private Integer age;

    /**
     * 头像
     */
    @TableField(value = "avatar")
    private String avatar;

    /**
     * 性别
     */
    @TableField(value = "gender")
    private Integer gender;

    /**
     * 平均评分（来自评价表中三个字段的平均值，保存一位小数）
     */
    @TableField(value = "avg_evaluation_score")
    private Double avgEvaluationScore;
    /**
     * 身份证号码
     */
    @TableField(value = "id_card_number")
    private String idCardNumber;

    /**
     * 身份证正面照片
     */
    @TableField(value = "id_card_front")
    private String idCardFront;

    /**
     * 身份证反面照片
     */
    @TableField(value = "id_card_back")
    private String idCardBack;

    /**
     * 健康证照片
     */
    @TableField(value = "health_certificate")
    private String healthCertificate;

    /**
     * 学习/考试状态（0：未学习，1：已学习，2：未考试，3：已考试）
     */
    @TableField(value = "exam_status")
    private Integer examStatus;

    /**
     * 阿姨评分
     */
    @TableField(value = "score")
    private BigDecimal score;

    /**
     * 身份证审核（1：通过，0：未通过，2：审核中）
     */
    @TableField(value = "id_card_verification")
    private Integer idCardVerification;

    /**
     * 健康证审核（1：通过，0：未通过，2：审核中）
     */
    @TableField(value = "health_certificate_verification")
    private Integer healthCertificateVerification;

    /**
     * 注册时间
     */
    @TableField(value = "registration_time", fill = FieldFill.INSERT)
    private Date registrationTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 支付密码
     */
    @TableField(value = "pay_password")
    private String payPassword;

    /**
     * 极光推送id
     */
    @TableField(value = "registration_id")
    private String registrationId;

    /**
     * 阿姨来源
     */
    @TableField(value = "caregiver_source")
    private String caregiverSource;

}