package com.guanghonggu.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.math.BigDecimal;

import lombok.Data;

/**
 * @TableName service_attribute_option
 */
@TableName(value = "service_attribute_option")
@Data
public class ServiceAttributeOption {
    /**
     *
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 服务类型编码
     */
    @TableField(value = "service_type_code")
    private String serviceTypeCode;

    /**
     *
     */
    @TableField(value = "attribute_key")
    private String attributeKey;

    /**
     *
     */
    @TableField(value = "value")
    private String value;

    /**
     *
     */
    @TableField(value = "label")
    private String label;

    /**
     * 价格
     */
    @TableField(value = "price")
    private BigDecimal price;

    /**
     * 排序
     */
    @TableField(value = "sort")
    private Integer sort;

    /**
     * 预估时间
     */
    @TableField(value = "estimated_time")
    private BigDecimal estimatedTime;

    /**
     * 计算方式
     */
    @TableField(value = "calculation_method")
    private String calculationMethod;

    /**
     * 是否是起步价
     */
    @TableField(value = "is_starting_price")
    private Integer isStartingPrice;

}