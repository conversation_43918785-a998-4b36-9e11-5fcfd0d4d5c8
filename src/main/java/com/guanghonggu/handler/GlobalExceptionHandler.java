package com.guanghonggu.handler;

import cn.dev33.satoken.exception.NotLoginException;
import com.guanghonggu.constant.HttpStatusConstants;
import com.guanghonggu.dto.ResultDTO;
import com.guanghonggu.handler.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.BindException;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;

/**
 * @author：zzn
 * @description：统一异常处理器
 * @createData：2025/4/27 15:17
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResultDTO<String> handleValidationExceptions(MethodArgumentNotValidException ex) {
        BindingResult result = ex.getBindingResult();
        String errorMessage = result.getFieldError().getDefaultMessage();
        return ResultDTO.error(errorMessage);
    }

    @ExceptionHandler(ConstraintViolationException.class)
    public ResultDTO<String> handleConstraintViolationException(ConstraintViolationException ex) {
        StringBuilder errorMessage = new StringBuilder();
        for (ConstraintViolation<?> violation : ex.getConstraintViolations()) {
            errorMessage.append(violation.getMessage()).append("; ");
        }
        String finalErrorMessage = errorMessage.toString().trim();
        return ResultDTO.error(finalErrorMessage);
    }

    // 处理表单（@ModelAttribute）绑定失败
    @ExceptionHandler(BindException.class)
    public ResultDTO<String> handleBindException(BindException ex) {
        FieldError fieldError = ex.getBindingResult().getFieldError();
        String message = (fieldError != null) ? fieldError.getDefaultMessage() : "参数绑定失败";
        return ResultDTO.error(message);
    }

    /**
     * 登录过期异常
     */
    @ExceptionHandler(NotLoginException.class)
    public ResultDTO<String> handlerException(NotLoginException e) {
        // 打印堆栈，以供调试
        log.error("token异常 - 未登录");
        String message = "账号信息过期，请重新登陆！";
        int code = e.getCode();
        if (code == 11014) {
            message = "账号已在其他地方登录，请重新登录";
        }
        // 返回给前端
        return ResultDTO.error(HttpStatusConstants.UNAUTHORIZED, message);
    }

    /**
     * 处理业务异常
     */
    @ExceptionHandler(BizException.class)
    public ResultDTO<?> handleBizException(BizException e) {
        log.warn("【业务异常】: {}", e.getMessage());
        return ResultDTO.error(e.getMessage());
    }

    /**
     * 处理所有其他异常
     */
    @ExceptionHandler(Exception.class)
    public ResultDTO<?> handleException(Exception e) {
        log.error("【系统异常】: {}", e.getMessage(), e);
        return ResultDTO.error("系统异常，请稍后再试");
    }
}
